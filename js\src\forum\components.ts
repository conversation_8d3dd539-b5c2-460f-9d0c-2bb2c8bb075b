import { ContainerConfig } from './types';

export class HeaderComponents {
    static createContainer(config: ContainerConfig): HTMLDivElement {
        const container = document.createElement("div");
        container.className = config.className;
        
        if (config.id) {
            container.id = config.id;
        }
        
        if (config.height) {
            container.style.height = typeof config.height === 'number' 
                ? `${config.height}px` 
                : config.height;
        }
        
        return container;
    }

    static createSwiperContainer(className: string): HTMLDivElement {
        const swiper = document.createElement("div");
        swiper.className = className;
        return swiper;
    }

    static createSwiperWrapper(id?: string): HTMLDivElement {
        const wrapper = document.createElement("div");
        wrapper.className = "swiper-wrapper";
        if (id) {
            wrapper.id = id;
        }
        return wrapper;
    }

    static createSwiperSlide(className: string, innerHTML: string): HTMLDivElement {
        const slide = document.createElement("div");
        slide.className = className;
        slide.innerHTML = innerHTML;
        return slide;
    }

    static createSwiperNavigation(className: string): HTMLDivElement {
        const navigation = document.createElement("div");
        navigation.className = className;
        return navigation;
    }
}