{"version": 3, "sources": ["webpack://@justoverclock/header-slideshow/webpack/bootstrap", "webpack://@justoverclock/header-slideshow/external \"flarum.core.compat['app']\"", "webpack://@justoverclock/header-slideshow/external \"flarum.core.compat['extend']\"", "webpack://@justoverclock/header-slideshow/external \"flarum.core.compat['forum/components/HeaderPrimary']\"", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/createClass.js", "webpack://@justoverclock/header-slideshow/./node_modules/ssr-window/ssr-window.esm.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/construct.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/isNativeFunction.js", "webpack://@justoverclock/header-slideshow/./node_modules/dom7/dom7.esm.js", "webpack://@justoverclock/header-slideshow/./node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/dom.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/get-support.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/get-device.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/get-browser.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/utils.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/transition/transitionEmit.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/onTouchStart.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/onTouchMove.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/onTouchEnd.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/onResize.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/onClick.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/onScroll.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/breakpoints/setBreakpoint.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/check-overflow/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/defaults.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/moduleExtendParams.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/core.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/events-emitter.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateSize.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateSlides.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateAutoHeight.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateSlidesOffset.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateSlidesProgress.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateProgress.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateSlidesClasses.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateActiveIndex.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/update/updateClickedSlide.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/translate/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/translate/getTranslate.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/translate/setTranslate.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/translate/minTranslate.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/translate/maxTranslate.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/translate/translateTo.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/transition/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/transition/setTransition.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/transition/transitionStart.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/transition/transitionEnd.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slideTo.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slideToLoop.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slideNext.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slidePrev.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slideReset.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slideToClosest.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/slide/slideToClickedSlide.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/loop/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/loop/loopCreate.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/loop/loopFix.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/loop/loopDestroy.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/grab-cursor/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/grab-cursor/setGrabCursor.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/grab-cursor/unsetGrabCursor.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/breakpoints/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/breakpoints/getBreakpoint.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/classes/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/classes/addClasses.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/classes/removeClasses.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/images/index.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/images/loadImage.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/images/preloadImages.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/modules/resize/resize.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/core/modules/observer/observer.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/create-element-if-not-defined.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/modules/navigation/navigation.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/classes-to-selector.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/modules/pagination/pagination.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/modules/autoplay/autoplay.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/effect-init.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/effect-target.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/shared/create-shadow.js", "webpack://@justoverclock/header-slideshow/./node_modules/swiper/modules/effect-coverflow/effect-coverflow.js", "webpack://@justoverclock/header-slideshow/./src/forum/index.ts"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "flarum", "core", "compat", "_defineProperties", "target", "props", "length", "descriptor", "configurable", "writable", "isObject", "obj", "constructor", "extend", "src", "keys", "for<PERSON>ach", "ssrDocument", "body", "addEventListener", "removeEventListener", "activeElement", "blur", "nodeName", "querySelector", "querySelectorAll", "getElementById", "createEvent", "initEvent", "createElement", "children", "childNodes", "style", "setAttribute", "getElementsByTagName", "createElementNS", "importNode", "location", "hash", "host", "hostname", "href", "origin", "pathname", "protocol", "search", "getDocument", "doc", "document", "ssrWindow", "navigator", "userAgent", "history", "replaceState", "pushState", "go", "back", "CustomEvent", "this", "getComputedStyle", "getPropertyValue", "Image", "Date", "screen", "setTimeout", "clearTimeout", "matchMedia", "requestAnimationFrame", "callback", "cancelAnimationFrame", "id", "getWindow", "win", "window", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "setPrototypeOf", "__proto__", "_getPrototypeOf", "getPrototypeOf", "_isNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "e", "_construct", "Parent", "args", "Class", "isNativeReflectConstruct", "a", "push", "apply", "instance", "Function", "arguments", "_wrapNativeSuper", "_cache", "Map", "undefined", "fn", "toString", "indexOf", "TypeError", "has", "set", "Wrapper", "Dom7", "subClass", "superClass", "items", "proto", "Array", "arrayFlat", "arr", "res", "el", "isArray", "arrayFilter", "filter", "$", "selector", "context", "html", "trim", "toCreate", "tempParent", "innerHTML", "qsa", "nodeType", "uniqueArray", "arrayUnique", "noTrigger", "split", "shortcut", "trigger", "on", "Methods", "addClass", "classes", "classNames", "map", "classList", "add", "removeClass", "remove", "hasClass", "className", "contains", "toggleClass", "toggle", "attr", "attrs", "getAttribute", "attrName", "removeAttr", "removeAttribute", "transform", "transition", "duration", "transitionDuration", "eventType", "targetSelector", "listener", "capture", "handleLiveEvent", "eventData", "dom7EventData", "unshift", "is", "parents", "k", "handleEvent", "j", "events", "event", "dom7LiveListeners", "proxyListener", "dom7Listeners", "off", "handlers", "handler", "dom7proxy", "splice", "evt", "detail", "bubbles", "cancelable", "data", "dataIndex", "dispatchEvent", "transitionEnd", "dom", "fireCallBack", "outerWidth", "<PERSON><PERSON><PERSON><PERSON>", "styles", "offsetWidth", "parseFloat", "outerHeight", "offsetHeight", "offset", "box", "getBoundingClientRect", "clientTop", "clientLeft", "scrollTop", "scrollY", "scrollLeft", "scrollX", "top", "left", "css", "prop", "each", "index", "text", "textContent", "compareWith", "matches", "webkitMatchesSelector", "msMatchesSelector", "child", "previousSibling", "eq", "returnIndex", "append", "<PERSON><PERSON><PERSON><PERSON>", "tempDiv", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "prepend", "insertBefore", "next", "nextElement<PERSON><PERSON>ling", "nextAll", "nextEls", "prev", "previousElementSibling", "prevAll", "prevEls", "parent", "parentNode", "closest", "find", "foundElements", "found", "<PERSON><PERSON><PERSON><PERSON>", "methodName", "support", "deviceCached", "browser", "nextTick", "delay", "now", "getTranslate", "axis", "matrix", "curTransform", "transformMatrix", "curStyle", "currentStyle", "WebKitCSSMatrix", "webkitTransform", "replace", "join", "MozTransform", "OTransform", "MsTransform", "msTransform", "m41", "m42", "slice", "isNode", "node", "HTMLElement", "to", "noExtend", "nextSource", "keysArray", "nextIndex", "len", "<PERSON><PERSON><PERSON>", "desc", "getOwnPropertyDescriptor", "__swiper__", "setCSSProperty", "varName", "varValue", "setProperty", "animateCSSModeScroll", "time", "swiper", "targetPosition", "side", "startPosition", "translate", "startTime", "params", "speed", "wrapperEl", "scrollSnapType", "cssModeFrameID", "dir", "isOutOfBound", "current", "animate", "getTime", "progress", "Math", "max", "min", "easeProgress", "cos", "PI", "currentPosition", "scrollTo", "overflow", "getSupport", "smoothScroll", "documentElement", "touch", "DocumentTouch", "passiveListener", "supportsPassive", "opts", "gestures", "calcSupport", "getDevice", "overrides", "platform", "ua", "device", "ios", "android", "screenWidth", "width", "screenHeight", "height", "match", "ipad", "ipod", "iphone", "windows", "macos", "os", "calcDevice", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "isWebView", "test", "calcB<PERSON>er", "transitionEmit", "runCallbacks", "direction", "step", "activeIndex", "previousIndex", "emit", "onTouchStart", "touchEventsData", "touches", "enabled", "animating", "preventInteractionOnTransition", "cssMode", "loop", "loopFix", "originalEvent", "$targetEl", "touchEventsTarget", "isTouchEvent", "type", "which", "button", "isTouched", "isMoved", "swipingClassHasValue", "noSwipingClass", "eventPath", "<PERSON><PERSON><PERSON>", "path", "shadowRoot", "noSwipingSelector", "isTargetShadow", "noSwiping", "base", "__closestFrom", "assignedSlot", "getRootNode", "closestElement", "allowClick", "swi<PERSON><PERSON><PERSON><PERSON>", "currentX", "targetTouches", "pageX", "currentY", "pageY", "startX", "startY", "edgeSwipeDetection", "iOSEdgeSwipeDetection", "edgeSwipeThreshold", "iOSEdgeSwipeThreshold", "innerWidth", "preventDefault", "assign", "allowTouchCallbacks", "isScrolling", "startMoving", "touchStartTime", "updateSize", "swipeDirection", "threshold", "allowThresholdMove", "focusableElements", "shouldPreventDefault", "allowTouchMove", "touchStartPreventDefault", "touchStartForcePreventDefault", "isContentEditable", "freeMode", "onTouchMove", "rtl", "rtlTranslate", "targetTouch", "changedTouches", "preventedByNestedSwiper", "touchReleaseOnEdges", "isVertical", "maxTranslate", "minTranslate", "diffX", "diffY", "sqrt", "touchAngle", "isHorizontal", "atan2", "abs", "touchMoveStopPropagation", "nested", "stopPropagation", "startTranslate", "setTransition", "$wrapperEl", "allowMomentumBounce", "grabCursor", "allowSlideNext", "allowSlidePrev", "setGrabCursor", "diff", "touchRatio", "currentTranslate", "disableParentSwiper", "resistanceRatio", "resistance", "follow<PERSON><PERSON>", "watchSlidesProgress", "updateActiveIndex", "updateSlidesClasses", "updateProgress", "setTranslate", "onTouchEnd", "slidesGrid", "currentPos", "touchEndTime", "timeDiff", "pathTree", "updateClickedSlide", "lastClickTime", "destroyed", "stopIndex", "groupSize", "slidesSizesGrid", "slidesPerGroupSkip", "slidesPerGroup", "increment", "rewindFirstIndex", "rewindLastIndex", "rewind", "isBeginning", "virtual", "slides", "isEnd", "ratio", "longSwipesMs", "longSwipes", "slideTo", "longSwipesRatio", "shortSwipes", "navigation", "nextEl", "prevEl", "onResize", "breakpoints", "setBreakpoint", "snapGrid", "updateSlides", "<PERSON><PERSON><PERSON><PERSON>iew", "centeredSlides", "autoplay", "running", "paused", "run", "watchOverflow", "checkOverflow", "onClick", "preventClicks", "preventClicksPropagation", "stopImmediatePropagation", "onScroll", "previousTranslate", "translatesDiff", "dummy<PERSON><PERSON><PERSON>ttached", "dummyEventListener", "method", "touchEvents", "dom<PERSON>ethod", "swiperMethod", "start", "passiveListeners", "passive", "move", "end", "cancel", "updateOnWindowResize", "isGridEnabled", "grid", "rows", "init", "initialSlide", "resizeObserver", "createElements", "url", "autoHeight", "setWrapperSize", "virtualTranslate", "effect", "breakpointsBase", "spaceBetween", "slidesPerGroupAuto", "centeredSlidesBounds", "slidesOffsetBefore", "slidesOffsetAfter", "normalizeSlideIndex", "centerInsufficientSlides", "roundLengths", "simulate<PERSON>ouch", "uniqueNavElements", "slideToClickedSlide", "preloadImages", "updateOnImagesReady", "loopAdditionalSlides", "loopedSlides", "loopedSlidesLimit", "loopFillGroupWithBlank", "loopPreventsSlide", "maxBackfaceHiddenSlides", "containerModifierClass", "slideClass", "slideBlankClass", "slideActiveClass", "slideDuplicateActiveClass", "slideVisibleClass", "slideDuplicateClass", "slideNextClass", "slideDuplicateNextClass", "slidePrevClass", "slideDuplicatePrevClass", "wrapperClass", "runCallbacksOnInit", "_emitClasses", "moduleExtendParams", "allModulesParams", "moduleParamName", "moduleParams", "auto", "prototypes", "eventsEmitter", "priority", "eventsListeners", "once", "once<PERSON><PERSON><PERSON>", "__emitterProxy", "onAny", "eventsAnyListeners", "offAny", "<PERSON><PERSON><PERSON><PERSON>", "eventsArray", "update", "$el", "clientWidth", "clientHeight", "parseInt", "Number", "isNaN", "size", "getDirectionLabel", "getDirectionPropertyValue", "label", "swiperSize", "wrongRTL", "isVirtual", "previousSlidesLength", "<PERSON><PERSON><PERSON><PERSON>", "offsetBefore", "offsetAfter", "previousSnapGridLength", "previousSlidesGridLength", "slidePosition", "prevSlideSize", "virtualSize", "marginLeft", "marginBottom", "marginTop", "marginRight", "slideSize", "gridEnabled", "initSlides", "shouldResetSlideSize", "slide", "updateSlide", "slideStyles", "currentTransform", "currentWebKitTransform", "paddingLeft", "paddingRight", "boxSizing", "floor", "swiperSlideSize", "updateWrapperSize", "newSlidesGrid", "slidesGridItem", "_", "slideIndex", "allSlidesSize", "slideSizeValue", "maxSnap", "snap", "allSlidesOffset", "snapIndex", "addToSnapGrid", "addToSlidesGrid", "v", "updateSlidesOffset", "backFaceHiddenClass", "hasClassBackfaceClassAdded", "updateAutoHeight", "activeSlides", "newHeight", "getSlideByIndex", "visibleSlides", "ceil", "swiperSlideOffset", "offsetLeft", "offsetTop", "updateSlidesProgress", "offsetCenter", "visibleSlidesIndexes", "slideOffset", "slideProgress", "originalSlideProgress", "slideBefore", "slideAfter", "originalProgress", "multiplier", "wasBeginning", "wasEnd", "activeSlide", "realIndex", "nextSlide", "prevSlide", "emitSlidesClasses", "newActiveIndex", "previousRealIndex", "previousSnapIndex", "skip", "initialized", "slideFound", "clickedSlide", "clickedIndex", "byController", "x", "y", "translateTo", "translateBounds", "internal", "newTranslate", "isH", "behavior", "onTranslateToWrapperTransitionEnd", "transitionStart", "initial", "Error", "indexAsNumber", "isFinite", "normalizedTranslate", "normalizedGrid", "normalizedGridNext", "_immediateVirtual", "_swiperImmediateVirtual", "onSlideToWrapperTransitionEnd", "slideToLoop", "newIndex", "slideNext", "perGroup", "slidesPerViewDynamic", "_clientLeft", "slidePrev", "normalize", "val", "prevSnapIndex", "normalizedSnapGrid", "prevSnap", "prevIndex", "lastIndex", "slideReset", "slideToClosest", "currentSnap", "slideToIndex", "loopCreate", "$selector", "blankSlidesNum", "blankNode", "prependSlides", "appendSlides", "cloneNode", "loop<PERSON><PERSON><PERSON>", "moving", "isLocked", "cursor", "unsetGrabCursor", "attachEvents", "detachEvents", "breakpoint", "getBreakpoint", "currentBreakpoint", "breakpointP<PERSON>ms", "originalParams", "wasMultiRow", "isMultiRow", "wasEnabled", "emitContainerClasses", "fill", "wasModuleEnabled", "isModuleEnabled", "disable", "enable", "directionChanged", "needsReLoop", "changeDirection", "isEnabled", "containerEl", "currentHeight", "innerHeight", "points", "point", "minRatio", "substr", "sort", "b", "wasLocked", "lastSlideIndex", "lastSlideRightEdge", "addClasses", "entries", "prefix", "resultClasses", "suffixes", "item", "removeClasses", "images", "loadImage", "imageEl", "srcset", "sizes", "checkForComplete", "image", "onReady", "complete", "onload", "onerror", "imagesLoaded", "imagesToLoad", "currentSrc", "extendedDefaults", "Swiper", "swipers", "newParams", "__modules__", "mod", "extendParams", "desktop", "swiperParams", "defaults", "passedParams", "eventName", "velocity", "touchEventsTouch", "touchEventsDesktop", "clickTimeout", "velocities", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "setProgress", "cls", "getSlideClasses", "slideEl", "updates", "view", "exact", "spv", "breakLoop", "translateValue", "newDirection", "needUpdate", "currentDirection", "changeLanguageDirection", "mount", "mounted", "getWrapperSelector", "options", "getWrapper", "wrapper", "destroy", "deleteInstance", "cleanStyles", "extendDefaults", "newDefaults", "installModule", "use", "prototypeGroup", "protoMethod", "observer", "animationFrame", "resize<PERSON><PERSON>ler", "orientationChangeHandler", "ResizeObserver", "newWidth", "contentBoxSize", "contentRect", "inlineSize", "blockSize", "observe", "unobserve", "observers", "attach", "MutationObserver", "WebkitMutationObserver", "mutations", "observerUpdate", "attributes", "childList", "characterData", "observeParents", "observeSlideChildren", "containerParents", "disconnect", "createElementIfNotDefined", "checkProps", "element", "Navigation", "getEl", "toggleEl", "disabled", "disabledClass", "tagName", "lockClass", "$nextEl", "$prevEl", "onPrevClick", "onNextClick", "hideOnClick", "hiddenClass", "navigationDisabledClass", "_s", "targetEl", "pagination", "clickable", "isHidden", "classesToSelector", "Pagination", "bulletSize", "pfx", "bulletElement", "renderBullet", "renderProgressbar", "renderFraction", "renderCustom", "progressbarOpposite", "dynamicBullets", "dynamicMainBullets", "formatFractionCurrent", "number", "formatFractionTotal", "bulletClass", "bulletActiveClass", "modifierClass", "currentClass", "totalClass", "progressbarFillClass", "progressbarOppositeClass", "clickableClass", "horizontalClass", "verticalClass", "paginationDisabledClass", "bullets", "dynamicBulletIndex", "isPaginationDisabled", "setSideBullets", "$bulletEl", "position", "total", "paginationType", "firstIndex", "midIndex", "suffix", "bullet", "$bullet", "bulletIndex", "$firstDisplayedBullet", "$lastDisplayedBullet", "dynamicBulletsLength", "bulletsOffset", "offsetProp", "progressbarDirection", "scale", "scaleX", "scaleY", "render", "paginationHTML", "numberOfBullets", "Autoplay", "timeout", "$activeSlideEl", "autoplayResult", "reverseDirection", "stopOnLastSlide", "stop", "pause", "waitForTransition", "onTransitionEnd", "onVisibilityChange", "visibilityState", "onMouseEnter", "disableOnInteraction", "onMouseLeave", "pauseOnMouseEnter", "effectInit", "requireUpdateOnVirtual", "overwriteParams", "perspective", "recreateShadows", "getEffectParams", "overwriteParamsResult", "slideShadows", "effect<PERSON>arget", "effectParams", "$slideEl", "transformEl", "createShadow", "shadowClass", "$shadowContainer", "$shadowEl", "EffectCoverflow", "coverflowEffect", "rotate", "stretch", "depth", "modifier", "swiper<PERSON><PERSON><PERSON>", "swiperHeight", "center", "centerOffset", "offsetMultiplier", "rotateY", "rotateX", "translateZ", "translateY", "translateX", "slideTransform", "zIndex", "round", "$shadowBeforeEl", "$shadowAfterEl", "opacity", "check", "tronscanListLoading", "tronscanList", "linksQueueListLoading", "linksQueueList", "links<PERSON><PERSON>uePointer", "buttonsCustomizationListLoading", "buttonsCustomizationList", "isMobile<PERSON>iew", "vendor", "opera", "parseTronscanResults", "results", "parseButtonsCustomizationResults", "parseLinksQueueResults", "app", "initializers", "HeaderPrimary", "vnode", "routeName", "vdom", "task", "setInterval", "clearInterval", "TransitionTime", "forum", "attribute", "styleWidth", "swiper<PERSON><PERSON><PERSON>", "swiper_wrapper", "swiper_slide", "imageSrc", "imageLink", "swiper_button_next", "swiper_button_prev", "swiper_pagination", "store", "then", "loadLinksQueueList", "loadTronscanList", "loadButtonsCustomizationList", "checkDataTask", "headerIconContainer", "tagTile", "TagTextOuterContainer", "tag", "tagURL", "tagBackground", "tagNameColor", "TronscanTextContainer", "tronscanData", "tronscanValueUsd", "valueUsd", "tronscanBackground", "img", "addTronscan", "changeCategoryLayout", "zhiboContainer", "zhiboIframe", "linksQueueURL", "addZhiBoContainer", "youxiC<PERSON><PERSON>", "innerText", "translator", "trans", "addYouXiContainer", "buttonCustomizationContainer", "addButtonCustomizationContainer", "shangchengContainer", "addShangChengContainer", "selectTitleContainer", "buttonsCustomization", "buttonsCustomizationMap", "totalButtons", "buttonsCustomizationData", "buttonsCustomizationName", "buttonsCustomizationIcon", "icon", "buttonsCustomizationURL", "color", "selectTitle", "leftValuePrev", "leftValueMap", "leftModifier", "leftValue", "iframeHeight", "customButtonData", "paddingBottom", "scrolling", "containerHeight", "customButtonIframe", "addButtons", "session", "user", "display", "attachAdvertiseHeader"], "mappings": "2BACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QA0Df,OArDAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,gBClFrDhC,EAAOD,QAAUkC,OAAOC,KAAKC,OAAY,K,cCAzCnC,EAAOD,QAAUkC,OAAOC,KAAKC,OAAe,Q,cCA5CnC,EAAOD,QAAUkC,OAAOC,KAAKC,OAAO,mC,+ECApC,SAASC,EAAkBC,EAAQC,GACjC,IAAK,IAAIrC,EAAI,EAAGA,EAAIqC,EAAMC,OAAQtC,IAAK,CACrC,IAAIuC,EAAaF,EAAMrC,GACvBuC,EAAW3B,WAAa2B,EAAW3B,aAAc,EACjD2B,EAAWC,cAAe,EACtB,UAAWD,IAAYA,EAAWE,UAAW,GACjD/B,OAAOC,eAAeyB,EAAQG,EAAWhB,IAAKgB,ICMlD,SAASG,EAASC,GACd,OAAgB,OAARA,GACW,iBAARA,GACP,gBAAiBA,GACjBA,EAAIC,cAAgBlC,OAE5B,SAASmC,EAAOT,EAAaU,QAAU,IAAvBV,MAAS,SAAc,IAAVU,MAAM,IAC/BpC,OAAOqC,KAAKD,GAAKE,SAAQ,SAACzB,QACK,IAAhBa,EAAOb,GACda,EAAOb,GAAOuB,EAAIvB,GACbmB,EAASI,EAAIvB,KAClBmB,EAASN,EAAOb,KAChBb,OAAOqC,KAAKD,EAAIvB,IAAMe,OAAS,GAC/BO,EAAOT,EAAOb,GAAMuB,EAAIvB,OAKpC,IAAM0B,EAAc,CAChBC,KAAM,GACNC,iBAFgB,aAGhBC,oBAHgB,aAIhBC,cAAe,CACXC,KADW,aAEXC,SAAU,IAEdC,cARgB,WASZ,OAAO,MAEXC,iBAXgB,WAYZ,MAAO,IAEXC,eAdgB,WAeZ,OAAO,MAEXC,YAjBgB,WAkBZ,MAAO,CACHC,UADG,eAIXC,cAtBgB,WAuBZ,MAAO,CACHC,SAAU,GACVC,WAAY,GACZC,MAAO,GACPC,aAJG,aAKHC,qBALG,WAMC,MAAO,MAInBC,gBAjCgB,WAkCZ,MAAO,IAEXC,WApCgB,WAqCZ,OAAO,MAEXC,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,KAGhB,SAASC,IACL,IAAMC,EAA0B,oBAAbC,SAA2BA,SAAW,GAEzD,OADAnC,EAAOkC,EAAK9B,GACL8B,EAGX,IAAME,EAAY,CACdD,SAAU/B,EACViC,UAAW,CACPC,UAAW,IAEfd,SAAU,CACNC,KAAM,GACNC,KAAM,GACNC,SAAU,GACVC,KAAM,GACNC,OAAQ,GACRC,SAAU,GACVC,SAAU,GACVC,OAAQ,IAEZO,QAAS,CACLC,aADK,aAELC,UAFK,aAGLC,GAHK,aAILC,KAJK,cAMTC,YAAa,WACT,OAAOC,MAEXvC,iBAxBc,aAyBdC,oBAzBc,aA0BduC,iBA1Bc,WA2BV,MAAO,CACHC,iBADG,WAEC,MAAO,MAInBC,MAjCc,aAkCdC,KAlCc,aAmCdC,OAAQ,GACRC,WApCc,aAqCdC,aArCc,aAsCdC,WAtCc,WAuCV,MAAO,IAEXC,sBAzCc,SAyCQC,GAClB,MAA0B,oBAAfJ,YACPI,IACO,MAEJJ,WAAWI,EAAU,IAEhCC,qBAhDc,SAgDOC,GACS,oBAAfN,YAGXC,aAAaK,KAGrB,SAASC,IACL,IAAMC,EAAwB,oBAAXC,OAAyBA,OAAS,GAErD,OADA5D,EAAO2D,EAAKvB,GACLuB,EChJI,SAASE,EAAuBC,GAC7C,QAAa,IAATA,EACF,MAAM,IAAIC,eAAe,6DAG3B,OAAOD,ECLM,SAASE,EAAgBpG,EAAGqB,GAMzC,OALA+E,EAAkBnG,OAAOoG,gBAAkB,SAAyBrG,EAAGqB,GAErE,OADArB,EAAEsG,UAAYjF,EACPrB,IAGcA,EAAGqB,GCNb,SAASkF,EAAgBvG,GAItC,OAHAuG,EAAkBtG,OAAOoG,eAAiBpG,OAAOuG,eAAiB,SAAyBxG,GACzF,OAAOA,EAAEsG,WAAarG,OAAOuG,eAAexG,KAEvBA,GCJV,SAASyG,IACtB,GAAuB,oBAAZC,UAA4BA,QAAQC,UAAW,OAAO,EACjE,GAAID,QAAQC,UAAUC,KAAM,OAAO,EACnC,GAAqB,mBAAVC,MAAsB,OAAO,EAExC,IAEE,OADAC,QAAQ3F,UAAU4F,QAAQrH,KAAKgH,QAAQC,UAAUG,QAAS,IAAI,iBACvD,EACP,MAAOE,GACP,OAAO,GCPI,SAASC,EAAWC,EAAQC,EAAMC,GAc/C,OAZEH,EADEI,IACWX,QAAQC,UAER,SAAoBO,EAAQC,EAAMC,GAC7C,IAAIE,EAAI,CAAC,MACTA,EAAEC,KAAKC,MAAMF,EAAGH,GAChB,IACIM,EAAW,IADGC,SAAS3G,KAAKyG,MAAMN,EAAQI,IAG9C,OADIF,GAAOf,EAAeoB,EAAUL,EAAMjG,WACnCsG,IAIOD,MAAM,KAAMG,WCZjB,SAASC,EAAiBR,GACvC,IAAIS,EAAwB,mBAARC,IAAqB,IAAIA,SAAQC,EA8BrD,OA5BAH,EAAmB,SAA0BR,GAC3C,GAAc,OAAVA,ICRkCY,EDQEZ,GCPsB,IAAzDM,SAASO,SAASvI,KAAKsI,GAAIE,QAAQ,kBDOQ,OAAOd,ECR5C,IAA2BY,EDUtC,GAAqB,mBAAVZ,EACT,MAAM,IAAIe,UAAU,sDAGtB,QAAsB,IAAXN,EAAwB,CACjC,GAAIA,EAAOO,IAAIhB,GAAQ,OAAOS,EAAOzH,IAAIgH,GAEzCS,EAAOQ,IAAIjB,EAAOkB,GAGpB,SAASA,IACP,OAAO3B,EAAUS,EAAOO,UAAWnB,EAAevB,MAAM9C,aAW1D,OARAmG,EAAQnH,UAAYlB,OAAOY,OAAOuG,EAAMjG,UAAW,CACjDgB,YAAa,CACX3B,MAAO8H,EACPnI,YAAY,EACZ6B,UAAU,EACVD,cAAc,KAGXsE,EAAeiC,EAASlB,KAGTA,G,IEPpBmB,E,YC3BS,IAAwBC,EAAUC,ED4B/C,WAAYC,GAAO,MAfCxG,EACdyG,EAca,MACI,iBAAVD,EACT,cAAMA,IAAN,MAEA,+BAAUA,GAAS,MAAnB,KAnBgBxG,EAoBH,KAnBXyG,EAAQzG,EAAIoE,UAClBrG,OAAOC,eAAegC,EAAK,YAAa,CACtC9B,IADsC,WAEpC,OAAOuI,GAGTN,IALsC,SAKlC7H,GACFmI,EAAMrC,UAAY9F,MAOH,K,OC5B4BiI,E,GAAVD,E,GAC5BrH,UAAYlB,OAAOY,OAAO4H,EAAWtH,WAC9CqH,EAASrH,UAAUgB,YAAcqG,EACjCnC,EAAemC,EAAUC,G,KDwBRG,QAYnB,SAASC,EAAUC,QAAU,IAAVA,MAAM,IACvB,IAAMC,EAAM,GAQZ,OAPAD,EAAIvG,SAAQ,SAAAyG,GACNJ,MAAMK,QAAQD,GAChBD,EAAIxB,KAAJ,MAAAwB,EAAYF,EAAUG,IAEtBD,EAAIxB,KAAKyB,MAGND,EAET,SAASG,EAAYJ,EAAKnD,GACxB,OAAOiD,MAAMzH,UAAUgI,OAAOzJ,KAAKoJ,EAAKnD,GAgC1C,SAASyD,EAAEC,EAAUC,GACnB,IAAMtD,EAASF,IACTvB,EAAWF,IACbyE,EAAM,GAEV,IAAKQ,GAAWD,aAAoBd,EAClC,OAAOc,EAGT,IAAKA,EACH,OAAO,IAAId,EAAKO,GAGlB,GAAwB,iBAAbO,EAAuB,CAChC,IAAME,EAAOF,EAASG,OAEtB,GAAID,EAAKrB,QAAQ,MAAQ,GAAKqB,EAAKrB,QAAQ,MAAQ,EAAG,CACpD,IAAIuB,EAAW,MACa,IAAxBF,EAAKrB,QAAQ,SAAcuB,EAAW,MACd,IAAxBF,EAAKrB,QAAQ,SAAcuB,EAAW,SACd,IAAxBF,EAAKrB,QAAQ,QAAwC,IAAxBqB,EAAKrB,QAAQ,SAAcuB,EAAW,MACxC,IAA3BF,EAAKrB,QAAQ,YAAiBuB,EAAW,SACb,IAA5BF,EAAKrB,QAAQ,aAAkBuB,EAAW,UAC9C,IAAMC,EAAanF,EAASnB,cAAcqG,GAC1CC,EAAWC,UAAYJ,EAEvB,IAAK,IAAIhK,EAAI,EAAGA,EAAImK,EAAWpG,WAAWzB,OAAQtC,GAAK,EACrDuJ,EAAIvB,KAAKmC,EAAWpG,WAAW/D,SAGjCuJ,EA7CN,SAAaO,EAAUC,GACrB,GAAwB,iBAAbD,EACT,MAAO,CAACA,GAMV,IAHA,IAAM/B,EAAI,GACJyB,EAAMO,EAAQtG,iBAAiBqG,GAE5B9J,EAAI,EAAGA,EAAIwJ,EAAIlH,OAAQtC,GAAK,EACnC+H,EAAEC,KAAKwB,EAAIxJ,IAGb,OAAO+H,EAiCGsC,CAAIP,EAASG,OAAQF,GAAW/E,QAGnC,GAAI8E,EAASQ,UAAYR,IAAarD,GAAUqD,IAAa9E,EAClEuE,EAAIvB,KAAK8B,QACJ,GAAIT,MAAMK,QAAQI,GAAW,CAClC,GAAIA,aAAoBd,EAAM,OAAOc,EACrCP,EAAMO,EAGR,OAAO,IAAId,EAtEb,SAAqBO,GAGnB,IAFA,IAAMgB,EAAc,GAEXvK,EAAI,EAAGA,EAAIuJ,EAAIjH,OAAQtC,GAAK,GACE,IAAjCuK,EAAY5B,QAAQY,EAAIvJ,KAAYuK,EAAYvC,KAAKuB,EAAIvJ,IAG/D,OAAOuK,EA+DSC,CAAYjB,IAG9BM,EAAEpB,GAAKO,EAAKpH,UA4xCZ,IAAM6I,EAAY,gBAAgBC,MAAM,KAExC,SAASC,EAASpK,GAiBhB,OAhBA,WAA+B,2BAANqH,EAAM,yBAANA,EAAM,gBAC7B,QAAuB,IAAZA,EAAK,GAAoB,CAClC,IAAK,IAAI5H,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAChCyK,EAAU9B,QAAQpI,GAAQ,IACxBA,KAAQmF,KAAK1F,GAAI0F,KAAK1F,GAAGO,KAC3BsJ,EAAEnE,KAAK1F,IAAI4K,QAAQrK,IAKzB,OAAOmF,KAGT,OAAOA,KAAKmF,GAAL,MAAAnF,KAAA,CAAQnF,GAAR,OAAiBqH,KAMd+C,EAAS,SACVA,EAAS,QACRA,EAAS,SACPA,EAAS,WACRA,EAAS,YACZA,EAAS,SACPA,EAAS,WACRA,EAAS,YACXA,EAAS,UACTA,EAAS,UACNA,EAAS,aACTA,EAAS,aACXA,EAAS,WACNA,EAAS,cACTA,EAAS,cACXA,EAAS,YACRA,EAAS,aACRA,EAAS,cACXA,EAAS,YACRA,EAAS,aACZA,EAAS,UACTA,EAAS,UArBxB,IEh7CMG,EAAU,CACdC,SFiIF,WAA8B,2BAATC,EAAS,yBAATA,EAAS,gBAC5B,IAAMC,EAAa3B,EAAU0B,EAAQE,KAAI,SAAA7K,GAAC,OAAIA,EAAEqK,MAAM,SAItD,OAHAhF,KAAK1C,SAAQ,SAAAyG,GAAM,OACjB,EAAAA,EAAG0B,WAAUC,IAAb,QAAoBH,MAEfvF,MErIP2F,YFwIF,WAAiC,2BAATL,EAAS,yBAATA,EAAS,gBAC/B,IAAMC,EAAa3B,EAAU0B,EAAQE,KAAI,SAAA7K,GAAC,OAAIA,EAAEqK,MAAM,SAItD,OAHAhF,KAAK1C,SAAQ,SAAAyG,GAAM,OACjB,EAAAA,EAAG0B,WAAUG,OAAb,QAAuBL,MAElBvF,ME5IP6F,SFwJF,WAA8B,2BAATP,EAAS,yBAATA,EAAS,gBAC5B,IAAMC,EAAa3B,EAAU0B,EAAQE,KAAI,SAAA7K,GAAC,OAAIA,EAAEqK,MAAM,SACtD,OAAOf,EAAYjE,MAAM,SAAA+D,GACvB,OAAOwB,EAAWrB,QAAO,SAAA4B,GAAS,OAAI/B,EAAG0B,UAAUM,SAASD,MAAYlJ,OAAS,KAChFA,OAAS,GE3JZoJ,YF8IF,WAAiC,2BAATV,EAAS,yBAATA,EAAS,gBAC/B,IAAMC,EAAa3B,EAAU0B,EAAQE,KAAI,SAAA7K,GAAC,OAAIA,EAAEqK,MAAM,SACtDhF,KAAK1C,SAAQ,SAAAyG,GACXwB,EAAWjI,SAAQ,SAAAwI,GACjB/B,EAAG0B,UAAUQ,OAAOH,UEjJxBI,KF6JF,SAAcC,EAAO5K,GACnB,GAAyB,IAArBmH,UAAU9F,QAAiC,iBAAVuJ,EAEnC,OAAInG,KAAK,GAAWA,KAAK,GAAGoG,aAAaD,QACzC,EAIF,IAAK,IAAI7L,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC,GAAyB,IAArBoI,UAAU9F,OAEZoD,KAAK1F,GAAGiE,aAAa4H,EAAO5K,QAG5B,IAAK,IAAM8K,KAAYF,EACrBnG,KAAK1F,GAAG+L,GAAYF,EAAME,GAC1BrG,KAAK1F,GAAGiE,aAAa8H,EAAUF,EAAME,IAK3C,OAAOrG,MEjLPsG,WFoLF,SAAoBJ,GAClB,IAAK,IAAI5L,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC0F,KAAK1F,GAAGiM,gBAAgBL,GAG1B,OAAOlG,MExLPwG,UF6TF,SAAmBA,GACjB,IAAK,IAAIlM,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC0F,KAAK1F,GAAGgE,MAAMkI,UAAYA,EAG5B,OAAOxG,MEjUPyG,WFoUF,SAAoBC,GAClB,IAAK,IAAIpM,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC0F,KAAK1F,GAAGgE,MAAMqI,mBAAyC,iBAAbD,EAA2BA,EAAlC,KAAiDA,EAGtF,OAAO1G,MExUPmF,GF2UF,WAAqB,2BAANjD,EAAM,yBAANA,EAAM,gBACnB,IAAK0E,EAAgD1E,EAArD,GAAgB2E,EAAqC3E,EAArD,GAAgC4E,EAAqB5E,EAArD,GAA0C6E,EAAW7E,EAArD,GASA,SAAS8E,EAAgBjF,GACvB,IAAMrF,EAASqF,EAAErF,OACjB,GAAKA,EAAL,CACA,IAAMuK,EAAYlF,EAAErF,OAAOwK,eAAiB,GAM5C,GAJID,EAAUhE,QAAQlB,GAAK,GACzBkF,EAAUE,QAAQpF,GAGhBoC,EAAEzH,GAAQ0K,GAAGP,GAAiBC,EAASvE,MAAM7F,EAAQuK,QAGvD,IAFA,IAAMI,EAAUlD,EAAEzH,GAAQ2K,UAEjBC,EAAI,EAAGA,EAAID,EAAQzK,OAAQ0K,GAAK,EACnCnD,EAAEkD,EAAQC,IAAIF,GAAGP,IAAiBC,EAASvE,MAAM8E,EAAQC,GAAIL,IAKvE,SAASM,EAAYxF,GACnB,IAAMkF,EAAYlF,GAAKA,EAAErF,QAASqF,EAAErF,OAAOwK,eAAsB,GAE7DD,EAAUhE,QAAQlB,GAAK,GACzBkF,EAAUE,QAAQpF,GAGpB+E,EAASvE,MAAMvC,KAAMiH,GAhCA,mBAAZ/E,EAAK,KACb0E,EAAgC1E,EADA,GACrB4E,EAAqB5E,EADA,GACX6E,EAAW7E,EADA,GAEjC2E,OAAiB/D,GAGdiE,IAASA,GAAU,GAiCxB,IAHA,IACIS,EADEC,EAASb,EAAU5B,MAAM,KAGtB1K,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAAG,CACvC,IAAMyJ,EAAK/D,KAAK1F,GAEhB,GAAKuM,EAaH,IAAKW,EAAI,EAAGA,EAAIC,EAAO7K,OAAQ4K,GAAK,EAAG,CACrC,IAAME,EAAQD,EAAOD,GAChBzD,EAAG4D,oBAAmB5D,EAAG4D,kBAAoB,IAC7C5D,EAAG4D,kBAAkBD,KAAQ3D,EAAG4D,kBAAkBD,GAAS,IAChE3D,EAAG4D,kBAAkBD,GAAOpF,KAAK,CAC/BwE,WACAc,cAAeZ,IAEjBjD,EAAGtG,iBAAiBiK,EAAOV,EAAiBD,QApB9C,IAAKS,EAAI,EAAGA,EAAIC,EAAO7K,OAAQ4K,GAAK,EAAG,CACrC,IAAME,EAAQD,EAAOD,GAChBzD,EAAG8D,gBAAe9D,EAAG8D,cAAgB,IACrC9D,EAAG8D,cAAcH,KAAQ3D,EAAG8D,cAAcH,GAAS,IACxD3D,EAAG8D,cAAcH,GAAOpF,KAAK,CAC3BwE,WACAc,cAAeL,IAEjBxD,EAAGtG,iBAAiBiK,EAAOH,EAAaR,IAiB9C,OAAO/G,MEhZP8H,IFmZF,WAAsB,2BAAN5F,EAAM,yBAANA,EAAM,gBACpB,IAAK0E,EAAgD1E,EAArD,GAAgB2E,EAAqC3E,EAArD,GAAgC4E,EAAqB5E,EAArD,GAA0C6E,EAAW7E,EAArD,GAEuB,mBAAZA,EAAK,KACb0E,EAAgC1E,EADA,GACrB4E,EAAqB5E,EADA,GACX6E,EAAW7E,EADA,GAEjC2E,OAAiB/D,GAGdiE,IAASA,GAAU,GAGxB,IAFA,IAAMU,EAASb,EAAU5B,MAAM,KAEtB1K,EAAI,EAAGA,EAAImN,EAAO7K,OAAQtC,GAAK,EAGtC,IAFA,IAAMoN,EAAQD,EAAOnN,GAEZkN,EAAI,EAAGA,EAAIxH,KAAKpD,OAAQ4K,GAAK,EAAG,CACvC,IAAMzD,EAAK/D,KAAKwH,GACZO,OAAQ,EAQZ,IANKlB,GAAkB9C,EAAG8D,cACxBE,EAAWhE,EAAG8D,cAAcH,GACnBb,GAAkB9C,EAAG4D,oBAC9BI,EAAWhE,EAAG4D,kBAAkBD,IAG9BK,GAAYA,EAASnL,OACvB,IAAK,IAAI0K,EAAIS,EAASnL,OAAS,EAAG0K,GAAK,EAAGA,GAAK,EAAG,CAChD,IAAMU,EAAUD,EAAST,GAErBR,GAAYkB,EAAQlB,WAAaA,GAG1BA,GAAYkB,EAAQlB,UAAYkB,EAAQlB,SAASmB,WAAaD,EAAQlB,SAASmB,YAAcnB,GAFtG/C,EAAGrG,oBAAoBgK,EAAOM,EAAQJ,cAAeb,GACrDgB,EAASG,OAAOZ,EAAG,IAITR,IACV/C,EAAGrG,oBAAoBgK,EAAOM,EAAQJ,cAAeb,GACrDgB,EAASG,OAAOZ,EAAG,KAO7B,OAAOtH,ME7bPkF,QFsdF,WAA0B,IACxB,IAAMnE,EAASF,IADS,mBAANqB,EAAM,yBAANA,EAAM,gBAKxB,IAHA,IAAMuF,EAASvF,EAAK,GAAG8C,MAAM,KACvBiC,EAAY/E,EAAK,GAEd5H,EAAI,EAAGA,EAAImN,EAAO7K,OAAQtC,GAAK,EAGtC,IAFA,IAAMoN,EAAQD,EAAOnN,GAEZkN,EAAI,EAAGA,EAAIxH,KAAKpD,OAAQ4K,GAAK,EAAG,CACvC,IAAMzD,EAAK/D,KAAKwH,GAEhB,GAAIzG,EAAOhB,YAAa,CACtB,IAAMoI,EAAM,IAAIpH,EAAOhB,YAAY2H,EAAO,CACxCU,OAAQnB,EACRoB,SAAS,EACTC,YAAY,IAEdvE,EAAGmD,cAAgBhF,EAAKgC,QAAO,SAACqE,EAAMC,GAAP,OAAqBA,EAAY,KAChEzE,EAAG0E,cAAcN,GACjBpE,EAAGmD,cAAgB,UACZnD,EAAGmD,eAKhB,OAAOlH,ME9eP0I,cFifF,SAAuBhI,GACrB,IAAMiI,EAAM3I,KAYZ,OAJIU,GACFiI,EAAIxD,GAAG,iBAPT,SAASyD,EAAa7G,GAChBA,EAAErF,SAAWsD,OACjBU,EAASjG,KAAKuF,KAAM+B,GACpB4G,EAAIb,IAAI,gBAAiBc,OAOpB5I,ME7fP6I,WF8hBF,SAAoBC,GAClB,GAAI9I,KAAKpD,OAAS,EAAG,CACnB,GAAIkM,EAAgB,CAClB,IAAMC,EAAS/I,KAAK+I,SACpB,OAAO/I,KAAK,GAAGgJ,YAAcC,WAAWF,EAAO7I,iBAAiB,iBAAmB+I,WAAWF,EAAO7I,iBAAiB,gBAGxH,OAAOF,KAAK,GAAGgJ,YAGjB,OAAO,MEviBPE,YFwjBF,SAAqBJ,GACnB,GAAI9I,KAAKpD,OAAS,EAAG,CACnB,GAAIkM,EAAgB,CAClB,IAAMC,EAAS/I,KAAK+I,SACpB,OAAO/I,KAAK,GAAGmJ,aAAeF,WAAWF,EAAO7I,iBAAiB,eAAiB+I,WAAWF,EAAO7I,iBAAiB,kBAGvH,OAAOF,KAAK,GAAGmJ,aAGjB,OAAO,MEjkBPJ,OFmnBF,WACE,IAAMhI,EAASF,IACf,OAAIb,KAAK,GAAWe,EAAOd,iBAAiBD,KAAK,GAAI,MAC9C,IErnBPoJ,OFmkBF,WACE,GAAIpJ,KAAKpD,OAAS,EAAG,CACnB,IAAMmE,EAASF,IACTvB,EAAWF,IACX2E,EAAK/D,KAAK,GACVqJ,EAAMtF,EAAGuF,wBACT9L,EAAO8B,EAAS9B,KAChB+L,EAAYxF,EAAGwF,WAAa/L,EAAK+L,WAAa,EAC9CC,EAAazF,EAAGyF,YAAchM,EAAKgM,YAAc,EACjDC,EAAY1F,IAAOhD,EAASA,EAAO2I,QAAU3F,EAAG0F,UAChDE,EAAa5F,IAAOhD,EAASA,EAAO6I,QAAU7F,EAAG4F,WACvD,MAAO,CACLE,IAAKR,EAAIQ,IAAMJ,EAAYF,EAC3BO,KAAMT,EAAIS,KAAOH,EAAaH,GAIlC,OAAO,MEnlBPO,IFunBF,SAAapN,EAAOpB,GAClB,IACIjB,EADEyG,EAASF,IAGf,GAAyB,IAArB6B,UAAU9F,OAAc,CAC1B,GAAqB,iBAAVD,EAGJ,CAEL,IAAKrC,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAChC,IAAK,IAAM0P,KAAQrN,EACjBqD,KAAK1F,GAAGgE,MAAM0L,GAAQrN,EAAMqN,GAIhC,OAAOhK,KATP,GAAIA,KAAK,GAAI,OAAOe,EAAOd,iBAAiBD,KAAK,GAAI,MAAME,iBAAiBvD,GAahF,GAAyB,IAArB+F,UAAU9F,QAAiC,iBAAVD,EAAoB,CAEvD,IAAKrC,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAChC0F,KAAK1F,GAAGgE,MAAM3B,GAASpB,EAGzB,OAAOyE,KAGT,OAAOA,MEnpBPiK,KFspBF,SAAcvJ,GACZ,OAAKA,GACLV,KAAK1C,SAAQ,SAACyG,EAAImG,GAChBxJ,EAAS6B,MAAMwB,EAAI,CAACA,EAAImG,OAEnBlK,MAJeA,MEtpBtBsE,KFkqBF,SAAcA,GACZ,QAAoB,IAATA,EACT,OAAOtE,KAAK,GAAKA,KAAK,GAAG0E,UAAY,KAGvC,IAAK,IAAIpK,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC0F,KAAK1F,GAAGoK,UAAYJ,EAGtB,OAAOtE,ME1qBPmK,KF6qBF,SAAcA,GACZ,QAAoB,IAATA,EACT,OAAOnK,KAAK,GAAKA,KAAK,GAAGoK,YAAY7F,OAAS,KAGhD,IAAK,IAAIjK,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC0F,KAAK1F,GAAG8P,YAAcD,EAGxB,OAAOnK,MErrBPoH,GFwrBF,SAAYhD,GACV,IAGIiG,EACA/P,EAJEyG,EAASF,IACTvB,EAAWF,IACX2E,EAAK/D,KAAK,GAGhB,IAAK+D,QAA0B,IAAbK,EAA0B,OAAO,EAEnD,GAAwB,iBAAbA,EAAuB,CAChC,GAAIL,EAAGuG,QAAS,OAAOvG,EAAGuG,QAAQlG,GAClC,GAAIL,EAAGwG,sBAAuB,OAAOxG,EAAGwG,sBAAsBnG,GAC9D,GAAIL,EAAGyG,kBAAmB,OAAOzG,EAAGyG,kBAAkBpG,GAGtD,IAFAiG,EAAclG,EAAEC,GAEX9J,EAAI,EAAGA,EAAI+P,EAAYzN,OAAQtC,GAAK,EACvC,GAAI+P,EAAY/P,KAAOyJ,EAAI,OAAO,EAGpC,OAAO,EAGT,GAAIK,IAAa9E,EACf,OAAOyE,IAAOzE,EAGhB,GAAI8E,IAAarD,EACf,OAAOgD,IAAOhD,EAGhB,GAAIqD,EAASQ,UAAYR,aAAoBd,EAAM,CAGjD,IAFA+G,EAAcjG,EAASQ,SAAW,CAACR,GAAYA,EAE1C9J,EAAI,EAAGA,EAAI+P,EAAYzN,OAAQtC,GAAK,EACvC,GAAI+P,EAAY/P,KAAOyJ,EAAI,OAAO,EAGpC,OAAO,EAGT,OAAO,GE9tBPmG,MFiuBF,WACE,IACI5P,EADAmQ,EAAQzK,KAAK,GAGjB,GAAIyK,EAAO,CAGT,IAFAnQ,EAAI,EAEuC,QAAnCmQ,EAAQA,EAAMC,kBACG,IAAnBD,EAAM7F,WAAgBtK,GAAK,GAGjC,OAAOA,IE3uBTqQ,GFivBF,SAAYT,GACV,QAAqB,IAAVA,EAAuB,OAAOlK,KACzC,IAAMpD,EAASoD,KAAKpD,OAEpB,GAAIsN,EAAQtN,EAAS,EACnB,OAAOuH,EAAE,IAGX,GAAI+F,EAAQ,EAAG,CACb,IAAMU,EAAchO,EAASsN,EAC7B,OAA4B/F,EAAxByG,EAAc,EAAY,GACrB,CAAC5K,KAAK4K,KAGjB,OAAOzG,EAAE,CAACnE,KAAKkK,ME9vBfW,OFiwBF,WAIE,IAHA,IAAIC,EACExL,EAAWF,IAERkI,EAAI,EAAGA,EAAI,UAAI1K,OAAQ0K,GAAK,EAAG,CACtCwD,EAAexD,EAAP,qBAAOA,OAAP,YAAOA,GAEf,IAAK,IAAIhN,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACpC,GAAwB,iBAAbwQ,EAAuB,CAChC,IAAMC,EAAUzL,EAASnB,cAAc,OAGvC,IAFA4M,EAAQrG,UAAYoG,EAEbC,EAAQC,YACbhL,KAAK1F,GAAG2Q,YAAYF,EAAQC,iBAEzB,GAAIF,aAAoBxH,EAC7B,IAAK,IAAIkE,EAAI,EAAGA,EAAIsD,EAASlO,OAAQ4K,GAAK,EACxCxH,KAAK1F,GAAG2Q,YAAYH,EAAStD,SAG/BxH,KAAK1F,GAAG2Q,YAAYH,GAK1B,OAAO9K,MEzxBPkL,QFiyBF,SAAiBJ,GACf,IACIxQ,EACAkN,EAFElI,EAAWF,IAIjB,IAAK9E,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAChC,GAAwB,iBAAbwQ,EAAuB,CAChC,IAAMC,EAAUzL,EAASnB,cAAc,OAGvC,IAFA4M,EAAQrG,UAAYoG,EAEftD,EAAIuD,EAAQ1M,WAAWzB,OAAS,EAAG4K,GAAK,EAAGA,GAAK,EACnDxH,KAAK1F,GAAG6Q,aAAaJ,EAAQ1M,WAAWmJ,GAAIxH,KAAK1F,GAAG+D,WAAW,SAE5D,GAAIyM,aAAoBxH,EAC7B,IAAKkE,EAAI,EAAGA,EAAIsD,EAASlO,OAAQ4K,GAAK,EACpCxH,KAAK1F,GAAG6Q,aAAaL,EAAStD,GAAIxH,KAAK1F,GAAG+D,WAAW,SAGvD2B,KAAK1F,GAAG6Q,aAAaL,EAAU9K,KAAK1F,GAAG+D,WAAW,IAItD,OAAO2B,MEtzBPoL,KF01BF,SAAchH,GACZ,OAAIpE,KAAKpD,OAAS,EACZwH,EACEpE,KAAK,GAAGqL,oBAAsBlH,EAAEnE,KAAK,GAAGqL,oBAAoBjE,GAAGhD,GAC1DD,EAAE,CAACnE,KAAK,GAAGqL,qBAGblH,EAAE,IAGPnE,KAAK,GAAGqL,mBAA2BlH,EAAE,CAACnE,KAAK,GAAGqL,qBAC3ClH,EAAE,IAGJA,EAAE,KEv2BTmH,QF02BF,SAAiBlH,GACf,IAAMmH,EAAU,GACZxH,EAAK/D,KAAK,GACd,IAAK+D,EAAI,OAAOI,EAAE,IAElB,KAAOJ,EAAGsH,oBAAoB,CAC5B,IAAMD,EAAOrH,EAAGsH,mBAEZjH,EACED,EAAEiH,GAAMhE,GAAGhD,IAAWmH,EAAQjJ,KAAK8I,GAClCG,EAAQjJ,KAAK8I,GAEpBrH,EAAKqH,EAGP,OAAOjH,EAAEoH,IEx3BTC,KF23BF,SAAcpH,GACZ,GAAIpE,KAAKpD,OAAS,EAAG,CACnB,IAAMmH,EAAK/D,KAAK,GAEhB,OAAIoE,EACEL,EAAG0H,wBAA0BtH,EAAEJ,EAAG0H,wBAAwBrE,GAAGhD,GACxDD,EAAE,CAACJ,EAAG0H,yBAGRtH,EAAE,IAGPJ,EAAG0H,uBAA+BtH,EAAE,CAACJ,EAAG0H,yBACrCtH,EAAE,IAGX,OAAOA,EAAE,KE14BTuH,QF64BF,SAAiBtH,GACf,IAAMuH,EAAU,GACZ5H,EAAK/D,KAAK,GACd,IAAK+D,EAAI,OAAOI,EAAE,IAElB,KAAOJ,EAAG0H,wBAAwB,CAChC,IAAMD,EAAOzH,EAAG0H,uBAEZrH,EACED,EAAEqH,GAAMpE,GAAGhD,IAAWuH,EAAQrJ,KAAKkJ,GAClCG,EAAQrJ,KAAKkJ,GAEpBzH,EAAKyH,EAGP,OAAOrH,EAAEwH,IE35BTC,OFk6BF,SAAgBxH,GAGd,IAFA,IAAMiD,EAAU,GAEP/M,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EACT,OAAvB0F,KAAK1F,GAAGuR,aACNzH,EACED,EAAEnE,KAAK1F,GAAGuR,YAAYzE,GAAGhD,IAAWiD,EAAQ/E,KAAKtC,KAAK1F,GAAGuR,YAE7DxE,EAAQ/E,KAAKtC,KAAK1F,GAAGuR,aAK3B,OAAO1H,EAAEkD,IE96BTA,QFi7BF,SAAiBjD,GAGf,IAFA,IAAMiD,EAAU,GAEP/M,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAGpC,IAFA,IAAIsR,EAAS5L,KAAK1F,GAAGuR,WAEdD,GACDxH,EACED,EAAEyH,GAAQxE,GAAGhD,IAAWiD,EAAQ/E,KAAKsJ,GAEzCvE,EAAQ/E,KAAKsJ,GAGfA,EAASA,EAAOC,WAIpB,OAAO1H,EAAEkD,IEj8BTyE,QFo8BF,SAAiB1H,GACf,IAAI0H,EAAU9L,KAEd,YAAwB,IAAboE,EACFD,EAAE,KAGN2H,EAAQ1E,GAAGhD,KACd0H,EAAUA,EAAQzE,QAAQjD,GAAUuG,GAAG,IAGlCmB,IE98BPC,KFi9BF,SAAc3H,GAGZ,IAFA,IAAM4H,EAAgB,GAEb1R,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAGpC,IAFA,IAAM2R,EAAQjM,KAAK1F,GAAGyD,iBAAiBqG,GAE9BoD,EAAI,EAAGA,EAAIyE,EAAMrP,OAAQ4K,GAAK,EACrCwE,EAAc1J,KAAK2J,EAAMzE,IAI7B,OAAOrD,EAAE6H,IE39BT5N,SF89BF,SAAkBgG,GAGhB,IAFA,IAAMhG,EAAW,GAER9D,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAGpC,IAFA,IAAM+D,EAAa2B,KAAK1F,GAAG8D,SAElBoJ,EAAI,EAAGA,EAAInJ,EAAWzB,OAAQ4K,GAAK,EACrCpD,IAAYD,EAAE9F,EAAWmJ,IAAIJ,GAAGhD,IACnChG,EAASkE,KAAKjE,EAAWmJ,IAK/B,OAAOrD,EAAE/F,IE1+BT8F,OF6oBF,SAAgBxD,GAEd,OAAOyD,EADQF,EAAYjE,KAAMU,KE7oBjCkF,OF4+BF,WACE,IAAK,IAAItL,EAAI,EAAGA,EAAI0F,KAAKpD,OAAQtC,GAAK,EAChC0F,KAAK1F,GAAGuR,YAAY7L,KAAK1F,GAAGuR,WAAWK,YAAYlM,KAAK1F,IAG9D,OAAO0F,OE/+BThF,OAAOqC,KAAK+H,GAAS9H,SAAQ,SAAA6O,GAC3BnR,OAAOC,eAAekJ,EAAEpB,GAAIoJ,EAAY,CACtC5Q,MAAO6J,EAAQ+G,GACfpP,UAAU,OAGCoH,IC5CXiI,ECCAC,ECDAC,EH4CWnI,II5Bf,SAASoI,EAAS7L,EAAU8L,GAC1B,YADqC,IAAXA,MAAQ,GAC3BlM,WAAWI,EAAU8L,GAG9B,SAASC,IACP,OAAOrM,KAAKqM,MAsBd,SAASC,EAAa3I,EAAI4I,QAAY,IAAZA,MAAO,KAC/B,IACIC,EACAC,EACAC,EAHE/L,EAASF,IAITkM,EAxBR,SAA0BhJ,GACxB,IACIzF,EADEyC,EAASF,IAef,OAZIE,EAAOd,mBACT3B,EAAQyC,EAAOd,iBAAiB8D,EAAI,QAGjCzF,GAASyF,EAAGiJ,eACf1O,EAAQyF,EAAGiJ,cAGR1O,IACHA,EAAQyF,EAAGzF,OAGNA,EAQU2B,CAAiB8D,GA+BlC,OA7BIhD,EAAOkM,kBACTJ,EAAeE,EAASvG,WAAauG,EAASG,iBAE7BlI,MAAM,KAAKpI,OAAS,IACnCiQ,EAAeA,EAAa7H,MAAM,MAAMQ,KAAI,SAAAnD,GAAC,OAAIA,EAAE8K,QAAQ,IAAK,QAAMC,KAAK,OAK7EN,EAAkB,IAAI/L,EAAOkM,gBAAiC,SAAjBJ,EAA0B,GAAKA,IAG5ED,GADAE,EAAkBC,EAASM,cAAgBN,EAASO,YAAcP,EAASQ,aAAeR,EAASS,aAAeT,EAASvG,WAAauG,EAAS7M,iBAAiB,aAAaiN,QAAQ,aAAc,uBAC5KnK,WAAWgC,MAAM,KAG/B,MAAT2H,IAE0BE,EAAxB9L,EAAOkM,gBAAgCH,EAAgBW,IAChC,KAAlBb,EAAOhQ,OAA8BqM,WAAW2D,EAAO,KAC5C3D,WAAW2D,EAAO,KAG3B,MAATD,IAE0BE,EAAxB9L,EAAOkM,gBAAgCH,EAAgBY,IAChC,KAAlBd,EAAOhQ,OAA8BqM,WAAW2D,EAAO,KAC5C3D,WAAW2D,EAAO,KAGjCC,GAAgB,EAGzB,SAAS7P,EAASjC,GAChB,MAAoB,iBAANA,GAAwB,OAANA,GAAcA,EAAEmC,aAAkE,WAAnDlC,OAAOkB,UAAU8G,SAASvI,KAAKM,GAAG4S,MAAM,GAAI,GAG7G,SAASC,EAAOC,GAEd,MAAsB,oBAAX9M,aAAwD,IAAvBA,OAAO+M,YAC1CD,aAAgBC,YAGlBD,IAA2B,IAAlBA,EAAKjJ,UAAoC,KAAlBiJ,EAAKjJ,UAG9C,SAASzH,IAIP,IAHA,IAAM4Q,EAAK/S,OAAO,UAAD,+BACXgT,EAAW,CAAC,YAAa,cAAe,aAErC1T,EAAI,EAAGA,EAAI,UAAKsC,OAAQtC,GAAK,EAAG,CACvC,IAAM2T,EAAkB3T,EAAR,qBAAQA,OAAR,YAAQA,GAExB,GAAI2T,UAAoDL,EAAOK,GAG7D,IAFA,IAAMC,EAAYlT,OAAOqC,KAAKrC,OAAOiT,IAAa/J,QAAO,SAAArI,GAAG,OAAImS,EAAS/K,QAAQpH,GAAO,KAE/EsS,EAAY,EAAGC,EAAMF,EAAUtR,OAAQuR,EAAYC,EAAKD,GAAa,EAAG,CAC/E,IAAME,EAAUH,EAAUC,GACpBG,EAAOtT,OAAOuT,yBAAyBN,EAAYI,QAE5CvL,IAATwL,GAAsBA,EAAKpT,aACzB8B,EAAS+Q,EAAGM,KAAarR,EAASiR,EAAWI,IAC3CJ,EAAWI,GAASG,WACtBT,EAAGM,GAAWJ,EAAWI,GAEzBlR,EAAO4Q,EAAGM,GAAUJ,EAAWI,KAEvBrR,EAAS+Q,EAAGM,KAAarR,EAASiR,EAAWI,KACvDN,EAAGM,GAAW,GAEVJ,EAAWI,GAASG,WACtBT,EAAGM,GAAWJ,EAAWI,GAEzBlR,EAAO4Q,EAAGM,GAAUJ,EAAWI,KAGjCN,EAAGM,GAAWJ,EAAWI,KAOnC,OAAON,EAGT,SAASU,EAAe1K,EAAI2K,EAASC,GACnC5K,EAAGzF,MAAMsQ,YAAYF,EAASC,GAGhC,SAASE,EAAT,GAIG,IAIGC,EAPJC,EAGC,EAHDA,OACAC,EAEC,EAFDA,eACAC,EACC,EADDA,KAEMlO,EAASF,IACTqO,GAAiBH,EAAOI,UAC1BC,EAAY,KAEV1I,EAAWqI,EAAOM,OAAOC,MAC/BP,EAAOQ,UAAUjR,MAAMkR,eAAiB,OACxCzO,EAAOJ,qBAAqBoO,EAAOU,gBACnC,IAAMC,EAAMV,EAAiBE,EAAgB,OAAS,OAEhDS,EAAe,SAACC,EAASlT,GAC7B,MAAe,SAARgT,GAAkBE,GAAWlT,GAAkB,SAARgT,GAAkBE,GAAWlT,IAG7D,SAAVmT,IAAgB,MACpBf,GAAO,IAAI1O,MAAO0P,UAEA,OAAdV,IACFA,EAAYN,GAGd,IAAMiB,EAAWC,KAAKC,IAAID,KAAKE,KAAKpB,EAAOM,GAAa1I,EAAU,GAAI,GAChEyJ,EAAe,GAAMH,KAAKI,IAAIL,EAAWC,KAAKK,IAAM,EACtDC,EAAkBpB,EAAgBiB,GAAgBnB,EAAiBE,GAUvE,GARIS,EAAaW,EAAiBtB,KAChCsB,EAAkBtB,GAGpBD,EAAOQ,UAAUgB,WAAjB,MACGtB,GAAOqB,EADV,IAIIX,EAAaW,EAAiBtB,GAUhC,OATAD,EAAOQ,UAAUjR,MAAMkS,SAAW,SAClCzB,EAAOQ,UAAUjR,MAAMkR,eAAiB,GACxClP,YAAW,WAAM,MACfyO,EAAOQ,UAAUjR,MAAMkS,SAAW,GAClCzB,EAAOQ,UAAUgB,WAAjB,MACGtB,GAAOqB,EADV,YAIFvP,EAAOJ,qBAAqBoO,EAAOU,gBAIrCV,EAAOU,eAAiB1O,EAAON,sBAAsBoP,GAGvDA,GHjKF,SAASY,IAKP,OAJKrE,IACHA,EA/BJ,WACE,IAAMrL,EAASF,IACTvB,EAAWF,IACjB,MAAO,CACLsR,aAAcpR,EAASqR,iBAAmB,mBAAoBrR,EAASqR,gBAAgBrS,MACvFsS,SAAU,iBAAkB7P,GAAUA,EAAO8P,eAAiBvR,aAAoByB,EAAO8P,eACzFC,gBAAiB,WACf,IAAIC,GAAkB,EAEtB,IACE,IAAMC,EAAOhW,OAAOC,eAAe,GAAI,UAAW,CAEhDE,IAFgD,WAG9C4V,GAAkB,KAItBhQ,EAAOtD,iBAAiB,sBAAuB,KAAMuT,GACrD,MAAOjP,IAGT,OAAOgP,EAfQ,GAiBjBE,SACS,mBAAoBlQ,GAOnBmQ,IAGL9E,ECWT,SAAS+E,EAAUC,GAKjB,YALiC,IAAhBA,MAAY,IACxB/E,IACHA,EA9CJ,YAEQ,IADN5M,QACM,MAAJ,GAAI,GADNA,UAEM2M,EAAUqE,IACV1P,EAASF,IACTwQ,EAAWtQ,EAAOvB,UAAU6R,SAC5BC,EAAK7R,GAAasB,EAAOvB,UAAUC,UACnC8R,EAAS,CACbC,KAAK,EACLC,SAAS,GAELC,EAAc3Q,EAAOV,OAAOsR,MAC5BC,EAAe7Q,EAAOV,OAAOwR,OAC7BJ,EAAUH,EAAGQ,MAAM,+BAErBC,EAAOT,EAAGQ,MAAM,wBACdE,EAAOV,EAAGQ,MAAM,2BAChBG,GAAUF,GAAQT,EAAGQ,MAAM,8BAC3BI,EAAuB,UAAbb,EACZc,EAAqB,aAAbd,EAsBZ,OAlBKU,GAAQI,GAAS/F,EAAQwE,OAFV,CAAC,YAAa,YAAa,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,YAExG3N,QAAWyO,EAAvB,IAAsCE,IAAmB,KAC9FG,EAAOT,EAAGQ,MAAM,0BACLC,EAAO,CAAC,EAAG,EAAG,WACzBI,GAAQ,GAINV,IAAYS,IACdX,EAAOa,GAAK,UACZb,EAAOE,SAAU,IAGfM,GAAQE,GAAUD,KACpBT,EAAOa,GAAK,MACZb,EAAOC,KAAM,GAIRD,EAKUc,CAAWjB,IAGrB/E,ECpCT,SAASiG,IAKP,OAJKhG,IACHA,EAhBJ,WACE,IAGQgF,EAHFvQ,EAASF,IAOf,MAAO,CACL0R,UALMjB,EAAKvQ,EAAOvB,UAAUC,UAAU+S,cAC/BlB,EAAGrO,QAAQ,WAAa,GAAKqO,EAAGrO,QAAQ,UAAY,GAAKqO,EAAGrO,QAAQ,WAAa,GAKxFwP,UAAW,+CAA+CC,KAAK3R,EAAOvB,UAAUC,YAMtEkT,IAGLrG,EEtBM,SAASsG,EAAT,GAKZ,IAJD7D,EAIC,EAJDA,OACA8D,EAGC,EAHDA,aACAC,EAEC,EAFDA,UACAC,EACC,EADDA,KAGEC,EAEEjE,EAFFiE,YACAC,EACElE,EADFkE,cAEEvD,EAAMoD,EAQV,GANKpD,IAC8BA,EAA7BsD,EAAcC,EAAqB,OAAgBD,EAAcC,EAAqB,OAAkB,SAG9GlE,EAAOmE,KAAP,aAAyBH,GAErBF,GAAgBG,IAAgBC,EAAe,CACjD,GAAY,UAARvD,EAEF,YADAX,EAAOmE,KAAP,uBAAmCH,GAIrChE,EAAOmE,KAAP,wBAAoCH,GAExB,SAARrD,EACFX,EAAOmE,KAAP,sBAAkCH,GAElChE,EAAOmE,KAAP,sBAAkCH,ICTzB,SAASI,EAAazL,GACnC,IACMpI,EAAWF,IACX2B,EAASF,IACT0H,EAHSvI,KAGKoT,gBAElB/D,EALarP,KAKbqP,OACAgE,EANarT,KAMbqT,QAGF,GATerT,KAObsT,WAPatT,KAWJuT,YAAalE,EAAOmE,gCAA/B,EAXexT,KAeHuT,WAAalE,EAAOoE,SAAWpE,EAAOqE,MAfnC1T,KAgBN2T,UAGT,IAAI5R,EAAI2F,EACJ3F,EAAE6R,gBAAe7R,EAAIA,EAAE6R,eAC3B,IAAIC,EAAY1P,EAAEpC,EAAErF,QAEpB,IAAiC,YAA7B2S,EAAOyE,mBACJD,EAAU/H,QAxBF9L,KAwBiBuP,WAAW3S,UAG3C2L,EAAKwL,aAA0B,eAAXhS,EAAEiS,MACjBzL,EAAKwL,gBAAgB,UAAWhS,IAAiB,IAAZA,EAAEkS,WACvC1L,EAAKwL,cAAgB,WAAYhS,GAAKA,EAAEmS,OAAS,GAClD3L,EAAK4L,WAAa5L,EAAK6L,UAA3B,CAEA,IAAMC,IAAyBhF,EAAOiF,gBAA4C,KAA1BjF,EAAOiF,eAEzDC,EAAY7M,EAAM8M,aAAe9M,EAAM8M,eAAiB9M,EAAM+M,KAEhEJ,GAAwBtS,EAAErF,QAAUqF,EAAErF,OAAOgY,YAAcH,IAC7DV,EAAY1P,EAAEoQ,EAAU,KAG1B,IAAMI,EAAoBtF,EAAOsF,kBAAoBtF,EAAOsF,kBAAlC,IAA0DtF,EAAOiF,eACrFM,KAAoB7S,EAAErF,SAAUqF,EAAErF,OAAOgY,YAE/C,GAAIrF,EAAOwF,YAAcD,EA5D3B,SAAwBxQ,EAAU0Q,GAahC,YAb6C,IAAbA,MAAO9U,MACvC,SAAS+U,EAAchR,GACrB,IAAKA,GAAMA,IAAO3E,KAAiB2E,IAAOlD,IAAa,OAAO,KAC1DkD,EAAGiR,eAAcjR,EAAKA,EAAGiR,cAC7B,IAAM/I,EAAQlI,EAAG+H,QAAQ1H,GAEzB,OAAK6H,GAAUlI,EAAGkR,YAIXhJ,GAAS8I,EAAchR,EAAGkR,cAAcpW,MAHtC,KAMJkW,CAAcD,GA+CqBI,CAAeP,EAAmBd,EAAU,IAAMA,EAAU/H,QAAQ6I,GAAmB,IA3ClH3U,KA4CNmV,YAAa,OAItB,IAAI9F,EAAO+F,cACJvB,EAAU/H,QAAQuD,EAAO+F,cAAc,GAD9C,CAIA/B,EAAQgC,SAAsB,eAAXtT,EAAEiS,KAAwBjS,EAAEuT,cAAc,GAAGC,MAAQxT,EAAEwT,MAC1ElC,EAAQmC,SAAsB,eAAXzT,EAAEiS,KAAwBjS,EAAEuT,cAAc,GAAGG,MAAQ1T,EAAE0T,MAC1E,IAAMC,EAASrC,EAAQgC,SACjBM,EAAStC,EAAQmC,SAEjBI,EAAqBvG,EAAOuG,oBAAsBvG,EAAOwG,sBACzDC,EAAqBzG,EAAOyG,oBAAsBzG,EAAO0G,sBAE/D,GAAIH,IAAuBF,GAAUI,GAAsBJ,GAAU3U,EAAOiV,WAAaF,GAAqB,CAC5G,GAA2B,YAAvBF,EAGF,OAFAlO,EAAMuO,iBAqBV,GAfAjb,OAAOkb,OAAO3N,EAAM,CAClB4L,WAAW,EACXC,SAAS,EACT+B,qBAAqB,EACrBC,iBAAatT,EACbuT,iBAAavT,IAEfuQ,EAAQqC,OAASA,EACjBrC,EAAQsC,OAASA,EACjBpN,EAAK+N,eAAiB7J,IA7EPzM,KA8ERmV,YAAa,EA9ELnV,KA+ERuW,aA/EQvW,KAgFRwW,oBAAiB1T,EACpBuM,EAAOoH,UAAY,IAAGlO,EAAKmO,oBAAqB,GAErC,eAAX3U,EAAEiS,KAAuB,CAC3B,IAAIiC,GAAiB,EAEjBpC,EAAUzM,GAAGmB,EAAKoO,qBACpBV,GAAiB,EAEa,WAA1BpC,EAAU,GAAGhW,WACf0K,EAAK4L,WAAY,IAIjB7U,EAAS3B,eAAiBwG,EAAE7E,EAAS3B,eAAeyJ,GAAGmB,EAAKoO,oBAAsBrX,EAAS3B,gBAAkBkW,EAAU,IACzHvU,EAAS3B,cAAcC,OAGzB,IAAMgZ,EAAuBX,GAlGhBjW,KAkGyC6W,gBAAkBxH,EAAOyH,0BAE1EzH,EAAO0H,gCAAiCH,GAA0B/C,EAAU,GAAGmD,mBAClFjV,EAAEkU,iBArGSjW,KAyGJqP,OAAO4H,UAzGHjX,KAyGsBqP,OAAO4H,SAAS3D,SAzGtCtT,KAyGwDiX,UAzGxDjX,KAyG2EuT,YAAclE,EAAOoE,SAzGhGzT,KA0GNiX,SAAS9D,eA1GHnT,KA6GRkT,KAAK,aAAcnR,MC/Hb,SAASmV,EAAYxP,GAClC,IAAMpI,EAAWF,IAEXmJ,EADSvI,KACKoT,gBAElB/D,EAHarP,KAGbqP,OACAgE,EAJarT,KAIbqT,QACc8D,EALDnX,KAKboX,aAGF,GARepX,KAMbsT,QAEF,CACA,IAAIvR,EAAI2F,EAGR,GAFI3F,EAAE6R,gBAAe7R,EAAIA,EAAE6R,eAEtBrL,EAAK4L,WAQV,IAAI5L,EAAKwL,cAA2B,cAAXhS,EAAEiS,KAA3B,CACA,IAAMqD,EAAyB,cAAXtV,EAAEiS,MAAwBjS,EAAEuT,gBAAkBvT,EAAEuT,cAAc,IAAMvT,EAAEuV,eAAe,IACnG/B,EAAmB,cAAXxT,EAAEiS,KAAuBqD,EAAY9B,MAAQxT,EAAEwT,MACvDE,EAAmB,cAAX1T,EAAEiS,KAAuBqD,EAAY5B,MAAQ1T,EAAE0T,MAE7D,GAAI1T,EAAEwV,wBAGJ,OAFAlE,EAAQqC,OAASH,OACjBlC,EAAQsC,OAASF,GAInB,IA/BezV,KA+BH6W,eAeV,OAdK1S,EAAEpC,EAAErF,QAAQ0K,GAAGmB,EAAKoO,qBAhCZ3W,KAiCJmV,YAAa,QAGlB5M,EAAK4L,YACPnZ,OAAOkb,OAAO7C,EAAS,CACrBqC,OAAQH,EACRI,OAAQF,EACRJ,SAAUE,EACVC,SAAUC,IAEZlN,EAAK+N,eAAiB7J,MAM1B,GAAIlE,EAAKwL,cAAgB1E,EAAOmI,sBAAwBnI,EAAOqE,KAC7D,GAlDa1T,KAkDFyX,cAET,GAAIhC,EAAQpC,EAAQsC,QApDT3V,KAoD0BmP,WApD1BnP,KAoD8C0X,gBAAkBjC,EAAQpC,EAAQsC,QApDhF3V,KAoDiGmP,WApDjGnP,KAoDqH2X,eAG9H,OAFApP,EAAK4L,WAAY,OACjB5L,EAAK6L,SAAU,QAGZ,GAAImB,EAAQlC,EAAQqC,QAzDd1V,KAyD+BmP,WAzD/BnP,KAyDmD0X,gBAAkBnC,EAAQlC,EAAQqC,QAzDrF1V,KAyDsGmP,WAzDtGnP,KAyD0H2X,eACrI,OAIJ,GAAIpP,EAAKwL,cAAgBzU,EAAS3B,eAC5BoE,EAAErF,SAAW4C,EAAS3B,eAAiBwG,EAAEpC,EAAErF,QAAQ0K,GAAGmB,EAAKoO,mBAG7D,OAFApO,EAAK6L,SAAU,OAhEJpU,KAiEJmV,YAAa,GASxB,GAJI5M,EAAK4N,qBAtEMnW,KAuENkT,KAAK,YAAanR,KAGvBA,EAAEuT,eAAiBvT,EAAEuT,cAAc1Y,OAAS,GAAhD,CACAyW,EAAQgC,SAAWE,EACnBlC,EAAQmC,SAAWC,EACnB,IAAMmC,EAAQvE,EAAQgC,SAAWhC,EAAQqC,OACnCmC,EAAQxE,EAAQmC,SAAWnC,EAAQsC,OACzC,KA/Ee3V,KA+EJqP,OAAOoH,WAAazG,KAAK8H,KAAK,SAAAF,EAAS,GAAT,SAAaC,EAAS,IA/EhD7X,KA+E4DqP,OAAOoH,WAAlF,CAGE,IAAIsB,EADN,QAAgC,IAArBxP,EAAK6N,YAjFDpW,KAoFFgY,gBAAkB3E,EAAQmC,WAAanC,EAAQsC,QApF7C3V,KAoF8DyX,cAAgBpE,EAAQgC,WAAahC,EAAQqC,OACtHnN,EAAK6N,aAAc,EAGfwB,EAAQA,EAAQC,EAAQA,GAAS,KACnCE,EAA4D,IAA/C/H,KAAKiI,MAAMjI,KAAKkI,IAAIL,GAAQ7H,KAAKkI,IAAIN,IAAgB5H,KAAKK,GACvE9H,EAAK6N,YA1FIpW,KA0FiBgY,eAAiBD,EAAa1I,EAAO0I,WAAa,GAAKA,EAAa1I,EAAO0I,YAe3G,GAVIxP,EAAK6N,aA/FMpW,KAgGNkT,KAAK,oBAAqBnR,QAGH,IAArBwG,EAAK8N,cACVhD,EAAQgC,WAAahC,EAAQqC,QAAUrC,EAAQmC,WAAanC,EAAQsC,SACtEpN,EAAK8N,aAAc,IAInB9N,EAAK6N,YACP7N,EAAK4L,WAAY,OAInB,GAAK5L,EAAK8N,YAAV,CA9GerW,KAkHRmV,YAAa,GAEf9F,EAAOoE,SAAW1R,EAAEuG,YACvBvG,EAAEkU,iBAGA5G,EAAO8I,2BAA6B9I,EAAO+I,QAC7CrW,EAAEsW,kBAGC9P,EAAK6L,UACJ/E,EAAOqE,OAASrE,EAAOoE,SA7HdzT,KA8HJ2T,UAGTpL,EAAK+P,eAjIQtY,KAiIgB0M,eAjIhB1M,KAkINuY,cAAc,GAlIRvY,KAoIFuT,WApIEvT,KAqIJwY,WAAWtT,QAAQ,qCAG5BqD,EAAKkQ,qBAAsB,GAEvBpJ,EAAOqJ,aAAyC,IA1IvC1Y,KA0IoB2Y,iBAAqD,IA1IzE3Y,KA0IsD4Y,gBA1ItD5Y,KA2IJ6Y,eAAc,GA3IV7Y,KA8INkT,KAAK,kBAAmBnR,IA9IlB/B,KAiJRkT,KAAK,aAAcnR,GAC1BwG,EAAK6L,SAAU,EACf,IAAI0E,EAnJW9Y,KAmJGgY,eAAiBJ,EAAQC,EAC3CxE,EAAQyF,KAAOA,EACfA,GAAQzJ,EAAO0J,WACX5B,IAAK2B,GAAQA,GAtJF9Y,KAuJRwW,eAAiBsC,EAAO,EAAI,OAAS,OAC5CvQ,EAAKyQ,iBAAmBF,EAAOvQ,EAAK+P,eACpC,IAAIW,GAAsB,EACtBC,EAAkB7J,EAAO6J,gBAgC7B,GA9BI7J,EAAOmI,sBACT0B,EAAkB,GAGhBJ,EAAO,GAAKvQ,EAAKyQ,iBAhKNhZ,KAgKgC2X,gBAC7CsB,GAAsB,EAClB5J,EAAO8J,aAAY5Q,EAAKyQ,iBAlKfhZ,KAkKyC2X,eAAiB,EAAxB,UAlKlC3X,KAkKuE2X,eAAiBpP,EAAK+P,eAAiBQ,EAASI,KAC3HJ,EAAO,GAAKvQ,EAAKyQ,iBAnKbhZ,KAmKuC0X,iBACpDuB,GAAsB,EAClB5J,EAAO8J,aAAY5Q,EAAKyQ,iBArKfhZ,KAqKyC0X,eAAiB,EAAxB,SArKlC1X,KAqKsE0X,eAAiBnP,EAAK+P,eAAiBQ,EAASI,KAGjID,IACFlX,EAAEwV,yBAA0B,IAzKfvX,KA6KH2Y,gBAA4C,SA7KzC3Y,KA6KsBwW,gBAA6BjO,EAAKyQ,iBAAmBzQ,EAAK+P,iBAC7F/P,EAAKyQ,iBAAmBzQ,EAAK+P,iBA9KhBtY,KAiLH4Y,gBAA4C,SAjLzC5Y,KAiLsBwW,gBAA6BjO,EAAKyQ,iBAAmBzQ,EAAK+P,iBAC7F/P,EAAKyQ,iBAAmBzQ,EAAK+P,gBAlLhBtY,KAqLH4Y,gBArLG5Y,KAqLuB2Y,iBACpCpQ,EAAKyQ,iBAAmBzQ,EAAK+P,gBAI3BjJ,EAAOoH,UAAY,EAAG,CACxB,KAAIzG,KAAKkI,IAAIY,GAAQzJ,EAAOoH,WAAalO,EAAKmO,oBAW5C,YADAnO,EAAKyQ,iBAAmBzQ,EAAK+P,gBAT7B,IAAK/P,EAAKmO,mBAMR,OALAnO,EAAKmO,oBAAqB,EAC1BrD,EAAQqC,OAASrC,EAAQgC,SACzBhC,EAAQsC,OAAStC,EAAQmC,SACzBjN,EAAKyQ,iBAAmBzQ,EAAK+P,oBAC7BjF,EAAQyF,KAjMC9Y,KAiMagY,eAAiB3E,EAAQgC,SAAWhC,EAAQqC,OAASrC,EAAQmC,SAAWnC,EAAQsC,QASvGtG,EAAO+J,eAAgB/J,EAAOoE,WAE/BpE,EAAO4H,UAAY5H,EAAO4H,SAAS3D,SA5MxBtT,KA4M0CiX,UAAY5H,EAAOgK,uBA5M7DrZ,KA6MNsZ,oBA7MMtZ,KA8MNuZ,uBA9MMvZ,KAiNJqP,OAAO4H,UAAY5H,EAAO4H,SAAS3D,SAjN/BtT,KAiNiDiX,UAjNjDjX,KAkNNiX,SAASC,cAlNHlX,KAsNRwZ,eAAejR,EAAKyQ,kBAtNZhZ,KAwNRyZ,aAAalR,EAAKyQ,4BA3MnBzQ,EAAK8N,aAAe9N,EAAK6N,aAbhBpW,KAcJkT,KAAK,oBAAqBnR,IClBxB,SAAS2X,EAAWhS,GACjC,IAAMqH,EAAS/O,KACTuI,EAAOwG,EAAOqE,gBAElB/D,EAKEN,EALFM,OACAgE,EAIEtE,EAJFsE,QACc8D,EAGZpI,EAHFqI,aACAuC,EAEE5K,EAFF4K,WAGF,GADI5K,EADFuE,QAEF,CACA,IAAIvR,EAAI2F,EASR,GARI3F,EAAE6R,gBAAe7R,EAAIA,EAAE6R,eAEvBrL,EAAK4N,qBACPpH,EAAOmE,KAAK,WAAYnR,GAG1BwG,EAAK4N,qBAAsB,GAEtB5N,EAAK4L,UAOR,OANI5L,EAAK6L,SAAW/E,EAAOqJ,YACzB3J,EAAO8J,eAAc,GAGvBtQ,EAAK6L,SAAU,OACf7L,EAAK8N,aAAc,GAKjBhH,EAAOqJ,YAAcnQ,EAAK6L,SAAW7L,EAAK4L,aAAwC,IAA1BpF,EAAO4J,iBAAqD,IAA1B5J,EAAO6J,iBACnG7J,EAAO8J,eAAc,GAIvB,IA4BIe,EA5BEC,EAAepN,IACfqN,EAAWD,EAAetR,EAAK+N,eAErC,GAAIvH,EAAOoG,WAAY,CACrB,IAAM4E,EAAWhY,EAAE0S,MAAQ1S,EAAEyS,cAAgBzS,EAAEyS,eAC/CzF,EAAOiL,mBAAmBD,GAAYA,EAAS,IAAMhY,EAAErF,QACvDqS,EAAOmE,KAAK,YAAanR,GAErB+X,EAAW,KAAOD,EAAetR,EAAK0R,cAAgB,KACxDlL,EAAOmE,KAAK,wBAAyBnR,GASzC,GALAwG,EAAK0R,cAAgBxN,IACrBF,GAAS,WACFwC,EAAOmL,YAAWnL,EAAOoG,YAAa,OAGxC5M,EAAK4L,YAAc5L,EAAK6L,UAAYrF,EAAOyH,gBAAmC,IAAjBnD,EAAQyF,MAAcvQ,EAAKyQ,mBAAqBzQ,EAAK+P,eAIrH,OAHA/P,EAAK4L,WAAY,EACjB5L,EAAK6L,SAAU,OACf7L,EAAK8N,aAAc,GAerB,GAXA9N,EAAK4L,WAAY,EACjB5L,EAAK6L,SAAU,EACf7L,EAAK8N,aAAc,EAIjBuD,EADEvK,EAAO+J,aACIjC,EAAMpI,EAAOI,WAAaJ,EAAOI,WAEhC5G,EAAKyQ,kBAGjB3J,EAAOoE,QAIX,GAAI1E,EAAOM,OAAO4H,UAAY5H,EAAO4H,SAAS3D,QAC5CvE,EAAOkI,SAASyC,WAAW,CACzBE,mBAFJ,CAWA,IAHA,IAAIO,EAAY,EACZC,EAAYrL,EAAOsL,gBAAgB,GAE9B/f,EAAI,EAAGA,EAAIqf,EAAW/c,OAAQtC,GAAKA,EAAI+U,EAAOiL,mBAAqB,EAAIjL,EAAOkL,eAAgB,CACrG,IAAMC,EAAYlgB,EAAI+U,EAAOiL,mBAAqB,EAAI,EAAIjL,EAAOkL,oBAExB,IAA9BZ,EAAWrf,EAAIkgB,GACpBZ,GAAcD,EAAWrf,IAAMsf,EAAaD,EAAWrf,EAAIkgB,KAC7DL,EAAY7f,EACZ8f,EAAYT,EAAWrf,EAAIkgB,GAAab,EAAWrf,IAE5Csf,GAAcD,EAAWrf,KAClC6f,EAAY7f,EACZ8f,EAAYT,EAAWA,EAAW/c,OAAS,GAAK+c,EAAWA,EAAW/c,OAAS,IAInF,IAAI6d,EAAmB,KACnBC,EAAkB,KAElBrL,EAAOsL,SACL5L,EAAO6L,YACTF,EAAkB3L,EAAOM,OAAOwL,SAAW9L,EAAOM,OAAOwL,QAAQvH,SAAWvE,EAAO8L,QAAU9L,EAAO8L,QAAQC,OAAOle,OAAS,EAAImS,EAAO+L,OAAOle,OAAS,EAC9ImS,EAAOgM,QAChBN,EAAmB,IAKvB,IAAMO,GAASpB,EAAaD,EAAWQ,IAAcC,EAC/CI,EAAYL,EAAY9K,EAAOiL,mBAAqB,EAAI,EAAIjL,EAAOkL,eAEzE,GAAIT,EAAWzK,EAAO4L,aAAc,CAElC,IAAK5L,EAAO6L,WAEV,YADAnM,EAAOoM,QAAQpM,EAAOiE,aAIM,SAA1BjE,EAAOyH,iBACLwE,GAAS3L,EAAO+L,gBAAiBrM,EAAOoM,QAAQ9L,EAAOsL,QAAU5L,EAAOgM,MAAQN,EAAmBN,EAAYK,GAAgBzL,EAAOoM,QAAQhB,IAGtH,SAA1BpL,EAAOyH,iBACLwE,EAAQ,EAAI3L,EAAO+L,gBACrBrM,EAAOoM,QAAQhB,EAAYK,GACE,OAApBE,GAA4BM,EAAQ,GAAKhL,KAAKkI,IAAI8C,GAAS3L,EAAO+L,gBAC3ErM,EAAOoM,QAAQT,GAEf3L,EAAOoM,QAAQhB,QAGd,CAEL,IAAK9K,EAAOgM,YAEV,YADAtM,EAAOoM,QAAQpM,EAAOiE,aAIEjE,EAAOuM,aAAevZ,EAAErF,SAAWqS,EAAOuM,WAAWC,QAAUxZ,EAAErF,SAAWqS,EAAOuM,WAAWE,QAU7GzZ,EAAErF,SAAWqS,EAAOuM,WAAWC,OACxCxM,EAAOoM,QAAQhB,EAAYK,GAE3BzL,EAAOoM,QAAQhB,IAVe,SAA1BpL,EAAOyH,gBACTzH,EAAOoM,QAA6B,OAArBV,EAA4BA,EAAmBN,EAAYK,GAG9C,SAA1BzL,EAAOyH,gBACTzH,EAAOoM,QAA4B,OAApBT,EAA2BA,EAAkBP,OCxJrD,SAASsB,IACtB,IAEEpM,EAFarP,KAEbqP,OACAtL,EAHa/D,KAGb+D,GAEF,IAAIA,GAAyB,IAAnBA,EAAGiF,YAAb,CAEIqG,EAAOqM,aAPI1b,KAQN2b,gBAIT,IACEhD,EAba3Y,KAab2Y,eACAC,EAda5Y,KAcb4Y,eACAgD,EAfa5b,KAeb4b,SAfa5b,KAkBR2Y,gBAAiB,EAlBT3Y,KAmBR4Y,gBAAiB,EAnBT5Y,KAoBRuW,aApBQvW,KAqBR6b,eArBQ7b,KAsBRuZ,uBAEuB,SAAzBlK,EAAOyM,eAA4BzM,EAAOyM,cAAgB,IAxBhD9b,KAwB6D+a,QAxB7D/a,KAwB8E4a,cAxB9E5a,KAwBqGqP,OAAO0M,eAxB5G/b,KAyBNmb,QAzBMnb,KAyBS8a,OAAOle,OAAS,EAAG,GAAG,GAAO,GAzBtCoD,KA2BNmb,QA3BMnb,KA2BSgT,YAAa,GAAG,GAAO,GA3BhChT,KA8BJgc,UA9BIhc,KA8Begc,SAASC,SA9BxBjc,KA8B0Cgc,SAASE,QA9BnDlc,KA+BNgc,SAASG,MA/BHnc,KAmCR4Y,eAAiBA,EAnCT5Y,KAoCR2Y,eAAiBA,EApCT3Y,KAsCJqP,OAAO+M,eAAiBR,IAtCpB5b,KAsCwC4b,UAtCxC5b,KAuCNqc,iBCxCI,SAASC,EAAQva,GACf/B,KACHsT,UADGtT,KAGHmV,aAHGnV,KAIFqP,OAAOkN,eAAexa,EAAEkU,iBAJtBjW,KAMFqP,OAAOmN,0BANLxc,KAMwCuT,YACnDxR,EAAEsW,kBACFtW,EAAE0a,8BCTO,SAASC,IACtB,IAEEnN,EAFavP,KAEbuP,UACA6H,EAHapX,KAGboX,aAGF,GANepX,KAIbsT,QAEF,CANetT,KAOR2c,kBAPQ3c,KAOmBmP,UAPnBnP,KASJgY,eATIhY,KAUNmP,WAAaI,EAAU5F,WAVjB3J,KAYNmP,WAAaI,EAAU9F,UAIP,IAhBVzJ,KAgBJmP,YAhBInP,KAgBoBmP,UAAY,GAhBhCnP,KAiBRsZ,oBAjBQtZ,KAkBRuZ,sBAEP,IAAMqD,EApBS5c,KAoBe0X,eApBf1X,KAoBuC2X,gBAE/B,IAAnBiF,EACY,GAvBD5c,KAyBSmP,UAzBTnP,KAyB4B2X,gBAAkBiF,KAzB9C5c,KA4BY+P,UA5BZ/P,KA6BNwZ,eAAepC,GA7BTpX,KA6BgCmP,UA7BhCnP,KA6BmDmP,WA7BnDnP,KAgCRkT,KAAK,eAhCGlT,KAgCoBmP,WAAW,IC1BhD,IAAI0N,GAAqB,EAEzB,SAASC,KAET,IAAMrV,EAAS,SAACsH,EAAQgO,GACtB,IAAMzd,EAAWF,IAEfiQ,EAMEN,EANFM,OACA2N,EAKEjO,EALFiO,YACAjZ,EAIEgL,EAJFhL,GACAwL,EAGER,EAHFQ,UACAgC,EAEExC,EAFFwC,OACAnF,EACE2C,EADF3C,QAEIrF,IAAYsI,EAAO+I,OACnB6E,EAAuB,OAAXF,EAAkB,mBAAqB,sBACnDG,EAAeH,EAErB,GAAK3Q,EAAQwE,MAIN,CACL,IAAME,IAAwC,eAAtBkM,EAAYG,QAA0B/Q,EAAQ0E,kBAAmBzB,EAAO+N,mBAAmB,CACjHC,SAAS,EACTtW,SAAS,GAEXhD,EAAGkZ,GAAWD,EAAYG,MAAOpO,EAAOoE,aAAcrC,GACtD/M,EAAGkZ,GAAWD,EAAYM,KAAMvO,EAAOmI,YAAa9K,EAAQ0E,gBAAkB,CAC5EuM,SAAS,EACTtW,WACEA,GACJhD,EAAGkZ,GAAWD,EAAYO,IAAKxO,EAAO2K,WAAY5I,GAE9CkM,EAAYQ,QACdzZ,EAAGkZ,GAAWD,EAAYQ,OAAQzO,EAAO2K,WAAY5I,QAhBvD/M,EAAGkZ,GAAWD,EAAYG,MAAOpO,EAAOoE,cAAc,GACtD7T,EAAS2d,GAAWD,EAAYM,KAAMvO,EAAOmI,YAAanQ,GAC1DzH,EAAS2d,GAAWD,EAAYO,IAAKxO,EAAO2K,YAAY,IAmBtDrK,EAAOkN,eAAiBlN,EAAOmN,2BACjCzY,EAAGkZ,GAAW,QAASlO,EAAOuN,SAAS,GAGrCjN,EAAOoE,SACTlE,EAAU0N,GAAW,SAAUlO,EAAO2N,UAIpCrN,EAAOoO,qBACT1O,EAAOmO,GAAc3L,EAAOC,KAAOD,EAAOE,QAAU,0CAA4C,wBAAyBgK,GAAU,GAEnI1M,EAAOmO,GAAc,iBAAkBzB,GAAU,IAkCtC,IC3FTiC,GAAgB,SAAC3O,EAAQM,GAC7B,OAAON,EAAO4O,MAAQtO,EAAOsO,MAAQtO,EAAOsO,KAAKC,KAAO,GCgC3C,ICnCA,IACbC,MAAM,EACN/K,UAAW,aACXgB,kBAAmB,UACnBgK,aAAc,EACdxO,MAAO,IACPmE,SAAS,EACTgK,sBAAsB,EACtBM,gBAAgB,EAChB3F,QAAQ,EACR4F,gBAAgB,EAChB1K,SAAS,EACTqD,kBAAmB,wDAEnBhF,MAAO,KACPE,OAAQ,KAER2B,gCAAgC,EAEhC/T,UAAW,KACXwe,IAAK,KAELrI,oBAAoB,EACpBE,mBAAoB,GAEpBoI,YAAY,EAEZC,gBAAgB,EAEhBC,kBAAkB,EAElBC,OAAQ,QAGR3C,iBAAa5Y,EACbwb,gBAAiB,SAEjBC,aAAc,EACdzC,cAAe,EACfvB,eAAgB,EAChBD,mBAAoB,EACpBkE,oBAAoB,EACpBzC,gBAAgB,EAChB0C,sBAAsB,EACtBC,mBAAoB,EAEpBC,kBAAmB,EAEnBC,qBAAqB,EACrBC,0BAA0B,EAE1BzC,eAAe,EAEf0C,cAAc,EAEd/F,WAAY,EACZhB,WAAY,GACZgH,eAAe,EACf1D,aAAa,EACbH,YAAY,EACZE,gBAAiB,GACjBH,aAAc,IACd7B,cAAc,EACdvC,gBAAgB,EAChBJ,UAAW,EACX0B,0BAA0B,EAC1BrB,0BAA0B,EAC1BC,+BAA+B,EAC/BS,qBAAqB,EAErBwH,mBAAmB,EAEnB7F,YAAY,EACZD,gBAAiB,IAEjBG,qBAAqB,EAErBX,YAAY,EAEZ6D,eAAe,EACfC,0BAA0B,EAC1ByC,qBAAqB,EAErBC,eAAe,EACfC,qBAAqB,EAErBzL,MAAM,EACN0L,qBAAsB,EACtBC,aAAc,KACdC,mBAAmB,EACnBC,wBAAwB,EACxBC,mBAAmB,EAEnB7E,QAAQ,EAER/B,gBAAgB,EAChBD,gBAAgB,EAChBvD,aAAc,KAEdP,WAAW,EACXP,eAAgB,oBAChBK,kBAAmB,KAEnByI,kBAAkB,EAClBqC,wBAAyB,GAEzBC,uBAAwB,UAExBC,WAAY,eACZC,gBAAiB,+BACjBC,iBAAkB,sBAClBC,0BAA2B,gCAC3BC,kBAAmB,uBACnBC,oBAAqB,yBACrBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,eAAgB,oBAChBC,wBAAyB,8BACzBC,aAAc,iBAEdC,oBAAoB,EAEpBC,cAAc,GCzHD,SAASC,GAAmBnR,EAAQoR,GACjD,OAAO,SAAsBxjB,QAAU,IAAVA,MAAM,IACjC,IAAMyjB,EAAkB1lB,OAAOqC,KAAKJ,GAAK,GACnC0jB,EAAe1jB,EAAIyjB,GAEG,iBAAjBC,GAA8C,OAAjBA,GAKpC,CAAC,aAAc,aAAc,aAAa1d,QAAQyd,IAAoB,IAAiC,IAA5BrR,EAAOqR,KACpFrR,EAAOqR,GAAmB,CACxBE,MAAM,IAIJF,KAAmBrR,GAAU,YAAasR,IAKhB,IAA5BtR,EAAOqR,KACTrR,EAAOqR,GAAmB,CACxBpN,SAAS,IAI0B,iBAA5BjE,EAAOqR,IAAmC,YAAarR,EAAOqR,KACvErR,EAAOqR,GAAiBpN,SAAU,GAG/BjE,EAAOqR,KAAkBrR,EAAOqR,GAAmB,CACtDpN,SAAS,IAEXnW,EAAOsjB,EAAkBxjB,IAjBvBE,EAAOsjB,EAAkBxjB,IAXzBE,EAAOsjB,EAAkBxjB,ICgB/B,IAAM4jB,GAAa,CACjBC,cCvBa,CACb3b,GADa,SACVsC,EAAQO,EAAS+Y,GAClB,IAAM9f,EAAOjB,KACb,IAAKiB,EAAK+f,iBAAmB/f,EAAKiZ,UAAW,OAAOjZ,EACpD,GAAuB,mBAAZ+G,EAAwB,OAAO/G,EAC1C,IAAM8b,EAASgE,EAAW,UAAY,OAKtC,OAJAtZ,EAAOzC,MAAM,KAAK1H,SAAQ,SAAAoK,GACnBzG,EAAK+f,gBAAgBtZ,KAAQzG,EAAK+f,gBAAgBtZ,GAAS,IAChEzG,EAAK+f,gBAAgBtZ,GAAOqV,GAAQ/U,MAE/B/G,GAGTggB,KAba,SAaRxZ,EAAQO,EAAS+Y,GACpB,IAAM9f,EAAOjB,KACb,IAAKiB,EAAK+f,iBAAmB/f,EAAKiZ,UAAW,OAAOjZ,EACpD,GAAuB,mBAAZ+G,EAAwB,OAAO/G,EAE1C,SAASigB,IACPjgB,EAAK6G,IAAIL,EAAQyZ,GAEbA,EAAYC,uBACPD,EAAYC,eAJO,2BAANjf,EAAM,yBAANA,EAAM,gBAO5B8F,EAAQzF,MAAMtB,EAAMiB,GAItB,OADAgf,EAAYC,eAAiBnZ,EACtB/G,EAAKkE,GAAGsC,EAAQyZ,EAAaH,IAGtCK,MAhCa,SAgCPpZ,EAAS+Y,GAEb,IADa/gB,KACHghB,iBADGhhB,KACqBka,UAAW,OADhCla,KAEb,GAAuB,mBAAZgI,EAAwB,OAFtBhI,KAGb,IAAM+c,EAASgE,EAAW,UAAY,OAMtC,OATa/gB,KAKJqhB,mBAAmBpe,QAAQ+E,GAAW,GALlChI,KAMNqhB,mBAAmBtE,GAAQ/U,GANrBhI,MAYfshB,OA7Ca,SA6CNtZ,GAEL,IADahI,KACHghB,iBADGhhB,KACqBka,UAAW,OADhCla,KAEb,IAFaA,KAEHqhB,mBAAoB,OAFjBrhB,KAGb,IAAMkK,EAHOlK,KAGMqhB,mBAAmBpe,QAAQ+E,GAM9C,OAJIkC,GAAS,GALAlK,KAMNqhB,mBAAmBnZ,OAAOgC,EAAO,GAN3BlK,MAYf8H,IA1Da,SA0DTL,EAAQO,GACV,IAAM/G,EAAOjB,KACb,OAAKiB,EAAK+f,iBAAmB/f,EAAKiZ,UAAkBjZ,EAC/CA,EAAK+f,iBACVvZ,EAAOzC,MAAM,KAAK1H,SAAQ,SAAAoK,QACD,IAAZM,EACT/G,EAAK+f,gBAAgBtZ,GAAS,GACrBzG,EAAK+f,gBAAgBtZ,IAC9BzG,EAAK+f,gBAAgBtZ,GAAOpK,SAAQ,SAACikB,EAAcrX,IAC7CqX,IAAiBvZ,GAAWuZ,EAAaJ,gBAAkBI,EAAaJ,iBAAmBnZ,IAC7F/G,EAAK+f,gBAAgBtZ,GAAOQ,OAAOgC,EAAO,SAK3CjJ,GAZ2BA,GAepCiS,KA5Ea,WA6EX,IAGIzL,EACAc,EACAlE,EALEpD,EAAOjB,KACb,IAAKiB,EAAK+f,iBAAmB/f,EAAKiZ,UAAW,OAAOjZ,EACpD,IAAKA,EAAK+f,gBAAiB,OAAO/f,EAHtB,2BAANiB,EAAM,yBAANA,EAAM,gBAQW,iBAAZA,EAAK,IAAmByB,MAAMK,QAAQ9B,EAAK,KACpDuF,EAASvF,EAAK,GACdqG,EAAOrG,EAAKyL,MAAM,EAAGzL,EAAKtF,QAC1ByH,EAAUpD,IAEVwG,EAASvF,EAAK,GAAGuF,OACjBc,EAAOrG,EAAK,GAAGqG,KACflE,EAAUnC,EAAK,GAAGmC,SAAWpD,GAG/BsH,EAAKpB,QAAQ9C,GACb,IAAMmd,EAAc7d,MAAMK,QAAQyD,GAAUA,EAASA,EAAOzC,MAAM,KAclE,OAbAwc,EAAYlkB,SAAQ,SAAAoK,GACdzG,EAAKogB,oBAAsBpgB,EAAKogB,mBAAmBzkB,QACrDqE,EAAKogB,mBAAmB/jB,SAAQ,SAAAikB,GAC9BA,EAAahf,MAAM8B,EAAnB,CAA6BqD,GAA7B,OAAuCa,OAIvCtH,EAAK+f,iBAAmB/f,EAAK+f,gBAAgBtZ,IAC/CzG,EAAK+f,gBAAgBtZ,GAAOpK,SAAQ,SAAAikB,GAClCA,EAAahf,MAAM8B,EAASkE,SAI3BtH,IDrFTwgB,OEhBa,CACblL,WCVa,WACb,IACI5E,EACAE,EACE6P,EAHS1hB,KAGI0hB,IAGjB/P,OADiC,IALpB3R,KAKGqP,OAAOsC,OAAiD,OAL3D3R,KAK0CqP,OAAOsC,MALjD3R,KAMEqP,OAAOsC,MAEd+P,EAAI,GAAGC,YAIf9P,OADkC,IAXrB7R,KAWGqP,OAAOwC,QAAmD,OAX7D7R,KAW2CqP,OAAOwC,OAXlD7R,KAYGqP,OAAOwC,OAEd6P,EAAI,GAAGE,aAGJ,IAAVjQ,GAjBW3R,KAiBWgY,gBAA6B,IAAXnG,GAjB7B7R,KAiBoDyX,eAKnE9F,EAAQA,EAAQkQ,SAASH,EAAI3X,IAAI,iBAAmB,EAAG,IAAM8X,SAASH,EAAI3X,IAAI,kBAAoB,EAAG,IACrG8H,EAASA,EAASgQ,SAASH,EAAI3X,IAAI,gBAAkB,EAAG,IAAM8X,SAASH,EAAI3X,IAAI,mBAAqB,EAAG,IACnG+X,OAAOC,MAAMpQ,KAAQA,EAAQ,GAC7BmQ,OAAOC,MAAMlQ,KAASA,EAAS,GACnC7W,OAAOkb,OA1BQlW,KA0BO,CACpB2R,QACAE,SACAmQ,KA7BahiB,KA6BAgY,eAAiBrG,EAAQE,MDnBxCgK,aEVa,WACb,IAAM9M,EAAS/O,KAEf,SAASiiB,EAAkBhmB,GACzB,OAAI8S,EAAOiJ,eACF/b,EAIF,CACL,MAAS,SACT,aAAc,cACd,iBAAkB,eAClB,cAAe,aACf,eAAgB,gBAChB,eAAgB,cAChB,gBAAiB,iBACjB,YAAe,gBACfA,GAGJ,SAASimB,EAA0BrU,EAAMsU,GACvC,OAAOlZ,WAAW4E,EAAK3N,iBAAiB+hB,EAAkBE,KAAW,GAGvE,IAAM9S,EAASN,EAAOM,OAEpBmJ,EAIEzJ,EAJFyJ,WACM4J,EAGJrT,EAHFiT,KACc7K,EAEZpI,EAFFqI,aACAiL,EACEtT,EADFsT,SAEIC,EAAYvT,EAAO8L,SAAWxL,EAAOwL,QAAQvH,QAC7CiP,EAAuBD,EAAYvT,EAAO8L,QAAQC,OAAOle,OAASmS,EAAO+L,OAAOle,OAChFke,EAAStC,EAAWpa,SAAX,IAAwB2Q,EAAOM,OAAOsQ,YAC/C6C,EAAeF,EAAYvT,EAAO8L,QAAQC,OAAOle,OAASke,EAAOle,OACnEgf,EAAW,GACTjC,EAAa,GACbU,EAAkB,GACpBoI,EAAepT,EAAOqP,mBAEE,mBAAjB+D,IACTA,EAAepT,EAAOqP,mBAAmBjkB,KAAKsU,IAGhD,IAAI2T,EAAcrT,EAAOsP,kBAEE,mBAAhB+D,IACTA,EAAcrT,EAAOsP,kBAAkBlkB,KAAKsU,IAG9C,IAAM4T,EAAyB5T,EAAO6M,SAAShf,OACzCgmB,EAA2B7T,EAAO4K,WAAW/c,OAC/C2hB,EAAelP,EAAOkP,aACtBsE,GAAiBJ,EACjBK,EAAgB,EAChB5Y,EAAQ,EAEZ,QAA0B,IAAfkY,EAAX,CAI4B,iBAAjB7D,GAA6BA,EAAatb,QAAQ,MAAQ,IACnEsb,EAAetV,WAAWsV,EAAapR,QAAQ,IAAK,KAAO,IAAMiV,GAGnErT,EAAOgU,aAAexE,EAElBpH,EAAK2D,EAAO/Q,IAAI,CAClBiZ,WAAY,GACZC,aAAc,GACdC,UAAW,KACLpI,EAAO/Q,IAAI,CACjBoZ,YAAa,GACbF,aAAc,GACdC,UAAW,KAGT7T,EAAO0M,gBAAkB1M,EAAOoE,UAClChF,EAAeM,EAAOQ,UAAW,kCAAmC,IACpEd,EAAeM,EAAOQ,UAAW,iCAAkC,KAGrE,IAOI6T,EAPEC,EAAchU,EAAOsO,MAAQtO,EAAOsO,KAAKC,KAAO,GAAK7O,EAAO4O,KAE9D0F,GACFtU,EAAO4O,KAAK2F,WAAWd,GASzB,IAJA,IAyG2B,EAzGrBe,EAAgD,SAAzBlU,EAAOyM,eAA4BzM,EAAOqM,aAAe1gB,OAAOqC,KAAKgS,EAAOqM,aAAaxX,QAAO,SAAArI,GAC3H,YAAwD,IAA1CwT,EAAOqM,YAAY7f,GAAKigB,iBACrClf,OAAS,EAEHtC,EAAI,EAAGA,EAAIkoB,EAAcloB,GAAK,EAAG,CACxC8oB,EAAY,EACZ,IAAMI,EAAQ1I,EAAOnQ,GAAGrQ,GAMxB,GAJI+oB,GACFtU,EAAO4O,KAAK8F,YAAYnpB,EAAGkpB,EAAOhB,EAAcP,GAGrB,SAAzBuB,EAAMzZ,IAAI,WAAd,CAEA,GAA6B,SAAzBsF,EAAOyM,cAA0B,CAC/ByH,IACFzI,EAAOxgB,GAAGgE,MAAM2jB,EAAkB,UAAlC,IAGF,IAAMyB,EAAczjB,iBAAiBujB,EAAM,IACrCG,EAAmBH,EAAM,GAAGllB,MAAMkI,UAClCod,EAAyBJ,EAAM,GAAGllB,MAAM4O,gBAU9C,GARIyW,IACFH,EAAM,GAAGllB,MAAMkI,UAAY,QAGzBod,IACFJ,EAAM,GAAGllB,MAAM4O,gBAAkB,QAG/BmC,EAAOyP,aACTsE,EAAYrU,EAAOiJ,eAAiBwL,EAAM3a,YAAW,GAAQ2a,EAAMta,aAAY,OAC1E,CAEL,IAAMyI,EAAQuQ,EAA0BwB,EAAa,SAC/CG,EAAc3B,EAA0BwB,EAAa,gBACrDI,EAAe5B,EAA0BwB,EAAa,iBACtDV,EAAad,EAA0BwB,EAAa,eACpDP,EAAcjB,EAA0BwB,EAAa,gBACrDK,EAAYL,EAAYxjB,iBAAiB,cAE/C,GAAI6jB,GAA2B,eAAdA,EACfX,EAAYzR,EAAQqR,EAAaG,MAC5B,CACL,MAGIK,EAAM,GAFR7B,EADF,EACEA,YAGFyB,EAAYzR,EAAQkS,EAAcC,EAAed,EAAaG,GAJ9D,EAEEna,YAEyF2Y,IAI3FgC,IACFH,EAAM,GAAGllB,MAAMkI,UAAYmd,GAGzBC,IACFJ,EAAM,GAAGllB,MAAM4O,gBAAkB0W,GAG/BvU,EAAOyP,eAAcsE,EAAYpT,KAAKgU,MAAMZ,SAEhDA,GAAahB,GAAc/S,EAAOyM,cAAgB,GAAKyC,GAAgBlP,EAAOyM,cAC1EzM,EAAOyP,eAAcsE,EAAYpT,KAAKgU,MAAMZ,IAE5CtI,EAAOxgB,KACTwgB,EAAOxgB,GAAGgE,MAAM2jB,EAAkB,UAAemB,EAAjD,MAIAtI,EAAOxgB,KACTwgB,EAAOxgB,GAAG2pB,gBAAkBb,GAG9B/I,EAAgB/X,KAAK8gB,GAEjB/T,EAAO0M,gBACT8G,EAAgBA,EAAgBO,EAAY,EAAIN,EAAgB,EAAIvE,EAC9C,IAAlBuE,GAA6B,IAANxoB,IAASuoB,EAAgBA,EAAgBT,EAAa,EAAI7D,GAC3E,IAANjkB,IAASuoB,EAAgBA,EAAgBT,EAAa,EAAI7D,GAC1DvO,KAAKkI,IAAI2K,GAAiB,OAAUA,EAAgB,GACpDxT,EAAOyP,eAAc+D,EAAgB7S,KAAKgU,MAAMnB,IAChD3Y,EAAQmF,EAAOkL,gBAAmB,GAAGqB,EAAStZ,KAAKugB,GACvDlJ,EAAWrX,KAAKugB,KAEZxT,EAAOyP,eAAc+D,EAAgB7S,KAAKgU,MAAMnB,KAC/C3Y,EAAQ8F,KAAKE,IAAInB,EAAOM,OAAOiL,mBAAoBpQ,IAAU6E,EAAOM,OAAOkL,gBAAmB,GAAGqB,EAAStZ,KAAKugB,GACpHlJ,EAAWrX,KAAKugB,GAChBA,EAAgBA,EAAgBO,EAAY7E,GAG9CxP,EAAOgU,aAAeK,EAAY7E,EAClCuE,EAAgBM,EAChBlZ,GAAS,GAWX,GARA6E,EAAOgU,YAAc/S,KAAKC,IAAIlB,EAAOgU,YAAaX,GAAcM,EAE5DvL,GAAOkL,IAA+B,UAAlBhT,EAAOgP,QAAwC,cAAlBhP,EAAOgP,SAC1D7F,EAAWzO,IAAI,CACb4H,MAAU5C,EAAOgU,YAAc1T,EAAOkP,aAAjC,OAILlP,EAAO8O,eACT3F,EAAWzO,MAAX,MACGkY,EAAkB,UAAclT,EAAOgU,YAAc1T,EAAOkP,aAD/D,SAUF,GALI8E,GACFtU,EAAO4O,KAAKuG,kBAAkBd,EAAWxH,EAAUqG,IAIhD5S,EAAO0M,eAAgB,CAG1B,IAFA,IAAMoI,EAAgB,GAEb7pB,EAAI,EAAGA,EAAIshB,EAAShf,OAAQtC,GAAK,EAAG,CAC3C,IAAI8pB,EAAiBxI,EAASthB,GAC1B+U,EAAOyP,eAAcsF,EAAiBpU,KAAKgU,MAAMI,IAEjDxI,EAASthB,IAAMyU,EAAOgU,YAAcX,GACtC+B,EAAc7hB,KAAK8hB,GAIvBxI,EAAWuI,EAEPnU,KAAKgU,MAAMjV,EAAOgU,YAAcX,GAAcpS,KAAKgU,MAAMpI,EAASA,EAAShf,OAAS,IAAM,GAC5Fgf,EAAStZ,KAAKyM,EAAOgU,YAAcX,GAMvC,GAFwB,IAApBxG,EAAShf,SAAcgf,EAAW,CAAC,IAEX,IAAxBvM,EAAOkP,aAAoB,OACvB1iB,EAAMkT,EAAOiJ,gBAAkBb,EAAM,aAAe8K,EAAkB,eAC5EnH,EAAO5W,QAAO,SAACmgB,EAAGC,GAChB,OAAKjV,EAAOoE,SAER6Q,IAAexJ,EAAOle,OAAS,KAKlCmN,MARH,MASGlO,GAAS0iB,EATZ,SAaF,GAAIlP,EAAO0M,gBAAkB1M,EAAOoP,qBAAsB,CACxD,IAAI8F,EAAgB,EACpBlK,EAAgB/c,SAAQ,SAAAknB,GACtBD,GAAiBC,GAAkBnV,EAAOkP,aAAelP,EAAOkP,aAAe,MAGjF,IAAMkG,GADNF,GAAiBlV,EAAOkP,cACQ6D,EAChCxG,EAAWA,EAASpW,KAAI,SAAAkf,GACtB,OAAIA,EAAO,GAAWjC,EAClBiC,EAAOD,EAAgBA,EAAU/B,EAC9BgC,KAIX,GAAIrV,EAAOwP,yBAA0B,CACnC,IAAI0F,EAAgB,EAMpB,GALAlK,EAAgB/c,SAAQ,SAAAknB,GACtBD,GAAiBC,GAAkBnV,EAAOkP,aAAelP,EAAOkP,aAAe,OAEjFgG,GAAiBlV,EAAOkP,cAEJ6D,EAAY,CAC9B,IAAMuC,GAAmBvC,EAAamC,GAAiB,EACvD3I,EAASte,SAAQ,SAAConB,EAAME,GACtBhJ,EAASgJ,GAAaF,EAAOC,KAE/BhL,EAAWrc,SAAQ,SAAConB,EAAME,GACxBjL,EAAWiL,GAAaF,EAAOC,MAYrC,GAPA3pB,OAAOkb,OAAOnH,EAAQ,CACpB+L,SACAc,WACAjC,aACAU,oBAGEhL,EAAO0M,gBAAkB1M,EAAOoE,UAAYpE,EAAOoP,qBAAsB,CAC3EhQ,EAAeM,EAAOQ,UAAW,mCAAuCqM,EAAS,GAAnE,MACdnN,EAAeM,EAAOQ,UAAW,iCAAqCR,EAAOiT,KAAO,EAAI3H,EAAgBA,EAAgBzd,OAAS,GAAK,EAAxH,MACd,IAAMioB,GAAiB9V,EAAO6M,SAAS,GACjCkJ,GAAmB/V,EAAO4K,WAAW,GAC3C5K,EAAO6M,SAAW7M,EAAO6M,SAASpW,KAAI,SAAAuf,GAAC,OAAIA,EAAIF,KAC/C9V,EAAO4K,WAAa5K,EAAO4K,WAAWnU,KAAI,SAAAuf,GAAC,OAAIA,EAAID,KAoBrD,GAjBItC,IAAiBD,GACnBxT,EAAOmE,KAAK,sBAGV0I,EAAShf,SAAW+lB,IAClB5T,EAAOM,OAAO+M,eAAerN,EAAOsN,gBACxCtN,EAAOmE,KAAK,yBAGVyG,EAAW/c,SAAWgmB,GACxB7T,EAAOmE,KAAK,0BAGV7D,EAAOgK,qBACTtK,EAAOiW,uBAGJ1C,GAAcjT,EAAOoE,SAA8B,UAAlBpE,EAAOgP,QAAwC,SAAlBhP,EAAOgP,QAAoB,CAC5F,IAAM4G,EAAyB5V,EAAOqQ,uBAAb,kBACnBwF,EAA6BnW,EAAO2S,IAAI7b,SAASof,GAEnDzC,GAAgBnT,EAAOoQ,wBACpByF,GAA4BnW,EAAO2S,IAAIrc,SAAS4f,GAC5CC,GACTnW,EAAO2S,IAAI/b,YAAYsf,MFhT3BE,iBGXa,SAA0B7V,GACvC,IAIIhV,EAJEyU,EAAS/O,KACTolB,EAAe,GACf9C,EAAYvT,EAAO8L,SAAW9L,EAAOM,OAAOwL,QAAQvH,QACtD+R,EAAY,EAGK,iBAAV/V,EACTP,EAAOwJ,cAAcjJ,IACF,IAAVA,GACTP,EAAOwJ,cAAcxJ,EAAOM,OAAOC,OAGrC,IAAMgW,EAAkB,SAAApb,GACtB,OAAIoY,EACKvT,EAAO+L,OAAO5W,QAAO,SAAAH,GAAE,OAAI8d,SAAS9d,EAAGqC,aAAa,2BAA4B,MAAQ8D,KAAO,GAGjG6E,EAAO+L,OAAOnQ,GAAGT,GAAO,IAIjC,GAAoC,SAAhC6E,EAAOM,OAAOyM,eAA4B/M,EAAOM,OAAOyM,cAAgB,EAC1E,GAAI/M,EAAOM,OAAO0M,gBACfhN,EAAOwW,eAAiBphB,EAAE,KAAK8F,MAAK,SAAAuZ,GACnC4B,EAAa9iB,KAAKkhB,WAGpB,IAAKlpB,EAAI,EAAGA,EAAI0V,KAAKwV,KAAKzW,EAAOM,OAAOyM,eAAgBxhB,GAAK,EAAG,CAC9D,IAAM4P,EAAQ6E,EAAOiE,YAAc1Y,EACnC,GAAI4P,EAAQ6E,EAAO+L,OAAOle,SAAW0lB,EAAW,MAChD8C,EAAa9iB,KAAKgjB,EAAgBpb,SAItCkb,EAAa9iB,KAAKgjB,EAAgBvW,EAAOiE,cAI3C,IAAK1Y,EAAI,EAAGA,EAAI8qB,EAAaxoB,OAAQtC,GAAK,EACxC,QAA+B,IAApB8qB,EAAa9qB,GAAoB,CAC1C,IAAMuX,EAASuT,EAAa9qB,GAAG6O,aAC/Bkc,EAAYxT,EAASwT,EAAYxT,EAASwT,GAK1CA,GAA2B,IAAdA,IAAiBtW,EAAOyJ,WAAWzO,IAAI,SAAasb,EAAnC,OHnClCL,mBIba,WAIb,IAHA,IACMlK,EADS9a,KACO8a,OAEbxgB,EAAI,EAAGA,EAAIwgB,EAAOle,OAAQtC,GAAK,EACtCwgB,EAAOxgB,GAAGmrB,kBAJGzlB,KAIwBgY,eAAiB8C,EAAOxgB,GAAGorB,WAAa5K,EAAOxgB,GAAGqrB,WJSzFC,qBKba,SAA8BzW,QAAyC,IAAzCA,MAAYnP,MAAQA,KAAKmP,WAAa,GACjF,IACME,EADSrP,KACOqP,OAEpByL,EAHa9a,KAGb8a,OACc3D,EAJDnX,KAIboX,aACAwE,EALa5b,KAKb4b,SAEF,GAAsB,IAAlBd,EAAOle,OAAX,MAC2C,IAAhCke,EAAO,GAAG2K,mBARNzlB,KAQgDglB,qBAC/D,IAAIa,GAAgB1W,EAChBgI,IAAK0O,EAAe1W,GAExB2L,EAAOnV,YAAY0J,EAAO0Q,mBAZX/f,KAaR8lB,qBAAuB,GAbf9lB,KAcRulB,cAAgB,GAEvB,IAAK,IAAIjrB,EAAI,EAAGA,EAAIwgB,EAAOle,OAAQtC,GAAK,EAAG,CACzC,IAAMkpB,EAAQ1I,EAAOxgB,GACjByrB,EAAcvC,EAAMiC,kBAEpBpW,EAAOoE,SAAWpE,EAAO0M,iBAC3BgK,GAAejL,EAAO,GAAG2K,mBAG3B,IAAMO,GAAiBH,GAAgBxW,EAAO0M,eAxBjC/b,KAwByD2X,eAAiB,GAAKoO,IAAgBvC,EAAMS,gBAAkB5U,EAAOkP,cACrI0H,GAAyBJ,EAAejK,EAAS,IAAMvM,EAAO0M,eAzBvD/b,KAyB+E2X,eAAiB,GAAKoO,IAAgBvC,EAAMS,gBAAkB5U,EAAOkP,cAC3J2H,IAAgBL,EAAeE,GAC/BI,EAAaD,EA3BNlmB,KA2B2Bqa,gBAAgB/f,IACtC4rB,GAAe,GAAKA,EA5BzBlmB,KA4B8CgiB,KAAO,GAAKmE,EAAa,GAAKA,GA5B5EnmB,KA4BiGgiB,MAAQkE,GAAe,GAAKC,GA5B7HnmB,KA4BkJgiB,QA5BlJhiB,KA+BJulB,cAAcjjB,KAAKkhB,GA/BfxjB,KAgCJ8lB,qBAAqBxjB,KAAKhI,GACjCwgB,EAAOnQ,GAAGrQ,GAAG+K,SAASgK,EAAO0Q,oBAG/ByD,EAAMzT,SAAWoH,GAAO6O,EAAgBA,EACxCxC,EAAM4C,iBAAmBjP,GAAO8O,EAAwBA,EArC3CjmB,KAwCRulB,cAAgBphB,EAxCRnE,KAwCiBulB,iBL3BhC/L,eMfa,SAAwBrK,GAGrC,QAAyB,IAAdA,EAA2B,CACpC,IAAMkX,EAHOrmB,KAGaoX,cAAgB,EAAI,EAE9CjI,EALanP,WAKgBmP,WALhBnP,KAKoCmP,UAAYkX,GAAc,EAG7E,IAAMhX,EARSrP,KAQOqP,OAChBuN,EATS5c,KASe0X,eATf1X,KASuC2X,eAEpD5H,EAXa/P,KAWb+P,SACA6K,EAZa5a,KAYb4a,YACAG,EAba/a,KAab+a,MAEIuL,EAAe1L,EACf2L,EAASxL,EAEQ,IAAnB6B,GACF7M,EAAW,EACX6K,GAAc,EACdG,GAAQ,IAGRH,GADA7K,GAAYZ,EAvBCnP,KAuBkB2X,gBAAkBiF,IACvB,EAC1B7B,EAAQhL,GAAY,GAGtB/U,OAAOkb,OA5BQlW,KA4BO,CACpB+P,WACA6K,cACAG,WAEE1L,EAAOgK,qBAAuBhK,EAAO0M,gBAAkB1M,EAAO6O,aAjCnDle,KAiCsE4lB,qBAAqBzW,GAEtGyL,IAAgB0L,GAnCLtmB,KAoCNkT,KAAK,yBAGV6H,IAAUwL,GAvCCvmB,KAwCNkT,KAAK,oBAGVoT,IAAiB1L,GAAe2L,IAAWxL,IA3ChC/a,KA4CNkT,KAAK,YA5CClT,KA+CRkT,KAAK,WAAYnD,INhCxBwJ,oBOhBa,WACb,IAUIiN,EARF1L,EAFa9a,KAEb8a,OACAzL,EAHarP,KAGbqP,OACAmJ,EAJaxY,KAIbwY,WACAxF,EALahT,KAKbgT,YACAyT,EANazmB,KAMbymB,UAEInE,EARStiB,KAQU6a,SAAWxL,EAAOwL,QAAQvH,QACnDwH,EAAOnV,YAAe0J,EAAOwQ,iBAA7B,IAAiDxQ,EAAO4Q,eAAxD,IAA0E5Q,EAAO8Q,eAAjF,IAAmG9Q,EAAOyQ,0BAA1G,IAAuIzQ,EAAO6Q,wBAA9I,IAAyK7Q,EAAO+Q,0BAI9KoG,EADElE,EAZWtiB,KAaQwY,WAAWzM,KAAlB,IAA2BsD,EAAOsQ,WAAlC,6BAAyE3M,EAAzE,MAEA8H,EAAOnQ,GAAGqI,IAId3N,SAASgK,EAAOwQ,kBAExBxQ,EAAOqE,OAEL8S,EAAY3gB,SAASwJ,EAAO2Q,qBAC9BxH,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,SAAkDtQ,EAAO2Q,oBAAzD,8BAA0GyG,EAA1G,MAAyHphB,SAASgK,EAAOyQ,2BAEzItH,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,IAA6CtQ,EAAO2Q,oBAApD,6BAAoGyG,EAApG,MAAmHphB,SAASgK,EAAOyQ,4BAKvI,IAAI4G,EAAYF,EAAYlb,QAAZ,IAAwB+D,EAAOsQ,YAAchV,GAAG,GAAGtF,SAASgK,EAAO4Q,gBAE/E5Q,EAAOqE,MAA6B,IAArBgT,EAAU9pB,SAC3B8pB,EAAY5L,EAAOnQ,GAAG,IACZtF,SAASgK,EAAO4Q,gBAI5B,IAAI0G,EAAYH,EAAY9a,QAAZ,IAAwB2D,EAAOsQ,YAAchV,GAAG,GAAGtF,SAASgK,EAAO8Q,gBAE/E9Q,EAAOqE,MAA6B,IAArBiT,EAAU/pB,SAC3B+pB,EAAY7L,EAAOnQ,IAAI,IACbtF,SAASgK,EAAO8Q,gBAGxB9Q,EAAOqE,OAELgT,EAAU7gB,SAASwJ,EAAO2Q,qBAC5BxH,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,SAAkDtQ,EAAO2Q,oBAAzD,8BAA0G0G,EAAUxgB,KAAK,2BAAzH,MAAyJb,SAASgK,EAAO6Q,yBAEzK1H,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,IAA6CtQ,EAAO2Q,oBAApD,6BAAoG0G,EAAUxgB,KAAK,2BAAnH,MAAmJb,SAASgK,EAAO6Q,yBAGjKyG,EAAU9gB,SAASwJ,EAAO2Q,qBAC5BxH,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,SAAkDtQ,EAAO2Q,oBAAzD,8BAA0G2G,EAAUzgB,KAAK,2BAAzH,MAAyJb,SAASgK,EAAO+Q,yBAEzK5H,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,IAA6CtQ,EAAO2Q,oBAApD,6BAAoG2G,EAAUzgB,KAAK,2BAAnH,MAAmJb,SAASgK,EAAO+Q,0BAzDxJpgB,KA6DR4mB,qBP7CPtN,kBQjBa,SAA2BuN,GACxC,IAWIjC,EAVEzV,EADSnP,KACUoX,aADVpX,KACgCmP,WADhCnP,KACoDmP,UAEjEwK,EAHa3Z,KAGb2Z,WACAiC,EAJa5b,KAIb4b,SACAvM,EALarP,KAKbqP,OACa4D,EANAjT,KAMbgT,YACW8T,EAPE9mB,KAObymB,UACWM,EARE/mB,KAQb4kB,UAEE5R,EAAc6T,EAGlB,QAA2B,IAAhB7T,EAA6B,CACtC,IAAK,IAAI1Y,EAAI,EAAGA,EAAIqf,EAAW/c,OAAQtC,GAAK,OACT,IAAtBqf,EAAWrf,EAAI,GACpB6U,GAAawK,EAAWrf,IAAM6U,EAAYwK,EAAWrf,EAAI,IAAMqf,EAAWrf,EAAI,GAAKqf,EAAWrf,IAAM,EACtG0Y,EAAc1Y,EACL6U,GAAawK,EAAWrf,IAAM6U,EAAYwK,EAAWrf,EAAI,KAClE0Y,EAAc1Y,EAAI,GAEX6U,GAAawK,EAAWrf,KACjC0Y,EAAc1Y,GAKd+U,EAAOuP,sBACL5L,EAAc,QAA4B,IAAhBA,KAA6BA,EAAc,GAI7E,GAAI4I,EAAS3Y,QAAQkM,IAAc,EACjCyV,EAAYhJ,EAAS3Y,QAAQkM,OACxB,CACL,IAAM6X,EAAOhX,KAAKE,IAAIb,EAAOiL,mBAAoBtH,GACjD4R,EAAYoC,EAAOhX,KAAKgU,OAAOhR,EAAcgU,GAAQ3X,EAAOkL,gBAK9D,GAFIqK,GAAahJ,EAAShf,SAAQgoB,EAAYhJ,EAAShf,OAAS,GAE5DoW,IAAgBC,EAApB,CAUA,IAAMwT,EAAY5E,SAnDH7hB,KAmDmB8a,OAAOnQ,GAAGqI,GAAa9M,KAAK,4BAA8B8M,EAAa,IACzGhY,OAAOkb,OApDQlW,KAoDO,CACpB4kB,YACA6B,YACAxT,gBACAD,gBAxDahT,KA0DRkT,KAAK,qBA1DGlT,KA2DRkT,KAAK,mBAER4T,IAAsBL,GA7DXzmB,KA8DNkT,KAAK,oBA9DClT,KAiEJinB,aAjEIjnB,KAiEkBqP,OAAOiR,qBAjEzBtgB,KAkENkT,KAAK,oBAxBR0R,IAAcmC,IA1CL/mB,KA2CJ4kB,UAAYA,EA3CR5kB,KA4CJkT,KAAK,qBR3BhB8G,mBSjBa,SAA4BjY,GACzC,IAIIuiB,EAHEjV,EADSrP,KACOqP,OAChBmU,EAAQrf,EAAEpC,GAAG+J,QAAL,IAAiBuD,EAAOsQ,YAAc,GAChDuH,GAAa,EAGjB,GAAI1D,EACF,IAAK,IAAIlpB,EAAI,EAAGA,EAPH0F,KAOc8a,OAAOle,OAAQtC,GAAK,EAC7C,GARW0F,KAQA8a,OAAOxgB,KAAOkpB,EAAO,CAC9B0D,GAAa,EACb5C,EAAahqB,EACb,MAKN,IAAIkpB,IAAS0D,EAWX,OA3BalnB,KAyBNmnB,kBAAerkB,OAzBT9C,KA0BNonB,kBAAetkB,GA1BT9C,KAiBNmnB,aAAe3D,EAjBTxjB,KAmBF6a,SAnBE7a,KAmBgBqP,OAAOwL,QAAQvH,QAnB/BtT,KAoBJonB,aAAevF,SAAS1d,EAAEqf,GAAOtd,KAAK,2BAA4B,IApB9DlG,KAsBJonB,aAAe9C,EAQtBjV,EAAO4P,0BAA+Cnc,IA9B3C9C,KA8B0BonB,cA9B1BpnB,KA8B+DonB,eA9B/DpnB,KA8BuFgT,aA9BvFhT,KA+BNif,wBXPT9P,UYrBa,CACbzC,aCLa,SAA4BC,QAAwC,IAAxCA,MAAO3M,KAAKgY,eAAiB,IAAM,KAC5E,IAEE3I,EAFarP,KAEbqP,OACc8H,EAHDnX,KAGboX,aACAjI,EAJanP,KAIbmP,UACAqJ,EALaxY,KAKbwY,WAGF,GAAInJ,EAAO+O,iBACT,OAAOjH,GAAOhI,EAAYA,EAG5B,GAAIE,EAAOoE,QACT,OAAOtE,EAGT,IAAI6J,EAAmBtM,EAAa8L,EAAW,GAAI7L,GAEnD,OADIwK,IAAK6B,GAAoBA,GACtBA,GAAoB,GDb3BS,aEPa,SAAsBtK,EAAWkY,GAC9C,IAEgBlQ,EAFDnX,KAEboX,aACA/H,EAHarP,KAGbqP,OACAmJ,EAJaxY,KAIbwY,WACAjJ,EALavP,KAKbuP,UACAQ,EANa/P,KAMb+P,SAEEuX,EAAI,EACJC,EAAI,EATOvnB,KAYJgY,eACTsP,EAAInQ,GAAOhI,EAAYA,EAEvBoY,EAAIpY,EAGFE,EAAOyP,eACTwI,EAAItX,KAAKgU,MAAMsD,GACfC,EAAIvX,KAAKgU,MAAMuD,IAGblY,EAAOoE,QACTlE,EAxBavP,KAwBIgY,eAAiB,aAAe,aAxBpChY,KAwB0DgY,gBAAkBsP,GAAKC,EACpFlY,EAAO+O,kBACjB5F,EAAWhS,UAAX,eAAoC8gB,EAApC,OAA4CC,EAA5C,YA1BavnB,KA6BR2c,kBA7BQ3c,KA6BmBmP,UA7BnBnP,KA8BRmP,UA9BQnP,KA8BWgY,eAAiBsP,EAAIC,EAG/C,IAAM3K,EAjCS5c,KAiCe0X,eAjCf1X,KAiCuC2X,gBAE/B,IAAnBiF,EACY,GAECzN,EAtCFnP,KAsCqB2X,gBAAkBiF,KAGlC7M,GAzCL/P,KA0CNwZ,eAAerK,GA1CTnP,KA6CRkT,KAAK,eA7CGlT,KA6CoBmP,UAAWkY,IFtC9C1P,aGRa,WACb,OAAQ3X,KAAK4b,SAAS,IHQtBlE,aITa,WACb,OAAQ1X,KAAK4b,SAAS5b,KAAK4b,SAAShf,OAAS,IJS7C4qB,YKTa,SAAqBrY,EAAeG,EAA2BuD,EAAqB4U,EAAwBC,QAAU,IAAjGvY,MAAY,QAAqF,IAAlFG,MAAQtP,KAAKqP,OAAOC,YAA8D,IAAvDuD,OAAe,QAAwC,IAAlC4U,OAAkB,GACnH,IAAM1Y,EAAS/O,KAEbqP,EAEEN,EAFFM,OACAE,EACER,EADFQ,UAGF,GAAIR,EAAOwE,WAAalE,EAAOmE,+BAC7B,OAAO,EAGT,IAEImU,EAFEhQ,EAAe5I,EAAO4I,eACtBD,EAAe3I,EAAO2I,eAM5B,GAJiDiQ,EAA7CF,GAAmBtY,EAAYwI,EAA6BA,EAAsB8P,GAAmBtY,EAAYuI,EAA6BA,EAAiCvI,EAEnLJ,EAAOyK,eAAemO,GAElBtY,EAAOoE,QAAS,CAClB,IAAMmU,EAAM7Y,EAAOiJ,eAEnB,GAAc,IAAV1I,EACFC,EAAUqY,EAAM,aAAe,cAAgBD,MAC1C,OACL,IAAK5Y,EAAO3C,QAAQsE,aAMlB,OALA7B,EAAqB,CACnBE,SACAC,gBAAiB2Y,EACjB1Y,KAAM2Y,EAAM,OAAS,SAEhB,EAGTrY,EAAUgB,WAAV,MACGqX,EAAM,OAAS,QAASD,EAD3B,EAEEE,SAAU,SAFZ,IAMF,OAAO,EA2CT,OAxCc,IAAVvY,GACFP,EAAOwJ,cAAc,GACrBxJ,EAAO0K,aAAakO,GAEhB9U,IACF9D,EAAOmE,KAAK,wBAAyB5D,EAAOoY,GAC5C3Y,EAAOmE,KAAK,oBAGdnE,EAAOwJ,cAAcjJ,GACrBP,EAAO0K,aAAakO,GAEhB9U,IACF9D,EAAOmE,KAAK,wBAAyB5D,EAAOoY,GAC5C3Y,EAAOmE,KAAK,oBAGTnE,EAAOwE,YACVxE,EAAOwE,WAAY,EAEdxE,EAAO+Y,oCACV/Y,EAAO+Y,kCAAoC,SAAuB/lB,GAC3DgN,IAAUA,EAAOmL,WAClBnY,EAAErF,SAAWsD,OACjB+O,EAAOyJ,WAAW,GAAG9a,oBAAoB,gBAAiBqR,EAAO+Y,mCACjE/Y,EAAOyJ,WAAW,GAAG9a,oBAAoB,sBAAuBqR,EAAO+Y,mCACvE/Y,EAAO+Y,kCAAoC,YACpC/Y,EAAO+Y,kCAEVjV,GACF9D,EAAOmE,KAAK,oBAKlBnE,EAAOyJ,WAAW,GAAG/a,iBAAiB,gBAAiBsR,EAAO+Y,mCAC9D/Y,EAAOyJ,WAAW,GAAG/a,iBAAiB,sBAAuBsR,EAAO+Y,sCAIjE,IjBxDPrhB,WkBxBa,CACb8R,cCJa,SAAuB7R,EAAU2gB,GAC/BrnB,KAEHqP,OAAOoE,SAFJzT,KAGNwY,WAAW/R,WAAWC,GAHhB1G,KAMRkT,KAAK,gBAAiBxM,EAAU2gB,IDFvCU,gBEJa,SAAyBlV,EAAqBC,QAAW,IAAhCD,OAAe,GACrD,IAEExD,EAFarP,KAEbqP,OAEEA,EAAOoE,UAEPpE,EAAO6O,YANIle,KAONmlB,mBAGTvS,EAAe,CACb7D,OAXa/O,KAYb6S,eACAC,YACAC,KAAM,YFVRrK,cGLa,SAAuBmK,EAAqBC,QAAW,IAAhCD,OAAe,GACnD,IAEExD,EAFarP,KAEbqP,OAFarP,KAIRuT,WAAY,EACflE,EAAOoE,UALIzT,KAMRuY,cAAc,GACrB3F,EAAe,CACb7D,OARa/O,KASb6S,eACAC,YACAC,KAAM,WrBeRyQ,MsBrBa,CACbrI,QCPa,SAAiBjR,EAAWoF,EAA2BuD,EAAqB6U,EAAUM,GACnG,QAD4G,IAA9E9d,MAAQ,QAAsE,IAAnEoF,MAAQtP,KAAKqP,OAAOC,YAA+C,IAAxCuD,OAAe,GAC9D,iBAAV3I,GAAuC,iBAAVA,EACtC,MAAM,IAAI+d,MAAJ,kFAA4F/d,EAA5F,YAGR,GAAqB,iBAAVA,EAAoB,CAK7B,IAAMge,EAAgBrG,SAAS3X,EAAO,IAStC,IAFsBie,SAASD,GAG7B,MAAM,IAAID,MAAJ,sEAAgF/d,EAAhF,YAKRA,EAAQge,EAGV,IAAMnZ,EAAS/O,KACXskB,EAAapa,EACboa,EAAa,IAAGA,EAAa,GACjC,IACEjV,EAQEN,EARFM,OACAuM,EAOE7M,EAPF6M,SACAjC,EAME5K,EANF4K,WACA1G,EAKElE,EALFkE,cACAD,EAIEjE,EAJFiE,YACcmE,EAGZpI,EAHFqI,aACA7H,EAEER,EAFFQ,UACA+D,EACEvE,EADFuE,QAGF,GAAIvE,EAAOwE,WAAalE,EAAOmE,iCAAmCF,IAAYoU,IAAaM,EACzF,OAAO,EAGT,IAAMhB,EAAOhX,KAAKE,IAAInB,EAAOM,OAAOiL,mBAAoBgK,GACpDM,EAAYoC,EAAOhX,KAAKgU,OAAOM,EAAa0C,GAAQjY,EAAOM,OAAOkL,gBAClEqK,GAAahJ,EAAShf,SAAQgoB,EAAYhJ,EAAShf,OAAS,GAChE,IAqCIkW,EArCE3D,GAAayM,EAASgJ,GAE5B,GAAIvV,EAAOuP,oBACT,IAAK,IAAItkB,EAAI,EAAGA,EAAIqf,EAAW/c,OAAQtC,GAAK,EAAG,CAC7C,IAAM8tB,GAAuBpY,KAAKgU,MAAkB,IAAZ7U,GAClCkZ,EAAiBrY,KAAKgU,MAAsB,IAAhBrK,EAAWrf,IACvCguB,EAAqBtY,KAAKgU,MAA0B,IAApBrK,EAAWrf,EAAI,SAEpB,IAAtBqf,EAAWrf,EAAI,GACpB8tB,GAAuBC,GAAkBD,EAAsBE,GAAsBA,EAAqBD,GAAkB,EAC9H/D,EAAahqB,EACJ8tB,GAAuBC,GAAkBD,EAAsBE,IACxEhE,EAAahqB,EAAI,GAEV8tB,GAAuBC,IAChC/D,EAAahqB,GAMnB,GAAIyU,EAAOkY,aAAe3C,IAAetR,EAAa,CACpD,IAAKjE,EAAO4J,gBAAkBxJ,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAO4I,eAC/E,OAAO,EAGT,IAAK5I,EAAO6J,gBAAkBzJ,EAAYJ,EAAOI,WAAaA,EAAYJ,EAAO2I,iBAC1E1E,GAAe,KAAOsR,EAAY,OAAO,EAalD,GATIA,KAAgBrR,GAAiB,IAAMJ,GACzC9D,EAAOmE,KAAK,0BAIdnE,EAAOyK,eAAerK,GAEQ2D,EAA1BwR,EAAatR,EAAyB,OAAgBsR,EAAatR,EAAyB,OAAwB,QAEpHmE,IAAQhI,IAAcJ,EAAOI,YAAcgI,GAAOhI,IAAcJ,EAAOI,UAkBzE,OAjBAJ,EAAOuK,kBAAkBgL,GAErBjV,EAAO6O,YACTnP,EAAOoW,mBAGTpW,EAAOwK,sBAEe,UAAlBlK,EAAOgP,QACTtP,EAAO0K,aAAatK,GAGJ,UAAd2D,IACF/D,EAAOgZ,gBAAgBlV,EAAcC,GACrC/D,EAAOrG,cAAcmK,EAAcC,KAG9B,EAGT,GAAIzD,EAAOoE,QAAS,CAClB,IAAMmU,EAAM7Y,EAAOiJ,eACbxc,EAAI2b,EAAMhI,GAAaA,EAE7B,GAAc,IAAVG,EAAa,CACf,IAAMgT,EAAYvT,EAAO8L,SAAW9L,EAAOM,OAAOwL,QAAQvH,QAEtDgP,IACFvT,EAAOQ,UAAUjR,MAAMkR,eAAiB,OACxCT,EAAOwZ,mBAAoB,GAG7BhZ,EAAUqY,EAAM,aAAe,aAAepsB,EAE1C8mB,GACF7hB,uBAAsB,WACpBsO,EAAOQ,UAAUjR,MAAMkR,eAAiB,GACxCT,EAAOyZ,yBAA0B,SAGhC,OACL,IAAKzZ,EAAO3C,QAAQsE,aAMlB,OALA7B,EAAqB,CACnBE,SACAC,eAAgBxT,EAChByT,KAAM2Y,EAAM,OAAS,SAEhB,EAGTrY,EAAUgB,WAAV,MACGqX,EAAM,OAAS,OAAQpsB,EAD1B,EAEEqsB,SAAU,SAFZ,IAMF,OAAO,EA+BT,OA5BA9Y,EAAOwJ,cAAcjJ,GACrBP,EAAO0K,aAAatK,GACpBJ,EAAOuK,kBAAkBgL,GACzBvV,EAAOwK,sBACPxK,EAAOmE,KAAK,wBAAyB5D,EAAOoY,GAC5C3Y,EAAOgZ,gBAAgBlV,EAAcC,GAEvB,IAAVxD,EACFP,EAAOrG,cAAcmK,EAAcC,GACzB/D,EAAOwE,YACjBxE,EAAOwE,WAAY,EAEdxE,EAAO0Z,gCACV1Z,EAAO0Z,8BAAgC,SAAuB1mB,GACvDgN,IAAUA,EAAOmL,WAClBnY,EAAErF,SAAWsD,OACjB+O,EAAOyJ,WAAW,GAAG9a,oBAAoB,gBAAiBqR,EAAO0Z,+BACjE1Z,EAAOyJ,WAAW,GAAG9a,oBAAoB,sBAAuBqR,EAAO0Z,+BACvE1Z,EAAO0Z,8BAAgC,YAChC1Z,EAAO0Z,8BACd1Z,EAAOrG,cAAcmK,EAAcC,MAIvC/D,EAAOyJ,WAAW,GAAG/a,iBAAiB,gBAAiBsR,EAAO0Z,+BAC9D1Z,EAAOyJ,WAAW,GAAG/a,iBAAiB,sBAAuBsR,EAAO0Z,iCAG/D,GDzKPC,YETa,SAAqBxe,EAAWoF,EAA2BuD,EAAqB6U,GAC7F,QADuG,IAArExd,MAAQ,QAA6D,IAA1DoF,MAAQtP,KAAKqP,OAAOC,YAAsC,IAA/BuD,OAAe,GAClE,iBAAV3I,EAAoB,CAK7B,IAAMge,EAAgBrG,SAAS3X,EAAO,IAStC,IAFsBie,SAASD,GAG7B,MAAM,IAAID,MAAJ,sEAAgF/d,EAAhF,YAKRA,EAAQge,EAGV,IACIS,EAAWze,EAMf,OAPelK,KAGJqP,OAAOqE,OAChBiV,GAJa3oB,KAIMqf,cAJNrf,KAODmb,QAAQwN,EAAUrZ,EAAOuD,EAAc6U,IFrBrDkB,UGTa,SAAmBtZ,EAA2BuD,EAAqB6U,QAAU,IAA1DpY,MAAQtP,KAAKqP,OAAOC,YAAsC,IAA/BuD,OAAe,GAC1E,IAEEU,EAFavT,KAEbuT,UACAD,EAHatT,KAGbsT,QACAjE,EAJarP,KAIbqP,OAEF,IAAKiE,EAAS,OANCtT,KAOf,IAAI6oB,EAAWxZ,EAAOkL,eAEO,SAAzBlL,EAAOyM,eAAsD,IAA1BzM,EAAOkL,gBAAwBlL,EAAOmP,qBAC3EqK,EAAW7Y,KAAKC,IAVHjQ,KAUc8oB,qBAAqB,WAAW,GAAO,IAGpE,IAAMtO,EAbSxa,KAaUgT,YAAc3D,EAAOiL,mBAAqB,EAAIuO,EAEvE,GAAIxZ,EAAOqE,KAAM,CACf,GAAIH,GAAalE,EAAOmQ,kBAAmB,OAAO,EAhBrCxf,KAiBN2T,UAjBM3T,KAmBN+oB,YAnBM/oB,KAmBewY,WAAW,GAAGhP,WAG5C,OAAI6F,EAAOsL,QAtBI3a,KAsBa+a,MAtBb/a,KAuBCmb,QAAQ,EAAG7L,EAAOuD,EAAc6U,GAvBjC1nB,KA0BDmb,QA1BCnb,KA0BcgT,YAAcwH,EAAWlL,EAAOuD,EAAc6U,IHjB3EsB,UIVa,SAAmB1Z,EAA2BuD,EAAqB6U,QAAU,IAA1DpY,MAAQtP,KAAKqP,OAAOC,YAAsC,IAA/BuD,OAAe,GAC1E,IAEExD,EAFarP,KAEbqP,OACAkE,EAHavT,KAGbuT,UACAqI,EAJa5b,KAIb4b,SACAjC,EALa3Z,KAKb2Z,WACAvC,EANapX,KAMboX,aAGF,IATepX,KAObsT,QAEY,OATCtT,KAWf,GAAIqP,EAAOqE,KAAM,CACf,GAAIH,GAAalE,EAAOmQ,kBAAmB,OAAO,EAZrCxf,KAaN2T,UAbM3T,KAeN+oB,YAfM/oB,KAeewY,WAAW,GAAGhP,WAK5C,SAASyf,EAAUC,GACjB,OAAIA,EAAM,GAAWlZ,KAAKgU,MAAMhU,KAAKkI,IAAIgR,IAClClZ,KAAKgU,MAAMkF,GAGpB,IAKMC,EALAf,EAAsBa,EAPV7R,EAlBHpX,KAkByBmP,WAlBzBnP,KAkB6CmP,WAQtDia,EAAqBxN,EAASpW,KAAI,SAAA0jB,GAAG,OAAID,EAAUC,MACrDG,EAAWzN,EAASwN,EAAmBnmB,QAAQmlB,GAAuB,QAElD,IAAbiB,GAA4Bha,EAAOoE,UAE5CmI,EAASte,SAAQ,SAAConB,EAAME,GAClBwD,GAAuB1D,IAEzByE,EAAgBvE,WAIS,IAAlBuE,IACTE,EAAWzN,EAASuN,EAAgB,EAAIA,EAAgB,EAAIA,KAIhE,IAAIG,EAAY,EAYhB,QAVwB,IAAbD,KACTC,EAAY3P,EAAW1W,QAAQomB,IACf,IAAGC,EA/CNtpB,KA+CyBgT,YAAc,GAEvB,SAAzB3D,EAAOyM,eAAsD,IAA1BzM,EAAOkL,gBAAwBlL,EAAOmP,qBAC3E8K,EAAYA,EAlDDtpB,KAkDoB8oB,qBAAqB,YAAY,GAAQ,EACxEQ,EAAYtZ,KAAKC,IAAIqZ,EAAW,KAIhCja,EAAOsL,QAvDI3a,KAuDa4a,YAAa,CACvC,IAAM2O,EAxDOvpB,KAwDYqP,OAAOwL,SAxDnB7a,KAwDqCqP,OAAOwL,QAAQvH,SAxDpDtT,KAwDsE6a,QAxDtE7a,KAwDuF6a,QAAQC,OAAOle,OAAS,EAxD/GoD,KAwD0H8a,OAAOle,OAAS,EACvJ,OAzDaoD,KAyDCmb,QAAQoO,EAAWja,EAAOuD,EAAc6U,GAGxD,OA5De1nB,KA4DDmb,QAAQmO,EAAWha,EAAOuD,EAAc6U,IJlDtD8B,WKXa,SAAoBla,EAA2BuD,EAAqB6U,GAEjF,YAF2F,IAA1DpY,MAAQtP,KAAKqP,OAAOC,YAAsC,IAA/BuD,OAAe,GAC5D7S,KACDmb,QADCnb,KACcgT,YAAa1D,EAAOuD,EAAc6U,ILU/D+B,eMZa,SAAwBna,EAA2BuD,EAAqB6U,EAAUjR,QAAiB,IAA3EnH,MAAQtP,KAAKqP,OAAOC,YAAuD,IAAhDuD,OAAe,QAAiC,IAAjB4D,MAAY,IAC3G,IACIvM,EADWlK,KACIgT,YACbgU,EAAOhX,KAAKE,IAFHlQ,KAEcqP,OAAOiL,mBAAoBpQ,GAClD0a,EAAYoC,EAAOhX,KAAKgU,OAAO9Z,EAAQ8c,GAH9BhnB,KAG6CqP,OAAOkL,gBAC7DpL,EAJSnP,KAIUoX,aAJVpX,KAIgCmP,WAJhCnP,KAIoDmP,UAEnE,GAAIA,GANWnP,KAMS4b,SAASgJ,GAAY,CAG3C,IAAM8E,EATO1pB,KASc4b,SAASgJ,GAGhCzV,EAAYua,GAZH1pB,KAUW4b,SAASgJ,EAAY,GAEH8E,GAAejT,IACvDvM,GAbWlK,KAaKqP,OAAOkL,oBAEpB,CAGL,IAAM8O,EAlBOrpB,KAkBW4b,SAASgJ,EAAY,GAGzCzV,EAAYka,IArBHrpB,KAmBc4b,SAASgJ,GAEOyE,GAAY5S,IACrDvM,GAtBWlK,KAsBKqP,OAAOkL,gBAM3B,OAFArQ,EAAQ8F,KAAKC,IAAI/F,EAAO,GACxBA,EAAQ8F,KAAKE,IAAIhG,EA3BFlK,KA2BgB2Z,WAAW/c,OAAS,GA3BpCoD,KA4BDmb,QAAQjR,EAAOoF,EAAOuD,EAAc6U,INhBlDzI,oBOZa,WACb,IAOIwH,EAPE1X,EAAS/O,KAEbqP,EAEEN,EAFFM,OACAmJ,EACEzJ,EADFyJ,WAEIsD,EAAyC,SAAzBzM,EAAOyM,cAA2B/M,EAAO+Z,uBAAyBzZ,EAAOyM,cAC3F6N,EAAe5a,EAAOqY,aAG1B,GAAI/X,EAAOqE,KAAM,CACf,GAAI3E,EAAOwE,UAAW,OACtBkT,EAAY5E,SAAS1d,EAAE4K,EAAOoY,cAAcjhB,KAAK,2BAA4B,IAEzEmJ,EAAO0M,eACL4N,EAAe5a,EAAOsQ,aAAevD,EAAgB,GAAK6N,EAAe5a,EAAO+L,OAAOle,OAASmS,EAAOsQ,aAAevD,EAAgB,GACxI/M,EAAO4E,UACPgW,EAAenR,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,6BAAsE8G,EAAtE,WAA0FpX,EAAO2Q,oBAAjG,KAAyHrV,GAAG,GAAGT,QAC9IqC,GAAS,WACPwC,EAAOoM,QAAQwO,OAGjB5a,EAAOoM,QAAQwO,GAERA,EAAe5a,EAAO+L,OAAOle,OAASkf,GAC/C/M,EAAO4E,UACPgW,EAAenR,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,6BAAsE8G,EAAtE,WAA0FpX,EAAO2Q,oBAAjG,KAAyHrV,GAAG,GAAGT,QAC9IqC,GAAS,WACPwC,EAAOoM,QAAQwO,OAGjB5a,EAAOoM,QAAQwO,QAGjB5a,EAAOoM,QAAQwO,K7BPjBjW,K8B1Ba,CACbkW,WCFa,WACb,IACMtqB,EAAWF,IAEfiQ,EAHarP,KAGbqP,OACAmJ,EAJaxY,KAIbwY,WAGIqR,EAAYrR,EAAWpa,WAAWxB,OAAS,EAAIuH,EAAEqU,EAAWpa,WAAW,GAAGyN,YAAc2M,EAC9FqR,EAAUzrB,SAAV,IAAuBiR,EAAOsQ,WAA9B,IAA4CtQ,EAAO2Q,qBAAuBpa,SAC1E,IAAIkV,EAAS+O,EAAUzrB,SAAV,IAAuBiR,EAAOsQ,YAE3C,GAAItQ,EAAOkQ,uBAAwB,CACjC,IAAMuK,EAAiBza,EAAOkL,eAAiBO,EAAOle,OAASyS,EAAOkL,eAEtE,GAAIuP,IAAmBza,EAAOkL,eAAgB,CAC5C,IAAK,IAAIjgB,EAAI,EAAGA,EAAIwvB,EAAgBxvB,GAAK,EAAG,CAC1C,IAAMyvB,EAAY5lB,EAAE7E,EAASnB,cAAc,QAAQkH,SAAYgK,EAAOsQ,WAApD,IAAkEtQ,EAAOuQ,iBAC3FiK,EAAUhf,OAAOkf,GAGnBjP,EAAS+O,EAAUzrB,SAAV,IAAuBiR,EAAOsQ,aAId,SAAzBtQ,EAAOyM,eAA6BzM,EAAOgQ,eAAchQ,EAAOgQ,aAAevE,EAAOle,QAxB3EoD,KAyBRqf,aAAerP,KAAKwV,KAAKvc,WAAWoG,EAAOgQ,cAAgBhQ,EAAOyM,cAAe,KAzBzE9b,KA0BRqf,cAAgBhQ,EAAO+P,qBA1Bfpf,KA4BJqf,aAAevE,EAAOle,QA5BlBoD,KA4BmCqP,OAAOiQ,oBA5B1Ctf,KA6BNqf,aAAevE,EAAOle,QAG/B,IAAMotB,EAAgB,GAChBC,EAAe,GACrBnP,EAAO7Q,MAAK,SAAClG,EAAImG,GACD/F,EAAEJ,GACVmC,KAAK,0BAA2BgE,MAGxC,IAAK,IAAI5P,EAAI,EAAGA,EAvCD0F,KAuCYqf,aAAc/kB,GAAK,EAAG,CAC/C,IAAM4P,EAAQ5P,EAAI0V,KAAKgU,MAAM1pB,EAAIwgB,EAAOle,QAAUke,EAAOle,OACzDqtB,EAAa3nB,KAAKwY,EAAOnQ,GAAGT,GAAO,IACnC8f,EAAc7iB,QAAQ2T,EAAOnQ,GAAGmQ,EAAOle,OAASsN,EAAQ,GAAG,IAG7D,IAAK,IAAI5P,EAAI,EAAGA,EAAI2vB,EAAartB,OAAQtC,GAAK,EAC5CuvB,EAAUhf,OAAO1G,EAAE8lB,EAAa3vB,GAAG4vB,WAAU,IAAO7kB,SAASgK,EAAO2Q,sBAGtE,IAAK,IAAI1lB,EAAI0vB,EAAcptB,OAAS,EAAGtC,GAAK,EAAGA,GAAK,EAClDuvB,EAAU3e,QAAQ/G,EAAE6lB,EAAc1vB,GAAG4vB,WAAU,IAAO7kB,SAASgK,EAAO2Q,uBDhDxErM,QELa,WACE3T,KACRkT,KAAK,iBACZ,IASIyV,EARF3V,EAHahT,KAGbgT,YACA8H,EAJa9a,KAIb8a,OACAuE,EALarf,KAKbqf,aACAzG,EANa5Y,KAMb4Y,eACAD,EAPa3Y,KAOb2Y,eACAiD,EARa5b,KAQb4b,SACczE,EATDnX,KASboX,aATapX,KAYR4Y,gBAAiB,EAZT5Y,KAaR2Y,gBAAiB,EACxB,IACMG,GADiB8C,EAAS5I,GAdjBhT,KAeqB0M,eAEpC,GAAIsG,EAAcqM,EAChBsJ,EAAW7N,EAAOle,OAAwB,EAAfyiB,EAAmBrM,EAC9C2V,GAAYtJ,EAnBCrf,KAoBemb,QAAQwN,EAAU,GAAG,GAAO,IAE3B,IAAT7P,GAtBP9Y,KAuBJyZ,cAActC,GAvBVnX,KAuBwBmP,UAvBxBnP,KAuB2CmP,WAAa2J,QAEhE,GAAI9F,GAAe8H,EAAOle,OAASyiB,EAAc,CAEtDsJ,GAAY7N,EAAOle,OAASoW,EAAcqM,EAC1CsJ,GAAYtJ,EA5BCrf,KA6Bemb,QAAQwN,EAAU,GAAG,GAAO,IAE3B,IAAT7P,GA/BP9Y,KAgCJyZ,cAActC,GAhCVnX,KAgCwBmP,UAhCxBnP,KAgC2CmP,WAAa2J,GAhCxD9Y,KAoCR4Y,eAAiBA,EApCT5Y,KAqCR2Y,eAAiBA,EArCT3Y,KAsCRkT,KAAK,YFjCZiX,YGNa,WACb,IAEE3R,EAFaxY,KAEbwY,WACAnJ,EAHarP,KAGbqP,OACAyL,EAJa9a,KAIb8a,OAEFtC,EAAWpa,SAAX,IAAwBiR,EAAOsQ,WAA/B,IAA6CtQ,EAAO2Q,oBAApD,KAA4E3Q,EAAOsQ,WAAnF,IAAiGtQ,EAAOuQ,iBAAmBha,SAC3HkV,EAAOxU,WAAW,6BjCsBlBoS,WkC5Ba,CACbG,cCHa,SAAuBuR,GAEpC,KADepqB,KACJoM,QAAQwE,QADJ5Q,KACqBqP,OAAO0P,eAD5B/e,KACoDqP,OAAO+M,eAD3Dpc,KACmFqqB,UADnFrqB,KACsGqP,OAAOoE,SAA5H,CACA,IAAM1P,EAAyC,cAFhC/D,KAEGqP,OAAOyE,kBAFV9T,KAEqD+D,GAFrD/D,KAEiEuP,UAChFxL,EAAGzF,MAAMgsB,OAAS,OAClBvmB,EAAGzF,MAAMgsB,OAASF,EAAS,WAAa,SDDxCG,gBEJa,WACEvqB,KAEJoM,QAAQwE,OAFJ5Q,KAEoBqP,OAAO+M,eAF3Bpc,KAEmDqqB,UAFnDrqB,KAEsEqP,OAAOoE,UAF7EzT,KAM4B,cAN5BA,KAMDqP,OAAOyE,kBAAoC,KAAO,aAAaxV,MAAMgsB,OAAS,MpCwB5F7iB,OL8Da,CACb+iB,aA/BF,WACE,IACMlrB,EAAWF,IAEfiQ,EAHarP,KAGbqP,OACAjD,EAJapM,KAIboM,QAJapM,KAMRmT,aAAeA,EAAarX,KANpBkE,WAORkX,YAAcA,EAAYpb,KAPlBkE,WAQR0Z,WAAaA,EAAW5d,KARhBkE,MAUXqP,EAAOoE,UAVIzT,KAWN0c,SAAWA,EAAS5gB,KAXdkE,YAcRsc,QAAUA,EAAQxgB,KAdVkE,MAgBXoM,EAAQwE,QAAUiM,IACpBvd,EAAS7B,iBAAiB,aAAcqf,GACxCD,GAAqB,GAGvBpV,EArBezH,KAqBA,OAUfyqB,aAPF,WAEEhjB,EADezH,KACA,SK1Df0b,YqC9Ba,CACbC,czCGa,WACb,IAAM5M,EAAS/O,KAEbgT,EAKEjE,EALFiE,YACAiU,EAIElY,EAJFkY,YAFF,EAMIlY,EAHFsQ,oBAHF,MAGiB,EAHjB,EAIEhQ,EAEEN,EAFFM,OACAqS,EACE3S,EADF2S,IAEIhG,EAAcrM,EAAOqM,YAC3B,GAAKA,KAAeA,GAAmD,IAApC1gB,OAAOqC,KAAKqe,GAAa9e,QAA5D,CAEA,IAAM8tB,EAAa3b,EAAO4b,cAAcjP,EAAa3M,EAAOM,OAAOiP,gBAAiBvP,EAAOhL,IAC3F,GAAK2mB,GAAc3b,EAAO6b,oBAAsBF,EAAhD,CACA,IACMG,GADuBH,KAAchP,EAAcA,EAAYgP,QAAc5nB,IAClCiM,EAAO+b,eAClDC,EAAcrN,GAAc3O,EAAQM,GACpC2b,EAAatN,GAAc3O,EAAQ8b,GACnCI,EAAa5b,EAAOiE,QAEtByX,IAAgBC,GAClBtJ,EAAI/b,YAAe0J,EAAOqQ,uBAA1B,QAAwDrQ,EAAOqQ,uBAA/D,eACA3Q,EAAOmc,yBACGH,GAAeC,IACzBtJ,EAAIrc,SAAYgK,EAAOqQ,uBAAvB,SAEImL,EAAiBlN,KAAKwN,MAAuC,WAA/BN,EAAiBlN,KAAKwN,OAAsBN,EAAiBlN,KAAKwN,MAA6B,WAArB9b,EAAOsO,KAAKwN,OACtHzJ,EAAIrc,SAAYgK,EAAOqQ,uBAAvB,eAGF3Q,EAAOmc,wBAIT,CAAC,aAAc,aAAc,aAAa5tB,SAAQ,SAAA0M,GAChD,IAAMohB,EAAmB/b,EAAOrF,IAASqF,EAAOrF,GAAMsJ,QAChD+X,EAAkBR,EAAiB7gB,IAAS6gB,EAAiB7gB,GAAMsJ,QAErE8X,IAAqBC,GACvBtc,EAAO/E,GAAMshB,WAGVF,GAAoBC,GACvBtc,EAAO/E,GAAMuhB,YAGjB,IAAMC,EAAmBX,EAAiB/X,WAAa+X,EAAiB/X,YAAczD,EAAOyD,UACvF2Y,EAAcpc,EAAOqE,OAASmX,EAAiB/O,gBAAkBzM,EAAOyM,eAAiB0P,GAE3FA,GAAoBvE,GACtBlY,EAAO2c,kBAGTvuB,EAAO4R,EAAOM,OAAQwb,GACtB,IAAMc,EAAY5c,EAAOM,OAAOiE,QAChCtY,OAAOkb,OAAOnH,EAAQ,CACpB8H,eAAgB9H,EAAOM,OAAOwH,eAC9B8B,eAAgB5J,EAAOM,OAAOsJ,eAC9BC,eAAgB7J,EAAOM,OAAOuJ,iBAG5BqS,IAAeU,EACjB5c,EAAOuc,WACGL,GAAcU,GACxB5c,EAAOwc,SAGTxc,EAAO6b,kBAAoBF,EAC3B3b,EAAOmE,KAAK,oBAAqB2X,GAE7BY,GAAexE,IACjBlY,EAAOob,cACPpb,EAAO6a,aACP7a,EAAO8M,eACP9M,EAAOoM,QAAQnI,EAAcqM,EAAetQ,EAAOsQ,aAAc,GAAG,IAGtEtQ,EAAOmE,KAAK,aAAc2X,MyC/E1BF,cCHa,SAAuBjP,EAAa5G,EAAiB8W,GAClE,QAD+E,IAA9B9W,MAAO,UACnD4G,IAAwB,cAAT5G,GAAyB8W,GAA7C,CACA,IAAIlB,GAAa,EACX3pB,EAASF,IACTgrB,EAAyB,WAAT/W,EAAoB/T,EAAO+qB,YAAcF,EAAYhK,aACrEmK,EAAS/wB,OAAOqC,KAAKqe,GAAalW,KAAI,SAAAwmB,GAC1C,GAAqB,iBAAVA,GAA6C,IAAvBA,EAAM/oB,QAAQ,KAAY,CACzD,IAAMgpB,EAAWhjB,WAAW+iB,EAAME,OAAO,IAEzC,MAAO,CACL3wB,MAFYswB,EAAgBI,EAG5BD,SAIJ,MAAO,CACLzwB,MAAOywB,EACPA,YAGJD,EAAOI,MAAK,SAAC9pB,EAAG+pB,GAAJ,OAAUvK,SAASxf,EAAE9G,MAAO,IAAMsmB,SAASuK,EAAE7wB,MAAO,OAEhE,IAAK,IAAIjB,EAAI,EAAGA,EAAIyxB,EAAOnvB,OAAQtC,GAAK,EAAG,CACzC,MAGIyxB,EAAOzxB,GAFT0xB,EADF,EACEA,MACAzwB,EAFF,EAEEA,MAGW,WAATuZ,EACE/T,EAAOP,WAAP,eAAiCjF,EAAjC,OAA6C+O,UAC/CogB,EAAasB,GAENzwB,GAASqwB,EAAYjK,cAC9B+I,EAAasB,GAIjB,OAAOtB,GAAc,StCLrBrO,cHEa,CACbA,cApCF,WACE,IAEYgQ,EAFGrsB,KAEbqqB,SACAhb,EAHarP,KAGbqP,OAGAqP,EACErP,EADFqP,mBAGF,GAAIA,EAAoB,CACtB,IAAM4N,EAVOtsB,KAUiB8a,OAAOle,OAAS,EACxC2vB,EAXOvsB,KAWqB2Z,WAAW2S,GAXhCtsB,KAWyDqa,gBAAgBiS,GAAuC,EAArB5N,EAX3F1e,KAYNqqB,SAZMrqB,KAYYgiB,KAAOuK,OAZnBvsB,KAcNqqB,SAAsC,IAdhCrqB,KAcY4b,SAAShf,QAGN,IAA1ByS,EAAOsJ,iBAjBI3Y,KAkBN2Y,gBAlBM3Y,KAkBmBqqB,WAGJ,IAA1Bhb,EAAOuJ,iBArBI5Y,KAsBN4Y,gBAtBM5Y,KAsBmBqqB,UAG9BgC,GAAaA,IAzBFrsB,KAyBuBqqB,WAzBvBrqB,KA0BN+a,OAAQ,GAGbsR,IA7BWrsB,KA6BUqqB,UA7BVrqB,KA8BNkT,KA9BMlT,KA8BMqqB,SAAW,OAAS,YGGzC/kB,QuChCa,CACbknB,WCaa,WACb,IAjBsBC,EAASC,EACzBC,EAkBJpnB,EAFavF,KAEbuF,WACA8J,EAHarP,KAGbqP,OACA8H,EAJanX,KAIbmX,IACAuK,EALa1hB,KAKb0hB,IACAnQ,EANavR,KAMbuR,OACAnF,EAPapM,KAOboM,QAGIwgB,GA3BgBH,EA2BU,CAAC,cAAepd,EAAOyD,UAAW,CAChE,kBAAmB1G,EAAQwE,OAC1B,CACD,YAba5Q,KAaOqP,OAAO4H,UAAY5H,EAAO4H,SAAS3D,SACtD,CACD,WAAcjE,EAAO6O,YACpB,CACD,IAAO/G,GACN,CACD,KAAQ9H,EAAOsO,MAAQtO,EAAOsO,KAAKC,KAAO,GACzC,CACD,cAAevO,EAAOsO,MAAQtO,EAAOsO,KAAKC,KAAO,GAA0B,WAArBvO,EAAOsO,KAAKwN,MACjE,CACD,QAAW5Z,EAAOE,SACjB,CACD,IAAOF,EAAOC,KACb,CACD,WAAYnC,EAAOoE,SAClB,CACD,SAAYpE,EAAOoE,SAAWpE,EAAO0M,gBACpC,CACD,iBAAkB1M,EAAOgK,sBAhDIqT,EAiD3Brd,EAAOqQ,uBAhDLiN,EAAgB,GACtBF,EAAQnvB,SAAQ,SAAAuvB,GACM,iBAATA,EACT7xB,OAAOqC,KAAKwvB,GAAMvvB,SAAQ,SAAAiI,GACpBsnB,EAAKtnB,IACPonB,EAAcrqB,KAAKoqB,EAASnnB,MAGP,iBAATsnB,GAChBF,EAAcrqB,KAAKoqB,EAASG,MAGzBF,GAqCPpnB,EAAWjD,KAAX,MAAAiD,EAAmBqnB,GACnBlL,EAAIrc,SAAS,UAAIE,GAAY6H,KAAK,MAlCnBpN,KAmCRkrB,wBDhDP4B,cEJa,WACb,IAEEpL,EAFa1hB,KAEb0hB,IACAnc,EAHavF,KAGbuF,WAEFmc,EAAI/b,YAAYJ,EAAW6H,KAAK,MALjBpN,KAMRkrB,yBzC4BP6B,O0CjCa,CACbC,UCDa,SAAmBC,EAAS7vB,EAAK8vB,EAAQC,EAAOC,EAAkB1sB,GAC/E,IACI2sB,EADEtsB,EAASF,IAGf,SAASysB,IACH5sB,GAAUA,IAGEyD,EAAE8oB,GAASrhB,OAAO,WAAW,IAE3BqhB,EAAQM,UAAaH,EAsBvCE,IArBIlwB,IACFiwB,EAAQ,IAAItsB,EAAOZ,OACbqtB,OAASF,EACfD,EAAMI,QAAUH,EAEZH,IACFE,EAAMF,MAAQA,GAGZD,IACFG,EAAMH,OAASA,GAGb9vB,IACFiwB,EAAMjwB,IAAMA,IAGdkwB,KD1BJpO,cEJa,WACb,IAAMnQ,EAAS/O,KAGf,SAASstB,IACH,MAAOve,GAA8CA,IAAUA,EAAOmL,iBAC9CpX,IAAxBiM,EAAO2e,eAA4B3e,EAAO2e,cAAgB,GAE1D3e,EAAO2e,eAAiB3e,EAAO4e,aAAa/wB,SAC1CmS,EAAOM,OAAO8P,qBAAqBpQ,EAAO0S,SAC9C1S,EAAOmE,KAAK,iBARhBnE,EAAO4e,aAAe5e,EAAO2S,IAAI3V,KAAK,OAYtC,IAAK,IAAIzR,EAAI,EAAGA,EAAIyU,EAAO4e,aAAa/wB,OAAQtC,GAAK,EAAG,CACtD,IAAM2yB,EAAUle,EAAO4e,aAAarzB,GACpCyU,EAAOie,UAAUC,EAASA,EAAQW,YAAcX,EAAQ7mB,aAAa,OAAQ6mB,EAAQC,QAAUD,EAAQ7mB,aAAa,UAAW6mB,EAAQE,OAASF,EAAQ7mB,aAAa,UAAU,EAAMknB,O5CqBnLO,GAAmB,GAEnBC,G,WACJ,aAAqB,IACnB,IAAI/pB,EACAsL,EAFe,mBAANnN,EAAM,yBAANA,EAAM,gBAcnB,GAVoB,IAAhBA,EAAKtF,QAAgBsF,EAAK,GAAGhF,aAAwE,WAAzDlC,OAAOkB,UAAU8G,SAASvI,KAAKyH,EAAK,IAAIyL,MAAM,GAAI,GAChG0B,EAASnN,EAAK,IAEb6B,EAAc7B,EADV,GACAmN,EAAUnN,EADV,IAIFmN,IAAQA,EAAS,IACtBA,EAASlS,EAAO,GAAIkS,GAChBtL,IAAOsL,EAAOtL,KAAIsL,EAAOtL,GAAKA,GAE9BsL,EAAOtL,IAAMI,EAAEkL,EAAOtL,IAAInH,OAAS,EAAG,CACxC,IAAMmxB,EAAU,GAQhB,OAPA5pB,EAAEkL,EAAOtL,IAAIkG,MAAK,SAAA2hB,GAChB,IAAMoC,EAAY7wB,EAAO,GAAIkS,EAAQ,CACnCtL,GAAI6nB,IAENmC,EAAQzrB,KAAK,IAAIwrB,EAAOE,OAGnBD,EAIT,IAWqD,EAX/Chf,EAAS/O,MACf+O,EAAOP,YAAa,EACpBO,EAAO3C,QAAUqE,IACjB1B,EAAOwC,OAASJ,EAAU,CACxB1R,UAAW4P,EAAO5P,YAEpBsP,EAAOzC,QAAUgG,IACjBvD,EAAOiS,gBAAkB,GACzBjS,EAAOsS,mBAAqB,GAC5BtS,EAAOvU,QAAP,UAAqBuU,EAAOkf,aAExB5e,EAAO7U,SAAWmJ,MAAMK,QAAQqL,EAAO7U,YACzC,EAAAuU,EAAOvU,SAAQ8H,KAAf,QAAuB+M,EAAO7U,SAGhC,IAAMimB,EAAmB,GACzB1R,EAAOvU,QAAQ8C,SAAQ,SAAA4wB,GACrBA,EAAI,CACFnf,SACAof,aAAc3N,GAAmBnR,EAAQoR,GACzCtb,GAAI4J,EAAO5J,GAAGrJ,KAAKiT,GACnBkS,KAAMlS,EAAOkS,KAAKnlB,KAAKiT,GACvBjH,IAAKiH,EAAOjH,IAAIhM,KAAKiT,GACrBmE,KAAMnE,EAAOmE,KAAKpX,KAAKiT,QAI3B,IAwDU6B,EACAwd,EAzDJC,EAAelxB,EAAO,GAAImxB,GAAU7N,GAkH1C,OAhHA1R,EAAOM,OAASlS,EAAO,GAAIkxB,EAAcR,GAAkBxe,GAC3DN,EAAO+b,eAAiB3tB,EAAO,GAAI4R,EAAOM,QAC1CN,EAAOwf,aAAepxB,EAAO,GAAIkS,GAE7BN,EAAOM,QAAUN,EAAOM,OAAOlK,IACjCnK,OAAOqC,KAAK0R,EAAOM,OAAOlK,IAAI7H,SAAQ,SAAAkxB,GACpCzf,EAAO5J,GAAGqpB,EAAWzf,EAAOM,OAAOlK,GAAGqpB,OAItCzf,EAAOM,QAAUN,EAAOM,OAAO+R,OACjCrS,EAAOqS,MAAMrS,EAAOM,OAAO+R,OAI7BrS,EAAO5K,EAAIA,EAEXnJ,OAAOkb,OAAOnH,EAAQ,CACpBuE,QAASvE,EAAOM,OAAOiE,QACvBvP,KAEAwB,WAAY,GAEZuV,OAAQ3W,IACRwV,WAAY,GACZiC,SAAU,GACVvB,gBAAiB,GAGjBrC,aAZoB,WAalB,MAAmC,eAA5BjJ,EAAOM,OAAOyD,WAGvB2E,WAhBoB,WAiBlB,MAAmC,aAA5B1I,EAAOM,OAAOyD,WAIvBE,YAAa,EACbyT,UAAW,EAEX7L,aAAa,EACbG,OAAO,EAEP5L,UAAW,EACXwN,kBAAmB,EACnB5M,SAAU,EACV0e,SAAU,EACVlb,WAAW,EAEXoF,eAAgB5J,EAAOM,OAAOsJ,eAC9BC,eAAgB7J,EAAOM,OAAOuJ,eAE9BoE,aACQpM,EAAQ,CAAC,aAAc,YAAa,WAAY,eAChDwd,EAAU,CAAC,cAAe,cAAe,aAC/Crf,EAAO2f,iBAAmB,CACxBvR,MAAOvM,EAAM,GACb0M,KAAM1M,EAAM,GACZ2M,IAAK3M,EAAM,GACX4M,OAAQ5M,EAAM,IAEhB7B,EAAO4f,mBAAqB,CAC1BxR,MAAOiR,EAAQ,GACf9Q,KAAM8Q,EAAQ,GACd7Q,IAAK6Q,EAAQ,IAERrf,EAAO3C,QAAQwE,QAAU7B,EAAOM,OAAO0P,cAAgBhQ,EAAO2f,iBAAmB3f,EAAO4f,oBAEjGvb,gBAAiB,CACfe,eAAWrR,EACXsR,aAAStR,EACTqT,yBAAqBrT,EACrBwT,oBAAgBxT,EAChBsT,iBAAatT,EACbkW,sBAAkBlW,EAClBwV,oBAAgBxV,EAChB4T,wBAAoB5T,EAEpB6T,kBAAmB5H,EAAOM,OAAOsH,kBAEjCsD,cAAexN,IACfmiB,kBAAc9rB,EAEd+rB,WAAY,GACZpW,yBAAqB3V,EACrBiR,kBAAcjR,EACduT,iBAAavT,GAGfqS,YAAY,EAEZ0B,eAAgB9H,EAAOM,OAAOwH,eAC9BxD,QAAS,CACPqC,OAAQ,EACRC,OAAQ,EACRN,SAAU,EACVG,SAAU,EACVsD,KAAM,GAGR6U,aAAc,GACdD,aAAc,IAEhB3e,EAAOmE,KAAK,WAERnE,EAAOM,OAAOwO,MAChB9O,EAAO8O,OAKF9O,E,I5BtM0B+f,EAAaC,EAAYC,E,uB4ByM5DzD,OAAA,WACiBvrB,KACJsT,UADItT,KAERsT,SAAU,EAFFtT,KAIJqP,OAAOqJ,YAJH1Y,KAKN6Y,gBALM7Y,KAQRkT,KAAK,Y,EAGdoY,QAAA,WACiBtrB,KACHsT,UADGtT,KAERsT,SAAU,EAFFtT,KAIJqP,OAAOqJ,YAJH1Y,KAKNuqB,kBALMvqB,KAQRkT,KAAK,a,EAGd+b,YAAA,SAAYlf,EAAUT,GAEpBS,EAAWC,KAAKE,IAAIF,KAAKC,IAAIF,EAAU,GAAI,GAC3C,IAAMG,EAFSlQ,KAEI2X,eAEb/H,GAJS5P,KAGI0X,eACIxH,GAAOH,EAAWG,EAJ1BlQ,KAKRwnB,YAAY5X,OAA0B,IAAVN,EAAwB,EAAIA,GALhDtP,KAMRsZ,oBANQtZ,KAORuZ,uB,EAGT2R,qBAAA,WACE,IAAMnc,EAAS/O,KACf,GAAK+O,EAAOM,OAAOkR,cAAiBxR,EAAOhL,GAA3C,CACA,IAAMmrB,EAAMngB,EAAOhL,GAAG+B,UAAUd,MAAM,KAAKd,QAAO,SAAA4B,GAChD,OAAuC,IAAhCA,EAAU7C,QAAQ,WAA+E,IAA5D6C,EAAU7C,QAAQ8L,EAAOM,OAAOqQ,2BAE9E3Q,EAAOmE,KAAK,oBAAqBgc,EAAI9hB,KAAK,Q,EAG5C+hB,gBAAA,SAAgBC,GACd,IAAMrgB,EAAS/O,KACf,OAAI+O,EAAOmL,UAAkB,GACtBkV,EAAQtpB,UAAUd,MAAM,KAAKd,QAAO,SAAA4B,GACzC,OAA6C,IAAtCA,EAAU7C,QAAQ,iBAAyE,IAAhD6C,EAAU7C,QAAQ8L,EAAOM,OAAOsQ,eACjFvS,KAAK,M,EAGVwZ,kBAAA,WACE,IAAM7X,EAAS/O,KACf,GAAK+O,EAAOM,OAAOkR,cAAiBxR,EAAOhL,GAA3C,CACA,IAAMsrB,EAAU,GAChBtgB,EAAO+L,OAAO7Q,MAAK,SAAAmlB,GACjB,IAAM7pB,EAAawJ,EAAOogB,gBAAgBC,GAC1CC,EAAQ/sB,KAAK,CACX8sB,UACA7pB,eAEFwJ,EAAOmE,KAAK,cAAekc,EAAS7pB,MAEtCwJ,EAAOmE,KAAK,gBAAiBmc,K,EAG/BvG,qBAAA,SAAqBwG,EAAkBC,QAAe,IAAjCD,MAAO,gBAA0B,IAAfC,OAAQ,GAC7C,IAEElgB,EAFarP,KAEbqP,OACAyL,EAHa9a,KAGb8a,OACAnB,EAJa3Z,KAIb2Z,WACAU,EALara,KAKbqa,gBACM+H,EANOpiB,KAMbgiB,KACAhP,EAPahT,KAObgT,YAEEwc,EAAM,EAEV,GAAIngB,EAAO0M,eAAgB,CAIzB,IAHA,IACI0T,EADArM,EAAYtI,EAAO9H,GAAaiR,gBAG3B3pB,EAAI0Y,EAAc,EAAG1Y,EAAIwgB,EAAOle,OAAQtC,GAAK,EAChDwgB,EAAOxgB,KAAOm1B,IAEhBD,GAAO,GADPpM,GAAatI,EAAOxgB,GAAG2pB,iBAEP7B,IAAYqN,GAAY,IAI5C,IAAK,IAAIn1B,EAAI0Y,EAAc,EAAG1Y,GAAK,EAAGA,GAAK,EACrCwgB,EAAOxgB,KAAOm1B,IAEhBD,GAAO,GADPpM,GAAatI,EAAOxgB,GAAG2pB,iBAEP7B,IAAYqN,GAAY,SAK5C,GAAa,YAATH,EACF,IAAK,IAAIh1B,EAAI0Y,EAAc,EAAG1Y,EAAIwgB,EAAOle,OAAQtC,GAAK,EAAG,EACnCi1B,EAAQ5V,EAAWrf,GAAK+f,EAAgB/f,GAAKqf,EAAW3G,GAAeoP,EAAazI,EAAWrf,GAAKqf,EAAW3G,GAAeoP,KAGhJoN,GAAO,QAKX,IAAK,IAAIl1B,EAAI0Y,EAAc,EAAG1Y,GAAK,EAAGA,GAAK,EAAG,CACxBqf,EAAW3G,GAAe2G,EAAWrf,GAAK8nB,IAG5DoN,GAAO,GAMf,OAAOA,G,EAGT/N,OAAA,WACE,IAAM1S,EAAS/O,KACf,GAAK+O,IAAUA,EAAOmL,UAAtB,CACA,IACE0B,EAEE7M,EAFF6M,SACAvM,EACEN,EADFM,OAGEA,EAAOqM,aACT3M,EAAO4M,gBAGT5M,EAAOwH,aACPxH,EAAO8M,eACP9M,EAAOyK,iBACPzK,EAAOwK,sBAYHxK,EAAOM,OAAO4H,UAAYlI,EAAOM,OAAO4H,SAAS3D,SACnDmG,IAEI1K,EAAOM,OAAO6O,YAChBnP,EAAOoW,sBAG4B,SAAhCpW,EAAOM,OAAOyM,eAA4B/M,EAAOM,OAAOyM,cAAgB,IAAM/M,EAAOgM,QAAUhM,EAAOM,OAAO0M,eACnGhN,EAAOoM,QAAQpM,EAAO+L,OAAOle,OAAS,EAAG,GAAG,GAAO,GAEnDmS,EAAOoM,QAAQpM,EAAOiE,YAAa,GAAG,GAAO,KAI1DyG,IAIApK,EAAO+M,eAAiBR,IAAa7M,EAAO6M,UAC9C7M,EAAOsN,gBAGTtN,EAAOmE,KAAK,UAhCZ,SAASuG,IACP,IAAMiW,EAAiB3gB,EAAOqI,cAAmC,EAApBrI,EAAOI,UAAiBJ,EAAOI,UACtEwY,EAAe3X,KAAKE,IAAIF,KAAKC,IAAIyf,EAAgB3gB,EAAO2I,gBAAiB3I,EAAO4I,gBACtF5I,EAAO0K,aAAakO,GACpB5Y,EAAOuK,oBACPvK,EAAOwK,wB,EA8BXmS,gBAAA,SAAgBiE,EAAcC,QAAmB,IAAnBA,OAAa,GACzC,IACMC,EADS7vB,KACiBqP,OAAOyD,UAOvC,OALK6c,IAEHA,EAAoC,eAArBE,EAAoC,WAAa,cAG9DF,IAAiBE,GAAqC,eAAjBF,GAAkD,aAAjBA,IAR3D3vB,KAYR0hB,IAAI/b,YAAX,GAZe3F,KAYkBqP,OAAOqQ,uBAAyBmQ,GAAoBxqB,SAArF,GAZerF,KAYyFqP,OAAOqQ,uBAAyBiQ,GAZzH3vB,KAaRkrB,uBAbQlrB,KAcRqP,OAAOyD,UAAY6c,EAdX3vB,KAeR8a,OAAO7Q,MAAK,SAAAmlB,GACI,aAAjBO,EACFP,EAAQ9wB,MAAMqT,MAAQ,GAEtByd,EAAQ9wB,MAAMuT,OAAS,MAnBZ7R,KAsBRkT,KAAK,mBACR0c,GAvBW5vB,KAuBQyhB,UAvBRzhB,M,EA2BjB8vB,wBAAA,SAAwBhd,GACP9S,KACJmX,KAAqB,QAAdrE,IADH9S,KACkCmX,KAAqB,QAAdrE,IADzC9S,KAERmX,IAAoB,QAAdrE,EAFE9S,KAGRoX,aAA2C,eAHnCpX,KAGcqP,OAAOyD,WAHrB9S,KAG0DmX,IAH1DnX,KAKJmX,KALInX,KAMN0hB,IAAIrc,SANErF,KAMiBqP,OAAOqQ,uBAArC,OANa1f,KAON+D,GAAG2L,IAAM,QAPH1P,KASN0hB,IAAI/b,YATE3F,KASoBqP,OAAOqQ,uBAAxC,OATa1f,KAUN+D,GAAG2L,IAAM,OAVH1P,KAaRyhB,W,EAGTsO,MAAA,SAAMhsB,GACJ,IAAMgL,EAAS/O,KACf,GAAI+O,EAAOihB,QAAS,OAAO,EAE3B,IAAMtO,EAAMvd,EAAEJ,GAAMgL,EAAOM,OAAOtL,IAGlC,KAFAA,EAAK2d,EAAI,IAGP,OAAO,EAGT3d,EAAGgL,OAASA,EAEZ,IAAMkhB,EAAqB,WACzB,WAAYlhB,EAAOM,OAAOgR,cAAgB,IAAI9b,OAAOS,MAAM,KAAKoI,KAAK,MAoBnEoL,EAjBe,WACjB,GAAIzU,GAAMA,EAAG2Q,YAAc3Q,EAAG2Q,WAAW5W,cAAe,CACtD,IAAMgG,EAAMK,EAAEJ,EAAG2Q,WAAW5W,cAAcmyB,MAI1C,OAFAnsB,EAAI1F,SAAW,SAAA8xB,GAAO,OAAIxO,EAAItjB,SAAS8xB,IAEhCpsB,EAGT,OAAK4d,EAAItjB,SAIFsjB,EAAItjB,SAAS6xB,KAHX9rB,EAAEud,GAAKtjB,SAAS6xB,KAOVE,GAEjB,GAA0B,IAAtB3X,EAAW5b,QAAgBmS,EAAOM,OAAO2O,eAAgB,CAC3D,IACMoS,EADWhxB,IACQjB,cAAc,OACvCqa,EAAarU,EAAEisB,GACfA,EAAQtqB,UAAYiJ,EAAOM,OAAOgR,aAClCqB,EAAI7W,OAAOulB,GACX1O,EAAItjB,SAAJ,IAAiB2Q,EAAOM,OAAOsQ,YAAc1V,MAAK,SAAAmlB,GAChD5W,EAAW3N,OAAOukB,MAetB,OAXAp0B,OAAOkb,OAAOnH,EAAQ,CACpB2S,MACA3d,KACAyU,aACAjJ,UAAWiJ,EAAW,GACtBwX,SAAS,EAET7Y,IAA8B,QAAzBpT,EAAG2L,IAAI8C,eAAoD,QAAzBkP,EAAI3X,IAAI,aAC/CqN,aAA0C,eAA5BrI,EAAOM,OAAOyD,YAAwD,QAAzB/O,EAAG2L,IAAI8C,eAAoD,QAAzBkP,EAAI3X,IAAI,cACrGsY,SAAwC,gBAA9B7J,EAAWzO,IAAI,cAEpB,G,EAGT8T,KAAA,SAAK9Z,GAEH,OADe/D,KACJinB,cAEK,IAHDjnB,KAEQ+vB,MAAMhsB,KAFd/D,KAIRkT,KAAK,cAJGlT,KAMJqP,OAAOqM,aANH1b,KAON2b,gBAPM3b,KAWRwsB,aAXQxsB,KAaJqP,OAAOqE,MAbH1T,KAcN4pB,aAdM5pB,KAkBRuW,aAlBQvW,KAoBR6b,eApBQ7b,KAsBJqP,OAAO+M,eAtBHpc,KAuBNqc,gBAvBMrc,KA2BJqP,OAAOqJ,YA3BH1Y,KA2BwBsT,SA3BxBtT,KA4BN6Y,gBA5BM7Y,KA+BJqP,OAAO6P,eA/BHlf,KAgCNkf,gBAhCMlf,KAoCJqP,OAAOqE,KApCH1T,KAqCNmb,QArCMnb,KAqCSqP,OAAOyO,aArChB9d,KAqCsCqf,aAAc,EArCpDrf,KAqC8DqP,OAAOiR,oBAAoB,GAAO,GArChGtgB,KAuCNmb,QAvCMnb,KAuCSqP,OAAOyO,aAAc,EAvC9B9d,KAuCwCqP,OAAOiR,oBAAoB,GAAO,GAvC1EtgB,KA2CRwqB,eA3CQxqB,KA6CRinB,aAAc,EA7CNjnB,KA+CRkT,KAAK,QA/CGlT,KAgDRkT,KAAK,cAhDGlT,M,EAoDjBqwB,QAAA,SAAQC,EAAuBC,QAAoB,IAA3CD,OAAiB,QAA0B,IAApBC,OAAc,GAC3C,Ib/hBIv0B,Ea+hBE+S,EAAS/O,KAEbqP,EAIEN,EAJFM,OACAqS,EAGE3S,EAHF2S,IACAlJ,EAEEzJ,EAFFyJ,WACAsC,EACE/L,EADF+L,OAGF,YAA6B,IAAlB/L,EAAOM,QAA0BN,EAAOmL,YAInDnL,EAAOmE,KAAK,iBAEZnE,EAAOkY,aAAc,EAErBlY,EAAO0b,eAEHpb,EAAOqE,MACT3E,EAAOob,cAILoG,IACFxhB,EAAO+d,gBACPpL,EAAIpb,WAAW,SACfkS,EAAWlS,WAAW,SAElBwU,GAAUA,EAAOle,QACnBke,EAAOnV,YAAY,CAAC0J,EAAO0Q,kBAAmB1Q,EAAOwQ,iBAAkBxQ,EAAO4Q,eAAgB5Q,EAAO8Q,gBAAgB/S,KAAK,MAAM9G,WAAW,SAASA,WAAW,4BAInKyI,EAAOmE,KAAK,WAEZlY,OAAOqC,KAAK0R,EAAOiS,iBAAiB1jB,SAAQ,SAAAkxB,GAC1Czf,EAAOjH,IAAI0mB,OAGU,IAAnB8B,IACFvhB,EAAO2S,IAAI,GAAG3S,OAAS,KbvkBrB/S,EawkBU+S,EbvkBhB/T,OAAOqC,KAAKrB,GAAQsB,SAAQ,SAAAzB,GAC1B,IACEG,EAAOH,GAAO,KACd,MAAOkG,IAGT,WACS/F,EAAOH,GACd,MAAOkG,SakkBTgN,EAAOmL,WAAY,GAnCV,M,EAuCJsW,eAAP,SAAsBC,GACpBtzB,EAAO0wB,GAAkB4C,I,EAWpBC,cAAP,SAAqBxC,GACdJ,EAAO5xB,UAAU+xB,cAAaH,EAAO5xB,UAAU+xB,YAAc,IAClE,IAAMzzB,EAAUszB,EAAO5xB,UAAU+xB,YAEd,mBAARC,GAAsB1zB,EAAQyI,QAAQirB,GAAO,GACtD1zB,EAAQ8H,KAAK4rB,I,EAIVyC,IAAP,SAAWt2B,GACT,OAAIsJ,MAAMK,QAAQ3J,IAChBA,EAAOiD,SAAQ,SAAA5C,GAAC,OAAIozB,EAAO4C,cAAch2B,MAClCozB,IAGTA,EAAO4C,cAAcr2B,GACdyzB,I5BpmB0BgB,E,EAAyBE,E,6B4B4kB5D,WACE,OAAOnB,K,oBAGT,WACE,OAAOS,O5BjlBuCS,E,OAChCtyB,EAAkBqyB,EAAY5yB,UAAW6yB,GACrDC,GAAavyB,EAAkBqyB,EAAaE,G,K4BumBlDh0B,OAAOqC,KAAKwjB,IAAYvjB,SAAQ,SAAAszB,GAC9B51B,OAAOqC,KAAKwjB,GAAW+P,IAAiBtzB,SAAQ,SAAAuzB,GAC9C/C,GAAO5xB,UAAU20B,GAAehQ,GAAW+P,GAAgBC,SAG/D/C,GAAO6C,IAAI,C6CvnBI,YAIZ,IAHD5hB,EAGC,EAHDA,OACA5J,EAEC,EAFDA,GACA+N,EACC,EADDA,KAEMnS,EAASF,IACXiwB,EAAW,KACXC,EAAiB,KAEfC,EAAgB,WACfjiB,IAAUA,EAAOmL,WAAcnL,EAAOkY,cAC3C/T,EAAK,gBACLA,EAAK,YA0CD+d,EAA2B,WAC1BliB,IAAUA,EAAOmL,WAAcnL,EAAOkY,aAC3C/T,EAAK,sBAGP/N,EAAG,QAAQ,WACL4J,EAAOM,OAAO0O,qBAAmD,IAA1Bhd,EAAOmwB,eA5C7CniB,IAAUA,EAAOmL,WAAcnL,EAAOkY,cAC3C6J,EAAW,IAAII,gBAAe,SAAAzE,GAC5BsE,EAAiBhwB,EAAON,uBAAsB,WAC5C,IACEkR,EAEE5C,EAFF4C,MACAE,EACE9C,EADF8C,OAEEsf,EAAWxf,EACX0T,EAAYxT,EAChB4a,EAAQnvB,SAAQ,YAIV,IAHJ8zB,EAGI,EAHJA,eACAC,EAEI,EAFJA,YACA30B,EACI,EADJA,OAEIA,GAAUA,IAAWqS,EAAOhL,KAChCotB,EAAWE,EAAcA,EAAY1f,OAASyf,EAAe,IAAMA,GAAgBE,WACnFjM,EAAYgM,EAAcA,EAAYxf,QAAUuf,EAAe,IAAMA,GAAgBG,cAGnFJ,IAAaxf,GAAS0T,IAAcxT,GACtCmf,WAIGQ,QAAQziB,EAAOhL,KAyBxBhD,EAAOtD,iBAAiB,SAAUuzB,GAClCjwB,EAAOtD,iBAAiB,oBAAqBwzB,OAE/C9rB,EAAG,WAAW,WAxBR4rB,GACFhwB,EAAOJ,qBAAqBowB,GAG1BD,GAAYA,EAASW,WAAa1iB,EAAOhL,KAC3C+sB,EAASW,UAAU1iB,EAAOhL,IAC1B+sB,EAAW,MAoBb/vB,EAAOrD,oBAAoB,SAAUszB,GACrCjwB,EAAOrD,oBAAoB,oBAAqBuzB,OCvErC,YAKZ,IAJDliB,EAIC,EAJDA,OACAof,EAGC,EAHDA,aACAhpB,EAEC,EAFDA,GACA+N,EACC,EADDA,KAEMwe,EAAY,GACZ3wB,EAASF,IAET8wB,EAAS,SAACj1B,EAAQwzB,QAAiB,IAAjBA,MAAU,IAChC,IACMY,EAAW,IADI/vB,EAAO6wB,kBAAoB7wB,EAAO8wB,yBACrB,SAAAC,GAIhC,GAAyB,IAArBA,EAAUl1B,OAAd,CAKA,IAAMm1B,EAAiB,WACrB7e,EAAK,iBAAkB4e,EAAU,KAG/B/wB,EAAON,sBACTM,EAAON,sBAAsBsxB,GAE7BhxB,EAAOT,WAAWyxB,EAAgB,QAXlC7e,EAAK,iBAAkB4e,EAAU,OAcrChB,EAASU,QAAQ90B,EAAQ,CACvBs1B,gBAA0C,IAAvB9B,EAAQ8B,YAAoC9B,EAAQ8B,WACvEC,eAAwC,IAAtB/B,EAAQ+B,WAAmC/B,EAAQ+B,UACrEC,mBAAgD,IAA1BhC,EAAQgC,eAAuChC,EAAQgC,gBAE/ER,EAAUpvB,KAAKwuB,IA+BjB3C,EAAa,CACX2C,UAAU,EACVqB,gBAAgB,EAChBC,sBAAsB,IAExBjtB,EAAG,QAjCU,WACX,GAAK4J,EAAOM,OAAOyhB,SAAnB,CAEA,GAAI/hB,EAAOM,OAAO8iB,eAGhB,IAFA,IAAME,EAAmBtjB,EAAO2S,IAAIra,UAE3B/M,EAAI,EAAGA,EAAI+3B,EAAiBz1B,OAAQtC,GAAK,EAChDq3B,EAAOU,EAAiB/3B,IAK5Bq3B,EAAO5iB,EAAO2S,IAAI,GAAI,CACpBuQ,UAAWljB,EAAOM,OAAO+iB,uBAG3BT,EAAO5iB,EAAOyJ,WAAW,GAAI,CAC3BwZ,YAAY,QAiBhB7sB,EAAG,WAba,WACdusB,EAAUp0B,SAAQ,SAAAwzB,GAChBA,EAASwB,gBAEXZ,EAAUxpB,OAAO,EAAGwpB,EAAU90B,c9CyjBnBkxB,U+CxnBA,SAASyE,GAA0BxjB,EAAQ+b,EAAgBzb,EAAQmjB,GAChF,IAAMlzB,EAAWF,IAmBjB,OAjBI2P,EAAOM,OAAO2O,gBAChBhjB,OAAOqC,KAAKm1B,GAAYl1B,SAAQ,SAAAzB,GAC9B,IAAKwT,EAAOxT,KAAwB,IAAhBwT,EAAOuR,KAAe,CACxC,IAAI6R,EAAU1jB,EAAO2S,IAAItjB,SAAX,IAAwBo0B,EAAW32B,IAAQ,GAEpD42B,KACHA,EAAUnzB,EAASnB,cAAc,QACzB2H,UAAY0sB,EAAW32B,GAC/BkT,EAAO2S,IAAI7W,OAAO4nB,IAGpBpjB,EAAOxT,GAAO42B,EACd3H,EAAejvB,GAAO42B,MAKrBpjB,ECnBM,SAASqjB,GAAT,GAKZ,IAJD3jB,EAIC,EAJDA,OACAof,EAGC,EAHDA,aACAhpB,EAEC,EAFDA,GACA+N,EACC,EADDA,KAoBA,SAASyf,EAAM5uB,GACb,IAAI2d,EAUJ,OARI3d,IACF2d,EAAMvd,EAAEJ,GAEJgL,EAAOM,OAAO2P,mBAAmC,iBAAPjb,GAAmB2d,EAAI9kB,OAAS,GAAoC,IAA/BmS,EAAO2S,IAAI3V,KAAKhI,GAAInH,SACrG8kB,EAAM3S,EAAO2S,IAAI3V,KAAKhI,KAInB2d,EAGT,SAASkR,EAASlR,EAAKmR,GACrB,IAAMxjB,EAASN,EAAOM,OAAOiM,WAEzBoG,GAAOA,EAAI9kB,OAAS,IACtB8kB,EAAImR,EAAW,WAAa,eAAexjB,EAAOyjB,eAC9CpR,EAAI,IAAyB,WAAnBA,EAAI,GAAGqR,UAAsBrR,EAAI,GAAGmR,SAAWA,GAEzD9jB,EAAOM,OAAO+M,eAAiBrN,EAAOuE,SACxCoO,EAAI3S,EAAOsb,SAAW,WAAa,eAAehb,EAAO2jB,YAK/D,SAASvR,IAEP,IAAI1S,EAAOM,OAAOqE,KAAlB,CACA,MAGI3E,EAAOuM,WAFT2X,EADF,EACEA,QAGFL,EAJA,EAEEM,QAEgBnkB,EAAO6L,cAAgB7L,EAAOM,OAAOsL,QACvDiY,EAASK,EAASlkB,EAAOgM,QAAUhM,EAAOM,OAAOsL,SAGnD,SAASwY,EAAYpxB,GACnBA,EAAEkU,mBACElH,EAAO6L,aAAgB7L,EAAOM,OAAOqE,MAAS3E,EAAOM,OAAOsL,UAChE5L,EAAOia,YACP9V,EAAK,mBAGP,SAASkgB,EAAYrxB,GACnBA,EAAEkU,mBACElH,EAAOgM,OAAUhM,EAAOM,OAAOqE,MAAS3E,EAAOM,OAAOsL,UAC1D5L,EAAO6Z,YACP1V,EAAK,mBAGP,SAAS2K,IACP,IAAMxO,EAASN,EAAOM,OAAOiM,WAK7B,GAJAvM,EAAOM,OAAOiM,WAAaiX,GAA0BxjB,EAAQA,EAAO+b,eAAexP,WAAYvM,EAAOM,OAAOiM,WAAY,CACvHC,OAAQ,qBACRC,OAAQ,uBAEJnM,EAAOkM,QAAUlM,EAAOmM,OAA9B,CACA,IAAMyX,EAAUN,EAAMtjB,EAAOkM,QACvB2X,EAAUP,EAAMtjB,EAAOmM,QAEzByX,GAAWA,EAAQr2B,OAAS,GAC9Bq2B,EAAQ9tB,GAAG,QAASiuB,GAGlBF,GAAWA,EAAQt2B,OAAS,GAC9Bs2B,EAAQ/tB,GAAG,QAASguB,GAGtBn4B,OAAOkb,OAAOnH,EAAOuM,WAAY,CAC/B2X,UACA1X,OAAQ0X,GAAWA,EAAQ,GAC3BC,UACA1X,OAAQ0X,GAAWA,EAAQ,KAGxBnkB,EAAOuE,UACN2f,GAASA,EAAQ5tB,SAASgK,EAAO2jB,WACjCE,GAASA,EAAQ7tB,SAASgK,EAAO2jB,aAIzC,SAAS3C,IACP,MAGIthB,EAAOuM,WAFT2X,EADF,EACEA,QACAC,EAFF,EAEEA,QAGED,GAAWA,EAAQr2B,SACrBq2B,EAAQnrB,IAAI,QAASsrB,GACrBH,EAAQttB,YAAYoJ,EAAOM,OAAOiM,WAAWwX,gBAG3CI,GAAWA,EAAQt2B,SACrBs2B,EAAQprB,IAAI,QAASqrB,GACrBD,EAAQvtB,YAAYoJ,EAAOM,OAAOiM,WAAWwX,gBAlHjD3E,EAAa,CACX7S,WAAY,CACVC,OAAQ,KACRC,OAAQ,KACR6X,aAAa,EACbP,cAAe,yBACfQ,YAAa,uBACbN,UAAW,qBACXO,wBAAyB,gCAG7BxkB,EAAOuM,WAAa,CAClBC,OAAQ,KACR0X,QAAS,KACTzX,OAAQ,KACR0X,QAAS,MAuGX/tB,EAAG,QAAQ,YACgC,IAArC4J,EAAOM,OAAOiM,WAAWhI,QAE3BgY,KAEAzN,IACA4D,QAGJtc,EAAG,+BAA+B,WAChCsc,OAEFtc,EAAG,WAAW,WACZkrB,OAEFlrB,EAAG,kBAAkB,WACnB,MAGI4J,EAAOuM,WAFT2X,EADF,EACEA,QACAC,EAFF,EAEEA,QAGED,GACFA,EAAQlkB,EAAOuE,QAAU,cAAgB,YAAYvE,EAAOM,OAAOiM,WAAW0X,WAG5EE,GACFA,EAAQnkB,EAAOuE,QAAU,cAAgB,YAAYvE,EAAOM,OAAOiM,WAAW0X,cAGlF7tB,EAAG,SAAS,SAACquB,EAAIzxB,GACf,MAGIgN,EAAOuM,WAFT2X,EADF,EACEA,QACAC,EAFF,EAEEA,QAEIO,EAAW1xB,EAAErF,OAEnB,GAAIqS,EAAOM,OAAOiM,WAAW+X,cAAgBlvB,EAAEsvB,GAAUrsB,GAAG8rB,KAAa/uB,EAAEsvB,GAAUrsB,GAAG6rB,GAAU,CAChG,GAAIlkB,EAAO2kB,YAAc3kB,EAAOM,OAAOqkB,YAAc3kB,EAAOM,OAAOqkB,WAAWC,YAAc5kB,EAAO2kB,WAAW3vB,KAAO0vB,GAAY1kB,EAAO2kB,WAAW3vB,GAAGgC,SAAS0tB,IAAY,OAC3K,IAAIG,EAEAX,EACFW,EAAWX,EAAQptB,SAASkJ,EAAOM,OAAOiM,WAAWgY,aAC5CJ,IACTU,EAAWV,EAAQrtB,SAASkJ,EAAOM,OAAOiM,WAAWgY,cAIrDpgB,GADe,IAAb0gB,EACG,iBAEA,kBAGHX,GACFA,EAAQjtB,YAAY+I,EAAOM,OAAOiM,WAAWgY,aAG3CJ,GACFA,EAAQltB,YAAY+I,EAAOM,OAAOiM,WAAWgY,iBAKnD,IAMMhI,EAAU,WACdvc,EAAO2S,IAAIrc,SAAS0J,EAAOM,OAAOiM,WAAWiY,yBAC7ClD,KAGFr1B,OAAOkb,OAAOnH,EAAOuM,WAAY,CAC/BiQ,OAZa,WACbxc,EAAO2S,IAAI/b,YAAYoJ,EAAOM,OAAOiM,WAAWiY,yBAChD1V,IACA4D,KAUA6J,UACA7J,SACA5D,OACAwS,YC5MW,SAASwD,GAAkBvuB,GACxC,YADsD,IAAdA,MAAU,IAClD,IAAWA,EAAQf,OAAO4I,QAAQ,cAAe,QAChDA,QAAQ,KAAM,KCCF,SAAS2mB,GAAT,GAKZ,IAuCGC,EA3CJhlB,EAIC,EAJDA,OACAof,EAGC,EAHDA,aACAhpB,EAEC,EAFDA,GACA+N,EACC,EADDA,KAEM8gB,EAAM,oBACZ7F,EAAa,CACXuF,WAAY,CACV3vB,GAAI,KACJkwB,cAAe,OACfN,WAAW,EACXN,aAAa,EACba,aAAc,KACdC,kBAAmB,KACnBC,eAAgB,KAChBC,aAAc,KACdC,qBAAqB,EACrBtgB,KAAM,UAENugB,gBAAgB,EAChBC,mBAAoB,EACpBC,sBAAuB,SAAAC,GAAM,OAAIA,GACjCC,oBAAqB,SAAAD,GAAM,OAAIA,GAC/BE,YAAgBZ,EAAL,UACXa,kBAAsBb,EAAL,iBACjBc,cAAkBd,EAAL,IACbe,aAAiBf,EAAL,WACZgB,WAAehB,EAAL,SACVV,YAAgBU,EAAL,UACXiB,qBAAyBjB,EAAL,oBACpBkB,yBAA6BlB,EAAL,wBACxBmB,eAAmBnB,EAAL,aACdhB,UAAcgB,EAAL,QACToB,gBAAoBpB,EAAL,cACfqB,cAAkBrB,EAAL,YACbsB,wBAA4BtB,EAAL,eAG3BjlB,EAAO2kB,WAAa,CAClB3vB,GAAI,KACJ2d,IAAK,KACL6T,QAAS,IAGX,IAAIC,EAAqB,EAEzB,SAASC,IACP,OAAQ1mB,EAAOM,OAAOqkB,WAAW3vB,KAAOgL,EAAO2kB,WAAW3vB,KAAOgL,EAAO2kB,WAAWhS,KAAwC,IAAjC3S,EAAO2kB,WAAWhS,IAAI9kB,OAGlH,SAAS84B,EAAeC,EAAWC,GACjC,IACEf,EACE9lB,EAAOM,OAAOqkB,WADhBmB,kBAEFc,EAAUC,KAAYvwB,SAAYwvB,EAAlC,IAAuDe,GAAYA,KAAYvwB,SAAYwvB,EAA3F,IAAgHe,EAAhH,IAA4HA,GAG9H,SAASnU,IAEP,IAAMtK,EAAMpI,EAAOoI,IACb9H,EAASN,EAAOM,OAAOqkB,WAC7B,IAAI+B,IAAJ,CACA,IAGI7lB,EAHE4S,EAAezT,EAAO8L,SAAW9L,EAAOM,OAAOwL,QAAQvH,QAAUvE,EAAO8L,QAAQC,OAAOle,OAASmS,EAAO+L,OAAOle,OAC9G8kB,EAAM3S,EAAO2kB,WAAWhS,IAGxBmU,EAAQ9mB,EAAOM,OAAOqE,KAAO1D,KAAKwV,MAAMhD,EAAqC,EAAtBzT,EAAOsQ,cAAoBtQ,EAAOM,OAAOkL,gBAAkBxL,EAAO6M,SAAShf,OAkBxI,GAhBImS,EAAOM,OAAOqE,OAChB9D,EAAUI,KAAKwV,MAAMzW,EAAOiE,YAAcjE,EAAOsQ,cAAgBtQ,EAAOM,OAAOkL,iBAEjEiI,EAAe,EAA0B,EAAtBzT,EAAOsQ,eACtCzP,GAAW4S,EAAqC,EAAtBzT,EAAOsQ,cAG/BzP,EAAUimB,EAAQ,IAAGjmB,GAAWimB,GAChCjmB,EAAU,GAAsC,YAAjCb,EAAOM,OAAOymB,iBAA8BlmB,EAAUimB,EAAQjmB,IAEjFA,OADqC,IAArBb,EAAO6V,UACb7V,EAAO6V,UAEP7V,EAAOiE,aAAe,EAId,YAAhB3D,EAAO2E,MAAsBjF,EAAO2kB,WAAW6B,SAAWxmB,EAAO2kB,WAAW6B,QAAQ34B,OAAS,EAAG,CAClG,IACIm5B,EACAxM,EACAyM,EAHET,EAAUxmB,EAAO2kB,WAAW6B,QA0BlC,GArBIlmB,EAAOklB,iBACTR,EAAawB,EAAQ5qB,GAAG,GAAGoE,EAAOiJ,eAAiB,aAAe,gBAAe,GACjF0J,EAAI3X,IAAIgF,EAAOiJ,eAAiB,QAAU,SAAa+b,GAAc1kB,EAAOmlB,mBAAqB,GAAjG,MAEInlB,EAAOmlB,mBAAqB,QAA8B1xB,IAAzBiM,EAAOkE,iBAC1CuiB,GAAsB5lB,GAAWb,EAAOkE,cAAgBlE,EAAOsQ,cAAgB,IAEtDhQ,EAAOmlB,mBAAqB,EACnDgB,EAAqBnmB,EAAOmlB,mBAAqB,EACxCgB,EAAqB,IAC9BA,EAAqB,IAIzBO,EAAa/lB,KAAKC,IAAIL,EAAU4lB,EAAoB,GAEpDQ,IADAzM,EAAYwM,GAAc/lB,KAAKE,IAAIqlB,EAAQ34B,OAAQyS,EAAOmlB,oBAAsB,IACxDuB,GAAc,GAGxCR,EAAQ5vB,YAAY,CAAC,GAAI,QAAS,aAAc,QAAS,aAAc,SAASH,KAAI,SAAAywB,GAAM,SAAO5mB,EAAOwlB,kBAAoBoB,KAAU7oB,KAAK,MAEvIsU,EAAI9kB,OAAS,EACf24B,EAAQtrB,MAAK,SAAAisB,GACX,IAAMC,EAAUhyB,EAAE+xB,GACZE,EAAcD,EAAQjsB,QAExBksB,IAAgBxmB,GAClBumB,EAAQ9wB,SAASgK,EAAOwlB,mBAGtBxlB,EAAOklB,iBACL6B,GAAeL,GAAcK,GAAe7M,GAC9C4M,EAAQ9wB,SAAYgK,EAAOwlB,kBAA3B,SAGEuB,IAAgBL,GAClBL,EAAeS,EAAS,QAGtBC,IAAgB7M,GAClBmM,EAAeS,EAAS,gBAIzB,CACL,IAAMA,EAAUZ,EAAQ5qB,GAAGiF,GACrBwmB,EAAcD,EAAQjsB,QAG5B,GAFAisB,EAAQ9wB,SAASgK,EAAOwlB,mBAEpBxlB,EAAOklB,eAAgB,CAIzB,IAHA,IAAM8B,EAAwBd,EAAQ5qB,GAAGorB,GACnCO,EAAuBf,EAAQ5qB,GAAG4e,GAE/BjvB,EAAIy7B,EAAYz7B,GAAKivB,EAAWjvB,GAAK,EAC5Ci7B,EAAQ5qB,GAAGrQ,GAAG+K,SAAYgK,EAAOwlB,kBAAjC,SAGF,GAAI9lB,EAAOM,OAAOqE,KAChB,GAAI0iB,GAAeb,EAAQ34B,OAAQ,CACjC,IAAK,IAAItC,EAAI+U,EAAOmlB,mBAAoBl6B,GAAK,EAAGA,GAAK,EACnDi7B,EAAQ5qB,GAAG4qB,EAAQ34B,OAAStC,GAAG+K,SAAYgK,EAAOwlB,kBAAlD,SAGFU,EAAQ5qB,GAAG4qB,EAAQ34B,OAASyS,EAAOmlB,mBAAqB,GAAGnvB,SAAYgK,EAAOwlB,kBAA9E,cAEAa,EAAeW,EAAuB,QACtCX,EAAeY,EAAsB,aAGvCZ,EAAeW,EAAuB,QACtCX,EAAeY,EAAsB,SAK3C,GAAIjnB,EAAOklB,eAAgB,CACzB,IAAMgC,EAAuBvmB,KAAKE,IAAIqlB,EAAQ34B,OAAQyS,EAAOmlB,mBAAqB,GAC5EgC,GAAiBzC,EAAawC,EAAuBxC,GAAc,EAAIiC,EAAWjC,EAClF0C,EAAatf,EAAM,QAAU,OACnCoe,EAAQxrB,IAAIgF,EAAOiJ,eAAiBye,EAAa,MAAUD,EAA3D,OASJ,GALoB,aAAhBnnB,EAAO2E,OACT0N,EAAI3V,KAAK8nB,GAAkBxkB,EAAO0lB,eAAe5qB,KAAKkF,EAAOolB,sBAAsB7kB,EAAU,IAC7F8R,EAAI3V,KAAK8nB,GAAkBxkB,EAAO2lB,aAAa7qB,KAAKkF,EAAOslB,oBAAoBkB,KAG7D,gBAAhBxmB,EAAO2E,KAAwB,CACjC,IAAI0iB,EAGFA,EADErnB,EAAOilB,oBACcvlB,EAAOiJ,eAAiB,WAAa,aAErCjJ,EAAOiJ,eAAiB,aAAe,WAGhE,IAAM2e,GAAS/mB,EAAU,GAAKimB,EAC1Be,EAAS,EACTC,EAAS,EAEgB,eAAzBH,EACFE,EAASD,EAETE,EAASF,EAGXjV,EAAI3V,KAAK8nB,GAAkBxkB,EAAO4lB,uBAAuBzuB,UAAzD,6BAAgGowB,EAAhG,YAAkHC,EAAlH,KAA6HpwB,WAAWsI,EAAOM,OAAOC,OAGpI,WAAhBD,EAAO2E,MAAqB3E,EAAOglB,cACrC3S,EAAIpd,KAAK+K,EAAOglB,aAAatlB,EAAQa,EAAU,EAAGimB,IAClD3iB,EAAK,mBAAoBwO,EAAI,KAE7BxO,EAAK,mBAAoBwO,EAAI,IAG3B3S,EAAOM,OAAO+M,eAAiBrN,EAAOuE,SACxCoO,EAAI3S,EAAOsb,SAAW,WAAa,eAAehb,EAAO2jB,YAI7D,SAAS8D,IAEP,IAAMznB,EAASN,EAAOM,OAAOqkB,WAC7B,IAAI+B,IAAJ,CACA,IAAMjT,EAAezT,EAAO8L,SAAW9L,EAAOM,OAAOwL,QAAQvH,QAAUvE,EAAO8L,QAAQC,OAAOle,OAASmS,EAAO+L,OAAOle,OAC9G8kB,EAAM3S,EAAO2kB,WAAWhS,IAC1BqV,EAAiB,GAErB,GAAoB,YAAhB1nB,EAAO2E,KAAoB,CAC7B,IAAIgjB,EAAkBjoB,EAAOM,OAAOqE,KAAO1D,KAAKwV,MAAMhD,EAAqC,EAAtBzT,EAAOsQ,cAAoBtQ,EAAOM,OAAOkL,gBAAkBxL,EAAO6M,SAAShf,OAE5ImS,EAAOM,OAAO4H,UAAYlI,EAAOM,OAAO4H,SAAS3D,UAAYvE,EAAOM,OAAOqE,MAAQsjB,EAAkBxU,IACvGwU,EAAkBxU,GAGpB,IAAK,IAAIloB,EAAI,EAAGA,EAAI08B,EAAiB18B,GAAK,EACpC+U,EAAO6kB,aACT6C,GAAkB1nB,EAAO6kB,aAAaz5B,KAAKsU,EAAQzU,EAAG+U,EAAOulB,aAE7DmC,GAAkB,IAAI1nB,EAAO4kB,cAAf,WAAuC5kB,EAAOulB,YAA9C,OAAgEvlB,EAAO4kB,cAAvE,IAIlBvS,EAAIpd,KAAKyyB,GACThoB,EAAO2kB,WAAW6B,QAAU7T,EAAI3V,KAAK8nB,GAAkBxkB,EAAOulB,cAG5C,aAAhBvlB,EAAO2E,OAEP+iB,EADE1nB,EAAO+kB,eACQ/kB,EAAO+kB,eAAe35B,KAAKsU,EAAQM,EAAO0lB,aAAc1lB,EAAO2lB,YAE/D,gBAAgB3lB,EAAO0lB,aAAvB,4BAAyE1lB,EAAO2lB,WAAhF,YAGnBtT,EAAIpd,KAAKyyB,IAGS,gBAAhB1nB,EAAO2E,OAEP+iB,EADE1nB,EAAO8kB,kBACQ9kB,EAAO8kB,kBAAkB15B,KAAKsU,EAAQM,EAAO4lB,sBAE7C,gBAAgB5lB,EAAO4lB,qBAA1B,YAGhBvT,EAAIpd,KAAKyyB,IAGS,WAAhB1nB,EAAO2E,MACTd,EAAK,mBAAoBnE,EAAO2kB,WAAWhS,IAAI,KAInD,SAAS7D,IACP9O,EAAOM,OAAOqkB,WAAanB,GAA0BxjB,EAAQA,EAAO+b,eAAe4I,WAAY3kB,EAAOM,OAAOqkB,WAAY,CACvH3vB,GAAI,sBAEN,IAAMsL,EAASN,EAAOM,OAAOqkB,WAC7B,GAAKrkB,EAAOtL,GAAZ,CACA,IAAI2d,EAAMvd,EAAEkL,EAAOtL,IACA,IAAf2d,EAAI9kB,SAEJmS,EAAOM,OAAO2P,mBAA0C,iBAAd3P,EAAOtL,IAAmB2d,EAAI9kB,OAAS,IACnF8kB,EAAM3S,EAAO2S,IAAI3V,KAAKsD,EAAOtL,KAErBnH,OAAS,IACf8kB,EAAMA,EAAIxd,QAAO,SAAAH,GACf,OAAII,EAAEJ,GAAIsD,QAAQ,WAAW,KAAO0H,EAAOhL,OAM7B,YAAhBsL,EAAO2E,MAAsB3E,EAAOskB,WACtCjS,EAAIrc,SAASgK,EAAO8lB,gBAGtBzT,EAAIrc,SAASgK,EAAOylB,cAAgBzlB,EAAO2E,MAC3C0N,EAAIrc,SAAS0J,EAAOiJ,eAAiB3I,EAAO+lB,gBAAkB/lB,EAAOgmB,eAEjD,YAAhBhmB,EAAO2E,MAAsB3E,EAAOklB,iBACtC7S,EAAIrc,SAAJ,GAAgBgK,EAAOylB,cAAgBzlB,EAAO2E,KAA9C,YACAwhB,EAAqB,EAEjBnmB,EAAOmlB,mBAAqB,IAC9BnlB,EAAOmlB,mBAAqB,IAIZ,gBAAhBnlB,EAAO2E,MAA0B3E,EAAOilB,qBAC1C5S,EAAIrc,SAASgK,EAAO6lB,0BAGlB7lB,EAAOskB,WACTjS,EAAIvc,GAAG,QAAS0uB,GAAkBxkB,EAAOulB,cAAc,SAAiB7yB,GACtEA,EAAEkU,iBACF,IAAI/L,EAAQ/F,EAAEnE,MAAMkK,QAAU6E,EAAOM,OAAOkL,eACxCxL,EAAOM,OAAOqE,OAAMxJ,GAAS6E,EAAOsQ,cACxCtQ,EAAOoM,QAAQjR,MAInBlP,OAAOkb,OAAOnH,EAAO2kB,WAAY,CAC/BhS,MACA3d,GAAI2d,EAAI,KAGL3S,EAAOuE,SACVoO,EAAIrc,SAASgK,EAAO2jB,aAIxB,SAAS3C,IACP,IAAMhhB,EAASN,EAAOM,OAAOqkB,WAC7B,IAAI+B,IAAJ,CACA,IAAM/T,EAAM3S,EAAO2kB,WAAWhS,IAC9BA,EAAI/b,YAAY0J,EAAOikB,aACvB5R,EAAI/b,YAAY0J,EAAOylB,cAAgBzlB,EAAO2E,MAC9C0N,EAAI/b,YAAYoJ,EAAOiJ,eAAiB3I,EAAO+lB,gBAAkB/lB,EAAOgmB,eACpEtmB,EAAO2kB,WAAW6B,SAAWxmB,EAAO2kB,WAAW6B,QAAQ5vB,aAAaoJ,EAAO2kB,WAAW6B,QAAQ5vB,YAAY0J,EAAOwlB,mBAEjHxlB,EAAOskB,WACTjS,EAAI5Z,IAAI,QAAS+rB,GAAkBxkB,EAAOulB,eAI9CzvB,EAAG,QAAQ,YACgC,IAArC4J,EAAOM,OAAOqkB,WAAWpgB,QAE3BgY,KAEAzN,IACAiZ,IACArV,QAGJtc,EAAG,qBAAqB,YAClB4J,EAAOM,OAAOqE,WAEqB,IAArB3E,EAAO6V,YADvBnD,OAKJtc,EAAG,mBAAmB,WACf4J,EAAOM,OAAOqE,MACjB+N,OAGJtc,EAAG,sBAAsB,WACnB4J,EAAOM,OAAOqE,OAChBojB,IACArV,QAGJtc,EAAG,wBAAwB,WACpB4J,EAAOM,OAAOqE,OACjBojB,IACArV,QAGJtc,EAAG,WAAW,WACZkrB,OAEFlrB,EAAG,kBAAkB,WACnB,IACEuc,EACE3S,EAAO2kB,WADThS,IAGEA,GACFA,EAAI3S,EAAOuE,QAAU,cAAgB,YAAYvE,EAAOM,OAAOqkB,WAAWV,cAG9E7tB,EAAG,eAAe,WAChBsc,OAEFtc,EAAG,SAAS,SAACquB,EAAIzxB,GACf,IAAM0xB,EAAW1xB,EAAErF,OAEjBglB,EACE3S,EAAO2kB,WADThS,IAGF,GAAI3S,EAAOM,OAAOqkB,WAAW3vB,IAAMgL,EAAOM,OAAOqkB,WAAWL,aAAe3R,GAAOA,EAAI9kB,OAAS,IAAMuH,EAAEsvB,GAAU5tB,SAASkJ,EAAOM,OAAOqkB,WAAWkB,aAAc,CAC/J,GAAI7lB,EAAOuM,aAAevM,EAAOuM,WAAWC,QAAUkY,IAAa1kB,EAAOuM,WAAWC,QAAUxM,EAAOuM,WAAWE,QAAUiY,IAAa1kB,EAAOuM,WAAWE,QAAS,OACnK,IAAMoY,EAAWlS,EAAI7b,SAASkJ,EAAOM,OAAOqkB,WAAWJ,aAGrDpgB,GADe,IAAb0gB,EACG,iBAEA,kBAGPlS,EAAI1b,YAAY+I,EAAOM,OAAOqkB,WAAWJ,iBAI7C,IAYMhI,EAAU,WACdvc,EAAO2S,IAAIrc,SAAS0J,EAAOM,OAAOqkB,WAAW4B,yBAEzCvmB,EAAO2kB,WAAWhS,KACpB3S,EAAO2kB,WAAWhS,IAAIrc,SAAS0J,EAAOM,OAAOqkB,WAAW4B,yBAG1DjF,KAGFr1B,OAAOkb,OAAOnH,EAAO2kB,WAAY,CAC/BnI,OAvBa,WACbxc,EAAO2S,IAAI/b,YAAYoJ,EAAOM,OAAOqkB,WAAW4B,yBAE5CvmB,EAAO2kB,WAAWhS,KACpB3S,EAAO2kB,WAAWhS,IAAI/b,YAAYoJ,EAAOM,OAAOqkB,WAAW4B,yBAG7DzX,IACAiZ,IACArV,KAeA6J,UACAwL,SACArV,SACA5D,OACAwS,YCjbW,SAAS4G,GAAT,GAKZ,IACGC,EALJnoB,EAIC,EAJDA,OACAof,EAGC,EAHDA,aACAhpB,EAEC,EAFDA,GACA+N,EACC,EADDA,KAmBA,SAASiJ,IACP,IAAKpN,EAAOiT,KAGV,OAFAjT,EAAOiN,SAASC,SAAU,OAC1BlN,EAAOiN,SAASE,QAAS,GAI3B,IAAMib,EAAiBpoB,EAAO+L,OAAOnQ,GAAGoE,EAAOiE,aAC3CxG,EAAQuC,EAAOM,OAAO2M,SAASxP,MAE/B2qB,EAAejxB,KAAK,0BACtBsG,EAAQ2qB,EAAejxB,KAAK,yBAA2B6I,EAAOM,OAAO2M,SAASxP,OAGhFjM,aAAa22B,GACbA,EAAU3qB,GAAS,WACjB,IAAI6qB,EAEAroB,EAAOM,OAAO2M,SAASqb,iBACrBtoB,EAAOM,OAAOqE,MAChB3E,EAAO4E,UACPyjB,EAAiBroB,EAAOia,UAAUja,EAAOM,OAAOC,OAAO,GAAM,GAC7D4D,EAAK,aACKnE,EAAO6L,YAGP7L,EAAOM,OAAO2M,SAASsb,gBAIjCC,KAHAH,EAAiBroB,EAAOoM,QAAQpM,EAAO+L,OAAOle,OAAS,EAAGmS,EAAOM,OAAOC,OAAO,GAAM,GACrF4D,EAAK,cAJLkkB,EAAiBroB,EAAOia,UAAUja,EAAOM,OAAOC,OAAO,GAAM,GAC7D4D,EAAK,aAOEnE,EAAOM,OAAOqE,MACvB3E,EAAO4E,UACPyjB,EAAiBroB,EAAO6Z,UAAU7Z,EAAOM,OAAOC,OAAO,GAAM,GAC7D4D,EAAK,aACKnE,EAAOgM,MAGPhM,EAAOM,OAAO2M,SAASsb,gBAIjCC,KAHAH,EAAiBroB,EAAOoM,QAAQ,EAAGpM,EAAOM,OAAOC,OAAO,GAAM,GAC9D4D,EAAK,cAJLkkB,EAAiBroB,EAAO6Z,UAAU7Z,EAAOM,OAAOC,OAAO,GAAM,GAC7D4D,EAAK,cAQHnE,EAAOM,OAAOoE,SAAW1E,EAAOiN,SAASC,UAA2C,IAAnBmb,IAAfjb,MAGrD3P,GAGL,SAAS2Q,IACP,YAAuB,IAAZ+Z,KACPnoB,EAAOiN,SAASC,UACpBlN,EAAOiN,SAASC,SAAU,EAC1B/I,EAAK,iBACLiJ,KACO,IAGT,SAASob,IACP,QAAKxoB,EAAOiN,SAASC,eACE,IAAZib,IAEPA,IACF32B,aAAa22B,GACbA,OAAUp0B,GAGZiM,EAAOiN,SAASC,SAAU,EAC1B/I,EAAK,iBACE,IAGT,SAASskB,EAAMloB,GACRP,EAAOiN,SAASC,UACjBlN,EAAOiN,SAASE,SAChBgb,GAAS32B,aAAa22B,GAC1BnoB,EAAOiN,SAASE,QAAS,EAEX,IAAV5M,GAAgBP,EAAOM,OAAO2M,SAASyb,kBAIzC,CAAC,gBAAiB,uBAAuBn6B,SAAQ,SAAAoK,GAC/CqH,EAAOyJ,WAAW,GAAG/a,iBAAiBiK,EAAOgwB,OAJ/C3oB,EAAOiN,SAASE,QAAS,EACzBC,OAQJ,SAASwb,IACP,IAAMr4B,EAAWF,IAEgB,WAA7BE,EAASs4B,iBAAgC7oB,EAAOiN,SAASC,SAC3Dub,IAG+B,YAA7Bl4B,EAASs4B,iBAAiC7oB,EAAOiN,SAASE,SAC5DC,IACApN,EAAOiN,SAASE,QAAS,GAI7B,SAASwb,EAAgB31B,GAClBgN,IAAUA,EAAOmL,WAAcnL,EAAOyJ,YACvCzW,EAAErF,SAAWqS,EAAOyJ,WAAW,KACnC,CAAC,gBAAiB,uBAAuBlb,SAAQ,SAAAoK,GAC/CqH,EAAOyJ,WAAW,GAAG9a,oBAAoBgK,EAAOgwB,MAElD3oB,EAAOiN,SAASE,QAAS,EAEpBnN,EAAOiN,SAASC,QAGnBE,IAFAob,KAMJ,SAASM,IACH9oB,EAAOM,OAAO2M,SAAS8b,qBACzBP,KAEArkB,EAAK,iBACLskB,KAGF,CAAC,gBAAiB,uBAAuBl6B,SAAQ,SAAAoK,GAC/CqH,EAAOyJ,WAAW,GAAG9a,oBAAoBgK,EAAOgwB,MAIpD,SAASK,IACHhpB,EAAOM,OAAO2M,SAAS8b,uBAI3B/oB,EAAOiN,SAASE,QAAS,EACzBhJ,EAAK,kBACLiJ,KA3JFpN,EAAOiN,SAAW,CAChBC,SAAS,EACTC,QAAQ,GAEViS,EAAa,CACXnS,SAAU,CACR1I,SAAS,EACT9G,MAAO,IACPirB,mBAAmB,EACnBK,sBAAsB,EACtBR,iBAAiB,EACjBD,kBAAkB,EAClBW,mBAAmB,KA8JvB7yB,EAAG,QAAQ,WACL4J,EAAOM,OAAO2M,SAAS1I,UACzB6J,IACiB/d,IACR3B,iBAAiB,mBAAoBk6B,GAf5C5oB,EAAOM,OAAO2M,SAASgc,oBACzBjpB,EAAO2S,IAAIvc,GAAG,aAAc0yB,GAC5B9oB,EAAO2S,IAAIvc,GAAG,aAAc4yB,QAiBhC5yB,EAAG,yBAAyB,SAACquB,EAAIlkB,EAAOoY,GAClC3Y,EAAOiN,SAASC,UACdyL,IAAa3Y,EAAOM,OAAO2M,SAAS8b,qBACtC/oB,EAAOiN,SAASwb,MAAMloB,GAEtBioB,QAINpyB,EAAG,mBAAmB,WAChB4J,EAAOiN,SAASC,UACdlN,EAAOM,OAAO2M,SAAS8b,qBACzBP,IAEAC,QAINryB,EAAG,YAAY,WACT4J,EAAOM,OAAOoE,SAAW1E,EAAOiN,SAASE,SAAWnN,EAAOM,OAAO2M,SAAS8b,sBAC7E3b,OAGJhX,EAAG,WAAW,WAnCZ4J,EAAO2S,IAAI5Z,IAAI,aAAc+vB,GAC7B9oB,EAAO2S,IAAI5Z,IAAI,aAAciwB,GAqCzBhpB,EAAOiN,SAASC,SAClBsb,IAGen4B,IACR1B,oBAAoB,mBAAoBi6B,MAEnD38B,OAAOkb,OAAOnH,EAAOiN,SAAU,CAC7Bwb,QACArb,MACAgB,QACAoa,SCnOW,SAASU,GAAW5oB,GACjC,IA6CI6oB,EA5CF7Z,EASEhP,EATFgP,OACAtP,EAQEM,EARFN,OACA5J,EAOEkK,EAPFlK,GACAsU,EAMEpK,EANFoK,aACAlB,EAKElJ,EALFkJ,cACA4f,EAIE9oB,EAJF8oB,gBACAC,EAGE/oB,EAHF+oB,YACAC,EAEEhpB,EAFFgpB,gBACAC,EACEjpB,EADFipB,gBAEFnzB,EAAG,cAAc,WACf,GAAI4J,EAAOM,OAAOgP,SAAWA,EAA7B,CACAtP,EAAOxJ,WAAWjD,KAAlB,GAA0ByM,EAAOM,OAAOqQ,uBAAyBrB,GAE7D+Z,GAAeA,KACjBrpB,EAAOxJ,WAAWjD,KAAQyM,EAAOM,OAAOqQ,uBAAxC,MAGF,IAAM6Y,EAAwBJ,EAAkBA,IAAoB,GACpEn9B,OAAOkb,OAAOnH,EAAOM,OAAQkpB,GAC7Bv9B,OAAOkb,OAAOnH,EAAO+b,eAAgByN,OAEvCpzB,EAAG,gBAAgB,WACb4J,EAAOM,OAAOgP,SAAWA,GAC7B5E,OAEFtU,EAAG,iBAAiB,SAACquB,EAAI9sB,GACnBqI,EAAOM,OAAOgP,SAAWA,GAC7B9F,EAAc7R,MAEhBvB,EAAG,iBAAiB,WAClB,GAAI4J,EAAOM,OAAOgP,SAAWA,GAEzBga,EAAiB,CACnB,IAAKC,IAAoBA,IAAkBE,aAAc,OAEzDzpB,EAAO+L,OAAO7Q,MAAK,SAAAmlB,GACArgB,EAAO5K,EAAEirB,GACjBrjB,KAAK,gHAAgHnG,YAGhIyyB,QAIJlzB,EAAG,iBAAiB,WACd4J,EAAOM,OAAOgP,SAAWA,IAExBtP,EAAO+L,OAAOle,SACjBs7B,GAAyB,GAG3Bz3B,uBAAsB,WAChBy3B,GAA0BnpB,EAAO+L,QAAU/L,EAAO+L,OAAOle,SAC3D6c,IACAye,GAAyB,UCzDlB,SAASO,GAAaC,EAAcC,GACjD,OAAID,EAAaE,YACRD,EAAS5sB,KAAK2sB,EAAaE,aAAa7uB,IAAI,CACjD,sBAAuB,SACvB,8BAA+B,WAI5B4uB,ECPM,SAASE,GAAaxpB,EAAQspB,EAAU1pB,GACrD,IAAM6pB,EAAc,uBAAsB7pB,EAAO,IAAIA,EAAS,IACxD8pB,EAAmB1pB,EAAOupB,YAAcD,EAAS5sB,KAAKsD,EAAOupB,aAAeD,EAC9EK,EAAYD,EAAiB36B,SAAjB,IAA8B06B,GAO9C,OALKE,EAAUp8B,SACbo8B,EAAY70B,EAAE,mCAAkC8K,EAAO,IAAIA,EAAS,IAAvD,YACb8pB,EAAiBluB,OAAOmuB,IAGnBA,ECRM,SAASC,GAAT,GAIZ,IAHDlqB,EAGC,EAHDA,OACAof,EAEC,EAFDA,aACAhpB,EACC,EADDA,GAEAgpB,EAAa,CACX+K,gBAAiB,CACfC,OAAQ,GACRC,QAAS,EACTC,MAAO,IACP1C,MAAO,EACP2C,SAAU,EACVd,cAAc,EACdI,YAAa,QA4EjBX,GAAW,CACT5Z,OAAQ,YACRtP,SACA5J,KACAsU,aA5EmB,WAcnB,IAbA,IACS8f,EAILxqB,EAJF4C,MACQ6nB,EAGNzqB,EAHF8C,OACAiJ,EAEE/L,EAFF+L,OACAT,EACEtL,EADFsL,gBAEIhL,EAASN,EAAOM,OAAO6pB,gBACvBlhB,EAAejJ,EAAOiJ,eACtBxR,EAAYuI,EAAOI,UACnBsqB,EAASzhB,EAA4BuhB,EAAc,EAA1B/yB,EAA2CgzB,EAAe,EAA3BhzB,EACxD2yB,EAASnhB,EAAe3I,EAAO8pB,QAAU9pB,EAAO8pB,OAChDhqB,EAAYE,EAAOgqB,MAEhB/+B,EAAI,EAAGsC,EAASke,EAAOle,OAAQtC,EAAIsC,EAAQtC,GAAK,EAAG,CAC1D,IAAMq+B,EAAW7d,EAAOnQ,GAAGrQ,GACrB8oB,EAAY/I,EAAgB/f,GAE5Bo/B,GAAgBD,EADFd,EAAS,GAAGlT,kBACarC,EAAY,GAAKA,EACxDuW,EAA8C,mBAApBtqB,EAAOiqB,SAA0BjqB,EAAOiqB,SAASI,GAAgBA,EAAerqB,EAAOiqB,SACnHM,EAAU5hB,EAAemhB,EAASQ,EAAmB,EACrDE,EAAU7hB,EAAe,EAAImhB,EAASQ,EAEtCG,GAAc3qB,EAAYa,KAAKkI,IAAIyhB,GACnCP,EAAU/pB,EAAO+pB,QAEE,iBAAZA,IAAkD,IAA1BA,EAAQn2B,QAAQ,OACjDm2B,EAAUnwB,WAAWoG,EAAO+pB,SAAW,IAAMhW,GAG/C,IAAI2W,EAAa/hB,EAAe,EAAIohB,EAAUO,EAC1CK,EAAahiB,EAAeohB,EAAUO,EAAmB,EACzDhD,EAAQ,GAAK,EAAItnB,EAAOsnB,OAAS3mB,KAAKkI,IAAIyhB,GAE1C3pB,KAAKkI,IAAI8hB,GAAc,OAAOA,EAAa,GAC3ChqB,KAAKkI,IAAI6hB,GAAc,OAAOA,EAAa,GAC3C/pB,KAAKkI,IAAI4hB,GAAc,OAAOA,EAAa,GAC3C9pB,KAAKkI,IAAI0hB,GAAW,OAAOA,EAAU,GACrC5pB,KAAKkI,IAAI2hB,GAAW,OAAOA,EAAU,GACrC7pB,KAAKkI,IAAIye,GAAS,OAAOA,EAAQ,GACrC,IAAMsD,EAAiB,eAAeD,EAAlB,MAAkCD,EAAlC,MAAkDD,EAAlD,gBAA4ED,EAA5E,gBAAmGD,EAAnG,cAAwHjD,EAAxH,IAKpB,GAJkB8B,GAAappB,EAAQspB,GAC7BnyB,UAAUyzB,GACpBtB,EAAS,GAAGr6B,MAAM47B,OAAmD,EAAzClqB,KAAKkI,IAAIlI,KAAKmqB,MAAMR,IAE5CtqB,EAAOmpB,aAAc,CAEvB,IAAI4B,EAAkBpiB,EAAe2gB,EAAS5sB,KAAK,6BAA+B4sB,EAAS5sB,KAAK,4BAC5FsuB,EAAiBriB,EAAe2gB,EAAS5sB,KAAK,8BAAgC4sB,EAAS5sB,KAAK,+BAEjE,IAA3BquB,EAAgBx9B,SAClBw9B,EAAkBvB,GAAaxpB,EAAQspB,EAAU3gB,EAAe,OAAS,QAG7C,IAA1BqiB,EAAez9B,SACjBy9B,EAAiBxB,GAAaxpB,EAAQspB,EAAU3gB,EAAe,QAAU,WAGvEoiB,EAAgBx9B,SAAQw9B,EAAgB,GAAG97B,MAAMg8B,QAAUX,EAAmB,EAAIA,EAAmB,GACrGU,EAAez9B,SAAQy9B,EAAe,GAAG/7B,MAAMg8B,SAAWX,EAAmB,GAAKA,EAAmB,MAkB7GphB,cAboB,SAAA7R,GACpB,IACEkyB,EACE7pB,EAAOM,OAAO6pB,gBADhBN,aAE0BA,EAAc7pB,EAAO+L,OAAO/O,KAAK6sB,GAAe7pB,EAAO+L,QAC/DrU,WAAWC,GAAUqF,KAAK,gHAAgHtF,WAAWC,IASzK0xB,YAAa,kBAAM,GACnBD,gBAAiB,iBAAO,CACtB9e,qBAAqB,MC7F3B,IA8BYhX,GADNk4B,GA5BFC,IAAsB,EACtBC,GAAe,KACfC,IAAwB,EACxBC,GAAiB,KACjBC,GAAoB,EACpBC,IAAkC,EAClCC,GAA2B,KACzBC,IAqBAR,IAAQ,EACFl4B,GAAy7D7C,UAAUC,WAAWD,UAAUw7B,QAAQj6B,OAAOk6B,YAAj+D,2TAA2TvoB,KAAKrQ,KAAI,0kDAA0kDqQ,KAAKrQ,GAAE6pB,OAAO,EAAE,OAAKqO,IAAQ,IACp7DA,IAGT,SAASW,GAAqBC,GAG1B,OAFAV,GAAe,GACf,GAAGn4B,KAAKC,MAAMk4B,GAAcU,GACrBA,EAaX,SAASC,GAAiCD,GAGtC,OAFAL,GAA2B,GAC3B,GAAGx4B,KAAKC,MAAMu4B,GAA0BK,GACjCA,EAaX,SAASE,GAAuBF,GAG5B,OAFAR,GAAiB,GACjB,GAAGr4B,KAAKC,MAAMo4B,GAAgBQ,GACvBA,EA3DXG,IAAIC,aAAa71B,IAAI,6BAA6B,WAC9CvI,iBAAOq+B,IAAct/B,UAAW,QAAQ,SAAUu/B,GAC9C,IAAMC,EAAYJ,IAAI1rB,QAAQzU,IAAI,aAE/BugC,IACgB,SAAZA,GAkbf,SAA+BC,GACxBZ,KACC52B,EAAE,uBAAuB4H,KAAK,qBAAqBzH,KAAK,wCACxDH,EAAE,uBAAuB4H,KAAK,qBAAqBhC,IAAI,UAAU,SACjE5F,EAAE,uBAAuB4H,KAAK,qBAAqBhC,IAAI,YAAY,QACnE5F,EAAE,uBAAuB4H,KAAK,qBAAqBhC,IAAI,eAAe,SAG1E5F,EAAE,uBAAuB4H,KAAK,KAAKhC,IAAI,UAAU,QACjD5F,EAAE,aAAayB,SACfzB,EAAE,aAAa4F,IAAI,UAAU,QAE7B,IAAI6xB,EAAOC,aAAY,WACnB,GAAGF,EAAKhzB,MACJmzB,cAAcF,QAEA94B,IAAX64B,EAAKhzB,KAAgB,CACpB,IAAIozB,EAAiBT,IAAIU,MAAMC,UAAU,kCAErCF,IACAA,EAAiB,KAGrB,IACIG,EAAyB,EADX/3B,EAAEpD,QAAQ4Q,QACG,GAE3BwqB,EAAkB78B,SAAStB,eAAe,qBAE9C,GAAqB,OAAlBm+B,EACC,QAGJA,EAAkB78B,SAASnB,cAAc,QACzB2H,UAAY,oBAC5Bq2B,EAAgBv7B,GAAK,qBAEH,IAAfm6B,KACCoB,EAAgB79B,MAAMqT,MAAQuqB,EAAW,KACzCC,EAAgB79B,MAAM0kB,YAA0B,KAAXkZ,EAAkB,MAG3D,IAAIntB,EAASzP,SAASnB,cAAc,OACpC4Q,EAAOjJ,UAAY,kBACnBq2B,EAAgBlxB,YAAY8D,GAE5B,IAAIqtB,EAAiB98B,SAASnB,cAAc,OAC5Ci+B,EAAet2B,UAAY,iBAC3BiJ,EAAO9D,YAAYmxB,GAEnB,IAAI,IAAI9hC,EAAE,EAAEA,GAAG,GAAGA,IAAI,CAClB,IAAI+hC,EAAe/8B,SAASnB,cAAc,OACtCm+B,EAAWhB,IAAIU,MAAMC,UAAU,wBAAwB3hC,GACvDiiC,EAAYjB,IAAIU,MAAMC,UAAU,uBAAuB3hC,GAExDgiC,IACCD,EAAav2B,UAAY,eACzBu2B,EAAa33B,UAAY,wCAAwC63B,EAAU,YAAYD,EAAS,OAChGF,EAAenxB,YAAYoxB,IAInC,IAAIG,EAAqBl9B,SAASnB,cAAc,OAChDq+B,EAAmB12B,UAAY,qBAC/BiJ,EAAO9D,YAAYuxB,GAEnB,IAAIC,EAAqBn9B,SAASnB,cAAc,OAChDs+B,EAAmB32B,UAAY,qBAC/BiJ,EAAO9D,YAAYwxB,GAEnB,IAAIC,EAAoBp9B,SAASnB,cAAc,OAC/Cu+B,EAAkB52B,UAAY,oBAC9BiJ,EAAO9D,YAAYyxB,GAEnBv4B,EAAE,uBAAuB+G,QAAQixB,GAEjC,IAAIrO,GAAO,YAAa,CACpB9R,SAAU,CACPxP,MAAOuvB,GAEVroB,MAAM,EACN6K,aAAc,GACdF,OAAQ,YACRtC,gBAAgB,EAChBD,cAAe,EACfod,gBAAiB,CACbC,OAAQ,EACRE,MAAO,IACPC,SAAU,EACVd,cAAc,EACdY,QAAQ,GAEZ1F,WAAY,CACR3vB,GAAI,qBACJiQ,KAAM,WAEVsH,WAAY,CACRC,OAAQ,sBACRC,OAAQ,uBAEZhhB,QAAS,CAACy+B,GAAiBvG,GAAYoB,GAAYmD,MA5dvE,WACI,IAA2B,IAAxByD,GACCA,IAAwB,EACjBY,IAAIqB,MACN5wB,KAAK,kBADH,OAEI,eACN6wB,KAAKvB,GAAuBv/B,KAAKkE,OAyd9B68B,GA/fhB,WACI,IAAyB,IAAtBrC,GACCA,IAAsB,EACfc,IAAIqB,MACN5wB,KAAK,oBADH,OAEI,eACN6wB,KAAK1B,GAAqBp/B,KAAKkE,OA0f5B88B,GAhfhB,WACI,IAAqC,IAAlCjC,GACCA,IAAkC,EAC3BS,IAAIqB,MACR5wB,KAAK,4BADD,OAEE,eACN6wB,KAAKxB,GAAiCt/B,KAAKkE,OA2etC+8B,GAEA,IAAIC,EAAgBnB,aAAY,WAxHhD,IACQoB,EAwH8B,OAAfxC,IAAwC,OAAjBE,IAAoD,OAA3BG,KAC/CgB,cAAckB,GAEuB,IAAlC74B,EAAE,uBAAuBvH,UA7dpD,WACI,IAAMsgC,EAAU/4B,EAAE,YAElB,GAAqC,IAAlCA,EAAE,uBAAuBvH,OAAW,CACnC,IAAIu/B,EAAkB78B,SAASnB,cAAc,OAC7Cg+B,EAAgBr2B,UAAY,qBAC5Bq2B,EAAgBv7B,GAAK,qBAErB,IAAImO,EAASzP,SAASnB,cAAc,OACpC4Q,EAAOjJ,UAAY,mBAEnB,IAAIq3B,EAAwB79B,SAASnB,cAAc,OACnDg/B,EAAsBr3B,UAAY,wBAElCq2B,EAAgBlxB,YAAYkyB,GAC5BA,EAAsBlyB,YAAY8D,GAElC,IAAIqtB,EAAiB98B,SAASnB,cAAc,OAC5Ci+B,EAAet2B,UAAY,iBAC3Bs2B,EAAex7B,GAAK,mBACpBmO,EAAO9D,YAAYmxB,GAEnB,IAAI,IAAI9hC,EAAE,EAAEA,EAAE4iC,EAAQtgC,OAAOtC,IAAI,CAC7B,IAAI8iC,EAAMF,EAAQ5iC,GACd+iC,EAASl5B,EAAEi5B,GAAKrxB,KAAK,KAAK7F,KAAK,QAC/Bo3B,EAAgBn5B,EAAEi5B,GAAKrzB,IAAI,cAC3BgpB,EAAU5uB,EAAEi5B,GAAKrxB,KAAK,iBAAiB5B,OACvCozB,EAAep5B,EAAEi5B,GAAKrxB,KAAK,iBAAiBhC,IAAI,SAQhDsyB,GAPUl4B,EAAEi5B,GAAKrxB,KAAK,wBAAwB5B,OAC/BhG,EAAEi5B,GAAKrxB,KAAK,wBAAwBhC,IAAI,SAMxCzK,SAASnB,cAAc,QAC1Ck+B,EAAav2B,UAAY,gCACzBu2B,EAAa33B,UAAY,YAAY24B,EAAO,kBAAkBtC,GAAa,gCAAgC,0BAA0B,uBAAuBuC,EAAc,wIAAwIC,EAAa,KAAKxK,EAAQ,mBAE5UqJ,EAAenxB,YAAYoxB,GAG/Bl4B,EAAE,yCAAyC+G,QAAQixB,GACnDh4B,EAAEg5B,GAAuBjyB,QAAQ,+EACjC/G,EAAEg5B,GAAuBtyB,OAAO,koCAEhC1G,EAAE,aAAayB,UAEG,IAAfm1B,KACC52B,EAAE,QAAQ4F,IAAI,aAAa,UAC3B5F,EAAE,gBAAgB4F,IAAI,aAAa,QACnC5F,EAAE,gBAAgB4F,IAAI,aAAa,KAGvC,IAAI+jB,GAAO,aAAc,CACrBpa,MAAK,EACL6K,aAAcwc,GAAa,GAAG,GAC9Bjf,cAAeif,GAAa,EAAE,EAC9B/e,SAAU,CACRxP,MAAO,IACPsrB,sBAAsB,GAExBt9B,QAAS,CAACy8B,MAOtB,WACI,IAAIuG,EAAwBl+B,SAAStB,eAAe,yBAEpD,GAA2B,OAAxBw/B,EAA6B,EAC5BA,EAAwBl+B,SAASnB,cAAc,QACzByC,GAAK,wBAC3B48B,EAAsB94B,UAAY,4DAClC84B,EAAsB13B,UAAY,wBAClC3B,EAAE,uBAAuB0G,OAAO2yB,GAEhC,IAAIzuB,EAASzP,SAASnB,cAAc,OACpC4Q,EAAOjJ,UAAY,wBACnB3B,EAAE,uBAAuB0G,OAAOkE,GAEhC,IAAIqtB,EAAiB98B,SAASnB,cAAc,OAC5Ci+B,EAAet2B,UAAY,iBAC3BiJ,EAAO9D,YAAYmxB,GAEnB,IAAI,IAAI9hC,EAAE,EAAEA,EAAEmgC,GAAa79B,OAAOtC,IAAI,CAClC,IAAImjC,EAAehD,GAAangC,GAE5BojC,GADeD,EAAa5iC,OACTgnB,SAAS4b,EAAaE,YAAY,QACrDC,EAAqB,OAAOH,EAAaI,MAAM,KAE/CxB,EAAe/8B,SAASnB,cAAc,OAC1Ck+B,EAAav2B,UAAY,gCACzBu2B,EAAa33B,UAAY,wEAAwEk5B,EAAmB,2XAA2XF,EAAiB,4BAEhgBtB,EAAenxB,YAAYoxB,GAG/B,IAAIvO,GAAO,kBAAmB,CAC1Bpa,MAAK,EACL6K,aAAcwc,GAAa,GAAG,GAC9Bjf,cAAeif,GAAa,EAAE,KAtClC+C,IA6ZoBC,GA1N5B,WACqC55B,EAAE,uBAAuB4F,IAAI,UAA9D,IAEIi0B,EAAiB1+B,SAASnB,cAAc,OAC5C6/B,EAAel4B,UAAY,iBAC3Bk4B,EAAe1/B,MAAMuT,OAAS1N,EAAE,uBAAuB4F,IAAI,UAK3Di0B,EAAet5B,UAAY,4GAA4Gq2B,GAAa,GAAG,GAA5H,gzCAE3B52B,EAAE,8BAA8B+G,QAAQ8yB,GAExC,IAAMp3B,EAAYm0B,GAAa,WAAW,QACtCkD,EAAc3+B,SAAStB,eAAe,eAE1CmG,EAAE,sBAAsBgB,GAAGyB,GAAW,WAClCq3B,EAAY7gC,IAAM,GAElBkD,YAAW,WACP,IAAI49B,EAAgBvD,GAAeC,IAAmBqB,UAAU,SAChEgC,EAAY7gC,IAAM8gC,IACpB,QAGN/5B,EAAE,mBAAmBgB,GAAGyB,GAAW,WAI/B,GAFAg0B,UAEuC93B,IAApC63B,GAAeC,IAA+B,CAC7C,IAAIsD,EAAgBvD,GAAeC,IAAmBqB,UAAU,SAChEgC,EAAY7gC,IAAM8gC,EAGtB/5B,EAAE,oBAAoB4F,IAAI,QAAQ,SACOjH,IAAtC63B,GAAeC,GAAkB,GAChCz2B,EAAE,oBAAoB4F,IAAI,QAAQ,QAElC5F,EAAE,oBAAoB4F,IAAI,QAAQ,OAI1C5F,EAAE,mBAAmBgB,GAAGyB,GAAW,WAG/B,GAFAg0B,UAEuC93B,IAApC63B,GAAeC,IAA+B,CAC7C,IAAIsD,EAAgBvD,GAAeC,IAAmBqB,UAAU,SAChEgC,EAAY7gC,IAAM8gC,EAGtB/5B,EAAE,oBAAoB4F,IAAI,QAAQ,SACOjH,IAAtC63B,GAAeC,GAAkB,GAChCz2B,EAAE,oBAAoB4F,IAAI,QAAQ,QAElC5F,EAAE,oBAAoB4F,IAAI,QAAQ,OAoKlBo0B,GApJ5B,WACqCh6B,EAAE,uBAAuB4F,IAAI,UAA9D,IAEIq0B,EAAiB9+B,SAASnB,cAAc,OAC5CigC,EAAet4B,UAAY,iBAC3Bs4B,EAAe9/B,MAAMuT,OAAS1N,EAAE,uBAAuB4F,IAAI,UAC3Dq0B,EAAeC,UAAY/C,IAAIgD,WAAWC,MAAM,2CAEhDp6B,EAAE,8BAA8B+G,QAAQkzB,GA6IhBI,GAhK5B,WACqCr6B,EAAE,uBAAuB4F,IAAI,UAA9D,IAEI00B,EAA+Bn/B,SAASnB,cAAc,OAC1DsgC,EAA6B34B,UAAY,+BACzC24B,EAA6BngC,MAAMuT,OAAS1N,EAAE,uBAAuB4F,IAAI,UACzE00B,EAA6B/5B,UAAY,iGAEzCP,EAAE,8BAA8B+G,QAAQuzB,GAyJhBC,GA3I5B,WACqCv6B,EAAE,uBAAuB4F,IAAI,UAA9D,IAEI40B,EAAsBr/B,SAASnB,cAAc,OACjDwgC,EAAoB74B,UAAY,sBAChC64B,EAAoBrgC,MAAMuT,OAAS1N,EAAE,uBAAuB4F,IAAI,UAChE40B,EAAoBN,UAAY/C,IAAIgD,WAAWC,MAAM,2CAErDp6B,EAAE,8BAA8B+G,QAAQyzB,GAoIhBC,GA/W5B,WACI,IAAIC,EAAuBv/B,SAAStB,eAAe,wBAEnD,GAA0B,OAAvB6gC,EAA4B,EAC3BA,EAAuBv/B,SAASnB,cAAc,QACzByC,GAAK,uBAC1Bi+B,EAAqB/4B,UAAY,uBAKjC,IAHA,IAAIg5B,EAAuB,GACvBC,EAA0B,GAC1BC,EAAe,EACX1kC,EAAE,EAAEA,EAAEwgC,GAAyBl+B,OAAOtC,IAAI,CAC9C,IAAI2kC,EAA2BnE,GAAyBxgC,GACpD4kC,EAA2BD,EAAyBpkC,OACpDskC,EAA2BF,EAAyBG,OAEpDC,GAD4BJ,EAAyBK,QAC3BL,EAAyBhhB,OAEvD+gB,IACAD,EAAwBC,GAAgB,CAAC/gB,IAAIohB,GAC7CP,GAAsB,kCAAkCE,EAAa,aAAaA,EAAa,2CAA2CG,EAAyB,iCAAiCD,EAAyB,kBAGjO,IAAIK,EAAcjgC,SAASnB,cAAc,OACzCohC,EAAYz5B,UAAY,cACxBy5B,EAAY76B,UAAY,6CAA6CP,EAAE,qBAAqBwN,QAAQ,ylBAAylBmtB,EAAqB,qHACltBD,EAAqB5zB,YAAYs0B,GAEjCp7B,EAAE,8BAA8B+G,QAAQ2zB,GAExC,IAAMj4B,EAAYm0B,GAAa,WAAW,QACtCyE,EAAgB,EAChBC,EAAe,GACfC,EAAe3E,GAAa,EAAE,EAElC0E,EAAa,GAAK,EAClB,IAAI,IAAInlC,EAAE,EAAEA,EAAE0kC,EAAa1kC,IAAI,CAC3B,IAAIqlC,EAAYx7B,EAAE,uBAAuB7J,GAAGuO,aAErC,IAAJvO,GAAa,IAAJA,IAIL,IAAJA,GACC6J,EAAE,6BAA6BwN,MAAMguB,GAGzCF,EAAanlC,EAAE,GAAKqlC,EAAUH,EAAcE,EAC5CF,GAAiBG,GAGrBx7B,EAAE,UAAUgB,GAAGyB,GAAW,WACtB,IAAM8tB,EAAS7S,SAAS1d,EAAEnE,MAAMkG,KAAK,WACjC+3B,EAAc3+B,SAAStB,eAAe,eAI1C,GAFAmG,EAAE,QAAQ4F,IAAI,aAAa,SAEf,IAAT2qB,EACCvwB,EAAE,uBAAuB4F,IAAI,UAAU,IACvC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,iCAAiC4F,IAAI,UAAU,QACjD5F,EAAE,wBAAwB4F,IAAI,UAAU,QACxC5F,EAAE,QAAQ4F,IAAI,aAAa,QAC3Bk0B,EAAY7gC,IAAM,QAChB,GAAY,IAATs3B,EAAW,CAChBvwB,EAAE,uBAAuB4F,IAAI,UAAU,QACvC5F,EAAE,mBAAmB4F,IAAI,UAAU,gBACnC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,iCAAiC4F,IAAI,UAAU,QACjD5F,EAAE,wBAAwB4F,IAAI,UAAU,QAExC,IAAM61B,EAAe7+B,OAAO+qB,YAAY3nB,EAAE,mBAAmB+E,cAAc/E,EAAE,yBAAyB+E,cAAc/E,EAAE,mBAAmB+E,cAGzI,GAFA/E,EAAE,gBAAgB4F,IAAI,SAAS61B,EAAa,MAEzCjF,GAAeC,IAAmB,CACjC,IAAIsD,EAAgBvD,GAAeC,IAAmBqB,UAAU,SAE7DgC,EAAY7gC,MAAM8gC,IACjBD,EAAY7gC,IAAM8gC,SAGxB,GAAY,IAATxJ,EACLvwB,EAAE,uBAAuB4F,IAAI,UAAU,QACvC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,iCAAiC4F,IAAI,UAAU,QACjD5F,EAAE,wBAAwB4F,IAAI,UAAU,QACxCk0B,EAAY7gC,IAAM,QAChB,GAAY,IAATs3B,EACLvwB,EAAE,uBAAuB4F,IAAI,UAAU,QACvC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,iCAAiC4F,IAAI,UAAU,QACjD5F,EAAE,wBAAwB4F,IAAI,UAAU,QACxCk0B,EAAY7gC,IAAM,OACjB,CACD,IAAMyiC,EAAmBd,EAAwBrK,GAEjD,GAAGmL,EAAiB,CAChB,IAAID,EAAe7+B,OAAO+qB,YAAY3nB,EAAE,mBAAmB+E,cAAc/E,EAAE,yBAAyB+E,cAAc/E,EAAE,mBAAmB+E,cACnI42B,EAAgB,EAChBC,EAAY,MACZC,EAAkB77B,EAAE,uBAAuB4F,IAAI,UAExC,GAAR2qB,GACCkL,EAAe,IACfI,EAAkB,KACL,GAARtL,GACLkL,EAAeI,EAAkB,IACjCD,EAAY,OACC,GAARrL,GAEQ,GAARA,KADLoL,EAAgB,IAKpB37B,EAAE,iCAAiC4F,IAAI,iBAAiB+1B,EAAc,MACtE37B,EAAE,uBAAuB4F,IAAI,iBAAiB+1B,EAAc,MAC5D37B,EAAE,uBAAuB+B,KAAK,YAAY65B,GAE1C57B,EAAE,iCAAiC4F,IAAI,SAASi2B,EAAgB,MAChE77B,EAAE,uBAAuB4F,IAAI,SAAS61B,EAAa,MAEnD,IAAIK,EAAqB3gC,SAAStB,eAAe,sBACjDmG,EAAE,uBAAuB4F,IAAI,UAAU,QACvC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,mBAAmB4F,IAAI,UAAU,QACnC5F,EAAE,iCAAiC4F,IAAI,UAAU,gBACjD5F,EAAE,wBAAwB4F,IAAI,UAAU,QAExCk2B,EAAmB7iC,IAAMyiC,EAAiB5hB,KAIlD9Z,EAAE,6BAA6BwN,MAAMxN,EAAEnE,MAAM6I,mBAEnB/F,IAAvB28B,EAAa/K,IACZvwB,EAAE,6BAA6B4F,IAAI,OAAO01B,EAAa/K,QAsO3CwL,GArXxB/7B,EAAE,0BAA0BkB,SAAS,sBACrClB,EAAE,uBAAuByH,SAASf,OAAO1G,EAAE,2BAC3CA,EAAE,0BAA0B4F,IAAI,QAAQ,SAuXhBuxB,IAAI6E,QAAQC,MAnIX,QAFrBnD,EAAsB39B,SAAStB,eAAe,8BAG9Ci/B,EAAsB39B,SAASnB,cAAc,QACzByC,GAAK,yBACzBq8B,EAAoB3+B,MAAM+hC,QAAU,eACpCpD,EAAoB3+B,MAAM4kB,UAAY,MACtC+Z,EAAoBv4B,UAAY,6GAEhCP,EAAE,mBAAmB4H,KAAK,oBAAoBb,QAAQ+xB,OAgI5C,QA7jBA,IAkBFqD,CAAsB7E", "file": "forum.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 3);\n", "module.exports = flarum.core.compat['app'];", "module.exports = flarum.core.compat['extend'];", "module.exports = flarum.core.compat['forum/components/HeaderPrimary'];", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nexport default function _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}", "/**\n * SSR Window 4.0.2\n * Better handling for window object in SSR environment\n * https://github.com/nolimits4web/ssr-window\n *\n * Copyright 2021, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: December 13, 2021\n */\n/* eslint-disable no-param-reassign */\nfunction isObject(obj) {\n    return (obj !== null &&\n        typeof obj === 'object' &&\n        'constructor' in obj &&\n        obj.constructor === Object);\n}\nfunction extend(target = {}, src = {}) {\n    Object.keys(src).forEach((key) => {\n        if (typeof target[key] === 'undefined')\n            target[key] = src[key];\n        else if (isObject(src[key]) &&\n            isObject(target[key]) &&\n            Object.keys(src[key]).length > 0) {\n            extend(target[key], src[key]);\n        }\n    });\n}\n\nconst ssrDocument = {\n    body: {},\n    addEventListener() { },\n    removeEventListener() { },\n    activeElement: {\n        blur() { },\n        nodeName: '',\n    },\n    querySelector() {\n        return null;\n    },\n    querySelectorAll() {\n        return [];\n    },\n    getElementById() {\n        return null;\n    },\n    createEvent() {\n        return {\n            initEvent() { },\n        };\n    },\n    createElement() {\n        return {\n            children: [],\n            childNodes: [],\n            style: {},\n            setAttribute() { },\n            getElementsByTagName() {\n                return [];\n            },\n        };\n    },\n    createElementNS() {\n        return {};\n    },\n    importNode() {\n        return null;\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n};\nfunction getDocument() {\n    const doc = typeof document !== 'undefined' ? document : {};\n    extend(doc, ssrDocument);\n    return doc;\n}\n\nconst ssrWindow = {\n    document: ssrDocument,\n    navigator: {\n        userAgent: '',\n    },\n    location: {\n        hash: '',\n        host: '',\n        hostname: '',\n        href: '',\n        origin: '',\n        pathname: '',\n        protocol: '',\n        search: '',\n    },\n    history: {\n        replaceState() { },\n        pushState() { },\n        go() { },\n        back() { },\n    },\n    CustomEvent: function CustomEvent() {\n        return this;\n    },\n    addEventListener() { },\n    removeEventListener() { },\n    getComputedStyle() {\n        return {\n            getPropertyValue() {\n                return '';\n            },\n        };\n    },\n    Image() { },\n    Date() { },\n    screen: {},\n    setTimeout() { },\n    clearTimeout() { },\n    matchMedia() {\n        return {};\n    },\n    requestAnimationFrame(callback) {\n        if (typeof setTimeout === 'undefined') {\n            callback();\n            return null;\n        }\n        return setTimeout(callback, 0);\n    },\n    cancelAnimationFrame(id) {\n        if (typeof setTimeout === 'undefined') {\n            return;\n        }\n        clearTimeout(id);\n    },\n};\nfunction getWindow() {\n    const win = typeof window !== 'undefined' ? window : {};\n    extend(win, ssrWindow);\n    return win;\n}\n\nexport { extend, getDocument, getWindow, ssrDocument, ssrWindow };\n", "export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "export default function _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}", "export default function _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nexport default function _construct(Parent, args, Class) {\n  if (isNativeReflectConstruct()) {\n    _construct = Reflect.construct;\n  } else {\n    _construct = function _construct(Parent, args, Class) {\n      var a = [null];\n      a.push.apply(a, args);\n      var Constructor = Function.bind.apply(Parent, a);\n      var instance = new Constructor();\n      if (Class) setPrototypeOf(instance, Class.prototype);\n      return instance;\n    };\n  }\n\n  return _construct.apply(null, arguments);\n}", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nimport isNativeFunction from \"./isNativeFunction.js\";\nimport construct from \"./construct.js\";\nexport default function _wrapNativeSuper(Class) {\n  var _cache = typeof Map === \"function\" ? new Map() : undefined;\n\n  _wrapNativeSuper = function _wrapNativeSuper(Class) {\n    if (Class === null || !isNativeFunction(Class)) return Class;\n\n    if (typeof Class !== \"function\") {\n      throw new TypeError(\"Super expression must either be null or a function\");\n    }\n\n    if (typeof _cache !== \"undefined\") {\n      if (_cache.has(Class)) return _cache.get(Class);\n\n      _cache.set(Class, Wrapper);\n    }\n\n    function Wrapper() {\n      return construct(Class, arguments, getPrototypeOf(this).constructor);\n    }\n\n    Wrapper.prototype = Object.create(Class.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n    return setPrototypeOf(Wrapper, Class);\n  };\n\n  return _wrapNativeSuper(Class);\n}", "export default function _isNativeFunction(fn) {\n  return Function.toString.call(fn).indexOf(\"[native code]\") !== -1;\n}", "/**\n * Dom7 4.0.4\n * Minimalistic JavaScript library for DOM manipulation, with a jQuery-compatible API\n * https://framework7.io/docs/dom7.html\n *\n * Copyright 2022, <PERSON>\n *\n * Licensed under MIT\n *\n * Released on: January 11, 2022\n */\nimport { getWindow, getDocument } from 'ssr-window';\n\n/* eslint-disable no-proto */\nfunction makeReactive(obj) {\n  const proto = obj.__proto__;\n  Object.defineProperty(obj, '__proto__', {\n    get() {\n      return proto;\n    },\n\n    set(value) {\n      proto.__proto__ = value;\n    }\n\n  });\n}\n\nclass Dom7 extends Array {\n  constructor(items) {\n    if (typeof items === 'number') {\n      super(items);\n    } else {\n      super(...(items || []));\n      makeReactive(this);\n    }\n  }\n\n}\n\nfunction arrayFlat(arr = []) {\n  const res = [];\n  arr.forEach(el => {\n    if (Array.isArray(el)) {\n      res.push(...arrayFlat(el));\n    } else {\n      res.push(el);\n    }\n  });\n  return res;\n}\nfunction arrayFilter(arr, callback) {\n  return Array.prototype.filter.call(arr, callback);\n}\nfunction arrayUnique(arr) {\n  const uniqueArray = [];\n\n  for (let i = 0; i < arr.length; i += 1) {\n    if (uniqueArray.indexOf(arr[i]) === -1) uniqueArray.push(arr[i]);\n  }\n\n  return uniqueArray;\n}\nfunction toCamelCase(string) {\n  return string.toLowerCase().replace(/-(.)/g, (match, group) => group.toUpperCase());\n}\n\n// eslint-disable-next-line\n\nfunction qsa(selector, context) {\n  if (typeof selector !== 'string') {\n    return [selector];\n  }\n\n  const a = [];\n  const res = context.querySelectorAll(selector);\n\n  for (let i = 0; i < res.length; i += 1) {\n    a.push(res[i]);\n  }\n\n  return a;\n}\n\nfunction $(selector, context) {\n  const window = getWindow();\n  const document = getDocument();\n  let arr = [];\n\n  if (!context && selector instanceof Dom7) {\n    return selector;\n  }\n\n  if (!selector) {\n    return new Dom7(arr);\n  }\n\n  if (typeof selector === 'string') {\n    const html = selector.trim();\n\n    if (html.indexOf('<') >= 0 && html.indexOf('>') >= 0) {\n      let toCreate = 'div';\n      if (html.indexOf('<li') === 0) toCreate = 'ul';\n      if (html.indexOf('<tr') === 0) toCreate = 'tbody';\n      if (html.indexOf('<td') === 0 || html.indexOf('<th') === 0) toCreate = 'tr';\n      if (html.indexOf('<tbody') === 0) toCreate = 'table';\n      if (html.indexOf('<option') === 0) toCreate = 'select';\n      const tempParent = document.createElement(toCreate);\n      tempParent.innerHTML = html;\n\n      for (let i = 0; i < tempParent.childNodes.length; i += 1) {\n        arr.push(tempParent.childNodes[i]);\n      }\n    } else {\n      arr = qsa(selector.trim(), context || document);\n    } // arr = qsa(selector, document);\n\n  } else if (selector.nodeType || selector === window || selector === document) {\n    arr.push(selector);\n  } else if (Array.isArray(selector)) {\n    if (selector instanceof Dom7) return selector;\n    arr = selector;\n  }\n\n  return new Dom7(arrayUnique(arr));\n}\n\n$.fn = Dom7.prototype;\n\n// eslint-disable-next-line\n\nfunction addClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  this.forEach(el => {\n    el.classList.add(...classNames);\n  });\n  return this;\n}\n\nfunction removeClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  this.forEach(el => {\n    el.classList.remove(...classNames);\n  });\n  return this;\n}\n\nfunction toggleClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  this.forEach(el => {\n    classNames.forEach(className => {\n      el.classList.toggle(className);\n    });\n  });\n}\n\nfunction hasClass(...classes) {\n  const classNames = arrayFlat(classes.map(c => c.split(' ')));\n  return arrayFilter(this, el => {\n    return classNames.filter(className => el.classList.contains(className)).length > 0;\n  }).length > 0;\n}\n\nfunction attr(attrs, value) {\n  if (arguments.length === 1 && typeof attrs === 'string') {\n    // Get attr\n    if (this[0]) return this[0].getAttribute(attrs);\n    return undefined;\n  } // Set attrs\n\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (arguments.length === 2) {\n      // String\n      this[i].setAttribute(attrs, value);\n    } else {\n      // Object\n      for (const attrName in attrs) {\n        this[i][attrName] = attrs[attrName];\n        this[i].setAttribute(attrName, attrs[attrName]);\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction removeAttr(attr) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].removeAttribute(attr);\n  }\n\n  return this;\n}\n\nfunction prop(props, value) {\n  if (arguments.length === 1 && typeof props === 'string') {\n    // Get prop\n    if (this[0]) return this[0][props];\n  } else {\n    // Set props\n    for (let i = 0; i < this.length; i += 1) {\n      if (arguments.length === 2) {\n        // String\n        this[i][props] = value;\n      } else {\n        // Object\n        for (const propName in props) {\n          this[i][propName] = props[propName];\n        }\n      }\n    }\n\n    return this;\n  }\n\n  return this;\n}\n\nfunction data(key, value) {\n  let el;\n\n  if (typeof value === 'undefined') {\n    el = this[0];\n    if (!el) return undefined; // Get value\n\n    if (el.dom7ElementDataStorage && key in el.dom7ElementDataStorage) {\n      return el.dom7ElementDataStorage[key];\n    }\n\n    const dataKey = el.getAttribute(`data-${key}`);\n\n    if (dataKey) {\n      return dataKey;\n    }\n\n    return undefined;\n  } // Set value\n\n\n  for (let i = 0; i < this.length; i += 1) {\n    el = this[i];\n    if (!el.dom7ElementDataStorage) el.dom7ElementDataStorage = {};\n    el.dom7ElementDataStorage[key] = value;\n  }\n\n  return this;\n}\n\nfunction removeData(key) {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (el.dom7ElementDataStorage && el.dom7ElementDataStorage[key]) {\n      el.dom7ElementDataStorage[key] = null;\n      delete el.dom7ElementDataStorage[key];\n    }\n  }\n}\n\nfunction dataset() {\n  const el = this[0];\n  if (!el) return undefined;\n  const dataset = {}; // eslint-disable-line\n\n  if (el.dataset) {\n    for (const dataKey in el.dataset) {\n      dataset[dataKey] = el.dataset[dataKey];\n    }\n  } else {\n    for (let i = 0; i < el.attributes.length; i += 1) {\n      const attr = el.attributes[i];\n\n      if (attr.name.indexOf('data-') >= 0) {\n        dataset[toCamelCase(attr.name.split('data-')[1])] = attr.value;\n      }\n    }\n  }\n\n  for (const key in dataset) {\n    if (dataset[key] === 'false') dataset[key] = false;else if (dataset[key] === 'true') dataset[key] = true;else if (parseFloat(dataset[key]) === dataset[key] * 1) dataset[key] *= 1;\n  }\n\n  return dataset;\n}\n\nfunction val(value) {\n  if (typeof value === 'undefined') {\n    // get value\n    const el = this[0];\n    if (!el) return undefined;\n\n    if (el.multiple && el.nodeName.toLowerCase() === 'select') {\n      const values = [];\n\n      for (let i = 0; i < el.selectedOptions.length; i += 1) {\n        values.push(el.selectedOptions[i].value);\n      }\n\n      return values;\n    }\n\n    return el.value;\n  } // set value\n\n\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (Array.isArray(value) && el.multiple && el.nodeName.toLowerCase() === 'select') {\n      for (let j = 0; j < el.options.length; j += 1) {\n        el.options[j].selected = value.indexOf(el.options[j].value) >= 0;\n      }\n    } else {\n      el.value = value;\n    }\n  }\n\n  return this;\n}\n\nfunction value(value) {\n  return this.val(value);\n}\n\nfunction transform(transform) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.transform = transform;\n  }\n\n  return this;\n}\n\nfunction transition(duration) {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.transitionDuration = typeof duration !== 'string' ? `${duration}ms` : duration;\n  }\n\n  return this;\n}\n\nfunction on(...args) {\n  let [eventType, targetSelector, listener, capture] = args;\n\n  if (typeof args[1] === 'function') {\n    [eventType, listener, capture] = args;\n    targetSelector = undefined;\n  }\n\n  if (!capture) capture = false;\n\n  function handleLiveEvent(e) {\n    const target = e.target;\n    if (!target) return;\n    const eventData = e.target.dom7EventData || [];\n\n    if (eventData.indexOf(e) < 0) {\n      eventData.unshift(e);\n    }\n\n    if ($(target).is(targetSelector)) listener.apply(target, eventData);else {\n      const parents = $(target).parents(); // eslint-disable-line\n\n      for (let k = 0; k < parents.length; k += 1) {\n        if ($(parents[k]).is(targetSelector)) listener.apply(parents[k], eventData);\n      }\n    }\n  }\n\n  function handleEvent(e) {\n    const eventData = e && e.target ? e.target.dom7EventData || [] : [];\n\n    if (eventData.indexOf(e) < 0) {\n      eventData.unshift(e);\n    }\n\n    listener.apply(this, eventData);\n  }\n\n  const events = eventType.split(' ');\n  let j;\n\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (!targetSelector) {\n      for (j = 0; j < events.length; j += 1) {\n        const event = events[j];\n        if (!el.dom7Listeners) el.dom7Listeners = {};\n        if (!el.dom7Listeners[event]) el.dom7Listeners[event] = [];\n        el.dom7Listeners[event].push({\n          listener,\n          proxyListener: handleEvent\n        });\n        el.addEventListener(event, handleEvent, capture);\n      }\n    } else {\n      // Live events\n      for (j = 0; j < events.length; j += 1) {\n        const event = events[j];\n        if (!el.dom7LiveListeners) el.dom7LiveListeners = {};\n        if (!el.dom7LiveListeners[event]) el.dom7LiveListeners[event] = [];\n        el.dom7LiveListeners[event].push({\n          listener,\n          proxyListener: handleLiveEvent\n        });\n        el.addEventListener(event, handleLiveEvent, capture);\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction off(...args) {\n  let [eventType, targetSelector, listener, capture] = args;\n\n  if (typeof args[1] === 'function') {\n    [eventType, listener, capture] = args;\n    targetSelector = undefined;\n  }\n\n  if (!capture) capture = false;\n  const events = eventType.split(' ');\n\n  for (let i = 0; i < events.length; i += 1) {\n    const event = events[i];\n\n    for (let j = 0; j < this.length; j += 1) {\n      const el = this[j];\n      let handlers;\n\n      if (!targetSelector && el.dom7Listeners) {\n        handlers = el.dom7Listeners[event];\n      } else if (targetSelector && el.dom7LiveListeners) {\n        handlers = el.dom7LiveListeners[event];\n      }\n\n      if (handlers && handlers.length) {\n        for (let k = handlers.length - 1; k >= 0; k -= 1) {\n          const handler = handlers[k];\n\n          if (listener && handler.listener === listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          } else if (listener && handler.listener && handler.listener.dom7proxy && handler.listener.dom7proxy === listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          } else if (!listener) {\n            el.removeEventListener(event, handler.proxyListener, capture);\n            handlers.splice(k, 1);\n          }\n        }\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction once(...args) {\n  const dom = this;\n  let [eventName, targetSelector, listener, capture] = args;\n\n  if (typeof args[1] === 'function') {\n    [eventName, listener, capture] = args;\n    targetSelector = undefined;\n  }\n\n  function onceHandler(...eventArgs) {\n    listener.apply(this, eventArgs);\n    dom.off(eventName, targetSelector, onceHandler, capture);\n\n    if (onceHandler.dom7proxy) {\n      delete onceHandler.dom7proxy;\n    }\n  }\n\n  onceHandler.dom7proxy = listener;\n  return dom.on(eventName, targetSelector, onceHandler, capture);\n}\n\nfunction trigger(...args) {\n  const window = getWindow();\n  const events = args[0].split(' ');\n  const eventData = args[1];\n\n  for (let i = 0; i < events.length; i += 1) {\n    const event = events[i];\n\n    for (let j = 0; j < this.length; j += 1) {\n      const el = this[j];\n\n      if (window.CustomEvent) {\n        const evt = new window.CustomEvent(event, {\n          detail: eventData,\n          bubbles: true,\n          cancelable: true\n        });\n        el.dom7EventData = args.filter((data, dataIndex) => dataIndex > 0);\n        el.dispatchEvent(evt);\n        el.dom7EventData = [];\n        delete el.dom7EventData;\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction transitionEnd(callback) {\n  const dom = this;\n\n  function fireCallBack(e) {\n    if (e.target !== this) return;\n    callback.call(this, e);\n    dom.off('transitionend', fireCallBack);\n  }\n\n  if (callback) {\n    dom.on('transitionend', fireCallBack);\n  }\n\n  return this;\n}\n\nfunction animationEnd(callback) {\n  const dom = this;\n\n  function fireCallBack(e) {\n    if (e.target !== this) return;\n    callback.call(this, e);\n    dom.off('animationend', fireCallBack);\n  }\n\n  if (callback) {\n    dom.on('animationend', fireCallBack);\n  }\n\n  return this;\n}\n\nfunction width() {\n  const window = getWindow();\n\n  if (this[0] === window) {\n    return window.innerWidth;\n  }\n\n  if (this.length > 0) {\n    return parseFloat(this.css('width'));\n  }\n\n  return null;\n}\n\nfunction outerWidth(includeMargins) {\n  if (this.length > 0) {\n    if (includeMargins) {\n      const styles = this.styles();\n      return this[0].offsetWidth + parseFloat(styles.getPropertyValue('margin-right')) + parseFloat(styles.getPropertyValue('margin-left'));\n    }\n\n    return this[0].offsetWidth;\n  }\n\n  return null;\n}\n\nfunction height() {\n  const window = getWindow();\n\n  if (this[0] === window) {\n    return window.innerHeight;\n  }\n\n  if (this.length > 0) {\n    return parseFloat(this.css('height'));\n  }\n\n  return null;\n}\n\nfunction outerHeight(includeMargins) {\n  if (this.length > 0) {\n    if (includeMargins) {\n      const styles = this.styles();\n      return this[0].offsetHeight + parseFloat(styles.getPropertyValue('margin-top')) + parseFloat(styles.getPropertyValue('margin-bottom'));\n    }\n\n    return this[0].offsetHeight;\n  }\n\n  return null;\n}\n\nfunction offset() {\n  if (this.length > 0) {\n    const window = getWindow();\n    const document = getDocument();\n    const el = this[0];\n    const box = el.getBoundingClientRect();\n    const body = document.body;\n    const clientTop = el.clientTop || body.clientTop || 0;\n    const clientLeft = el.clientLeft || body.clientLeft || 0;\n    const scrollTop = el === window ? window.scrollY : el.scrollTop;\n    const scrollLeft = el === window ? window.scrollX : el.scrollLeft;\n    return {\n      top: box.top + scrollTop - clientTop,\n      left: box.left + scrollLeft - clientLeft\n    };\n  }\n\n  return null;\n}\n\nfunction hide() {\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].style.display = 'none';\n  }\n\n  return this;\n}\n\nfunction show() {\n  const window = getWindow();\n\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (el.style.display === 'none') {\n      el.style.display = '';\n    }\n\n    if (window.getComputedStyle(el, null).getPropertyValue('display') === 'none') {\n      // Still not visible\n      el.style.display = 'block';\n    }\n  }\n\n  return this;\n}\n\nfunction styles() {\n  const window = getWindow();\n  if (this[0]) return window.getComputedStyle(this[0], null);\n  return {};\n}\n\nfunction css(props, value) {\n  const window = getWindow();\n  let i;\n\n  if (arguments.length === 1) {\n    if (typeof props === 'string') {\n      // .css('width')\n      if (this[0]) return window.getComputedStyle(this[0], null).getPropertyValue(props);\n    } else {\n      // .css({ width: '100px' })\n      for (i = 0; i < this.length; i += 1) {\n        for (const prop in props) {\n          this[i].style[prop] = props[prop];\n        }\n      }\n\n      return this;\n    }\n  }\n\n  if (arguments.length === 2 && typeof props === 'string') {\n    // .css('width', '100px')\n    for (i = 0; i < this.length; i += 1) {\n      this[i].style[props] = value;\n    }\n\n    return this;\n  }\n\n  return this;\n}\n\nfunction each(callback) {\n  if (!callback) return this;\n  this.forEach((el, index) => {\n    callback.apply(el, [el, index]);\n  });\n  return this;\n}\n\nfunction filter(callback) {\n  const result = arrayFilter(this, callback);\n  return $(result);\n}\n\nfunction html(html) {\n  if (typeof html === 'undefined') {\n    return this[0] ? this[0].innerHTML : null;\n  }\n\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].innerHTML = html;\n  }\n\n  return this;\n}\n\nfunction text(text) {\n  if (typeof text === 'undefined') {\n    return this[0] ? this[0].textContent.trim() : null;\n  }\n\n  for (let i = 0; i < this.length; i += 1) {\n    this[i].textContent = text;\n  }\n\n  return this;\n}\n\nfunction is(selector) {\n  const window = getWindow();\n  const document = getDocument();\n  const el = this[0];\n  let compareWith;\n  let i;\n  if (!el || typeof selector === 'undefined') return false;\n\n  if (typeof selector === 'string') {\n    if (el.matches) return el.matches(selector);\n    if (el.webkitMatchesSelector) return el.webkitMatchesSelector(selector);\n    if (el.msMatchesSelector) return el.msMatchesSelector(selector);\n    compareWith = $(selector);\n\n    for (i = 0; i < compareWith.length; i += 1) {\n      if (compareWith[i] === el) return true;\n    }\n\n    return false;\n  }\n\n  if (selector === document) {\n    return el === document;\n  }\n\n  if (selector === window) {\n    return el === window;\n  }\n\n  if (selector.nodeType || selector instanceof Dom7) {\n    compareWith = selector.nodeType ? [selector] : selector;\n\n    for (i = 0; i < compareWith.length; i += 1) {\n      if (compareWith[i] === el) return true;\n    }\n\n    return false;\n  }\n\n  return false;\n}\n\nfunction index() {\n  let child = this[0];\n  let i;\n\n  if (child) {\n    i = 0; // eslint-disable-next-line\n\n    while ((child = child.previousSibling) !== null) {\n      if (child.nodeType === 1) i += 1;\n    }\n\n    return i;\n  }\n\n  return undefined;\n}\n\nfunction eq(index) {\n  if (typeof index === 'undefined') return this;\n  const length = this.length;\n\n  if (index > length - 1) {\n    return $([]);\n  }\n\n  if (index < 0) {\n    const returnIndex = length + index;\n    if (returnIndex < 0) return $([]);\n    return $([this[returnIndex]]);\n  }\n\n  return $([this[index]]);\n}\n\nfunction append(...els) {\n  let newChild;\n  const document = getDocument();\n\n  for (let k = 0; k < els.length; k += 1) {\n    newChild = els[k];\n\n    for (let i = 0; i < this.length; i += 1) {\n      if (typeof newChild === 'string') {\n        const tempDiv = document.createElement('div');\n        tempDiv.innerHTML = newChild;\n\n        while (tempDiv.firstChild) {\n          this[i].appendChild(tempDiv.firstChild);\n        }\n      } else if (newChild instanceof Dom7) {\n        for (let j = 0; j < newChild.length; j += 1) {\n          this[i].appendChild(newChild[j]);\n        }\n      } else {\n        this[i].appendChild(newChild);\n      }\n    }\n  }\n\n  return this;\n}\n\nfunction appendTo(parent) {\n  $(parent).append(this);\n  return this;\n}\n\nfunction prepend(newChild) {\n  const document = getDocument();\n  let i;\n  let j;\n\n  for (i = 0; i < this.length; i += 1) {\n    if (typeof newChild === 'string') {\n      const tempDiv = document.createElement('div');\n      tempDiv.innerHTML = newChild;\n\n      for (j = tempDiv.childNodes.length - 1; j >= 0; j -= 1) {\n        this[i].insertBefore(tempDiv.childNodes[j], this[i].childNodes[0]);\n      }\n    } else if (newChild instanceof Dom7) {\n      for (j = 0; j < newChild.length; j += 1) {\n        this[i].insertBefore(newChild[j], this[i].childNodes[0]);\n      }\n    } else {\n      this[i].insertBefore(newChild, this[i].childNodes[0]);\n    }\n  }\n\n  return this;\n}\n\nfunction prependTo(parent) {\n  $(parent).prepend(this);\n  return this;\n}\n\nfunction insertBefore(selector) {\n  const before = $(selector);\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (before.length === 1) {\n      before[0].parentNode.insertBefore(this[i], before[0]);\n    } else if (before.length > 1) {\n      for (let j = 0; j < before.length; j += 1) {\n        before[j].parentNode.insertBefore(this[i].cloneNode(true), before[j]);\n      }\n    }\n  }\n}\n\nfunction insertAfter(selector) {\n  const after = $(selector);\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (after.length === 1) {\n      after[0].parentNode.insertBefore(this[i], after[0].nextSibling);\n    } else if (after.length > 1) {\n      for (let j = 0; j < after.length; j += 1) {\n        after[j].parentNode.insertBefore(this[i].cloneNode(true), after[j].nextSibling);\n      }\n    }\n  }\n}\n\nfunction next(selector) {\n  if (this.length > 0) {\n    if (selector) {\n      if (this[0].nextElementSibling && $(this[0].nextElementSibling).is(selector)) {\n        return $([this[0].nextElementSibling]);\n      }\n\n      return $([]);\n    }\n\n    if (this[0].nextElementSibling) return $([this[0].nextElementSibling]);\n    return $([]);\n  }\n\n  return $([]);\n}\n\nfunction nextAll(selector) {\n  const nextEls = [];\n  let el = this[0];\n  if (!el) return $([]);\n\n  while (el.nextElementSibling) {\n    const next = el.nextElementSibling; // eslint-disable-line\n\n    if (selector) {\n      if ($(next).is(selector)) nextEls.push(next);\n    } else nextEls.push(next);\n\n    el = next;\n  }\n\n  return $(nextEls);\n}\n\nfunction prev(selector) {\n  if (this.length > 0) {\n    const el = this[0];\n\n    if (selector) {\n      if (el.previousElementSibling && $(el.previousElementSibling).is(selector)) {\n        return $([el.previousElementSibling]);\n      }\n\n      return $([]);\n    }\n\n    if (el.previousElementSibling) return $([el.previousElementSibling]);\n    return $([]);\n  }\n\n  return $([]);\n}\n\nfunction prevAll(selector) {\n  const prevEls = [];\n  let el = this[0];\n  if (!el) return $([]);\n\n  while (el.previousElementSibling) {\n    const prev = el.previousElementSibling; // eslint-disable-line\n\n    if (selector) {\n      if ($(prev).is(selector)) prevEls.push(prev);\n    } else prevEls.push(prev);\n\n    el = prev;\n  }\n\n  return $(prevEls);\n}\n\nfunction siblings(selector) {\n  return this.nextAll(selector).add(this.prevAll(selector));\n}\n\nfunction parent(selector) {\n  const parents = []; // eslint-disable-line\n\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i].parentNode !== null) {\n      if (selector) {\n        if ($(this[i].parentNode).is(selector)) parents.push(this[i].parentNode);\n      } else {\n        parents.push(this[i].parentNode);\n      }\n    }\n  }\n\n  return $(parents);\n}\n\nfunction parents(selector) {\n  const parents = []; // eslint-disable-line\n\n  for (let i = 0; i < this.length; i += 1) {\n    let parent = this[i].parentNode; // eslint-disable-line\n\n    while (parent) {\n      if (selector) {\n        if ($(parent).is(selector)) parents.push(parent);\n      } else {\n        parents.push(parent);\n      }\n\n      parent = parent.parentNode;\n    }\n  }\n\n  return $(parents);\n}\n\nfunction closest(selector) {\n  let closest = this; // eslint-disable-line\n\n  if (typeof selector === 'undefined') {\n    return $([]);\n  }\n\n  if (!closest.is(selector)) {\n    closest = closest.parents(selector).eq(0);\n  }\n\n  return closest;\n}\n\nfunction find(selector) {\n  const foundElements = [];\n\n  for (let i = 0; i < this.length; i += 1) {\n    const found = this[i].querySelectorAll(selector);\n\n    for (let j = 0; j < found.length; j += 1) {\n      foundElements.push(found[j]);\n    }\n  }\n\n  return $(foundElements);\n}\n\nfunction children(selector) {\n  const children = []; // eslint-disable-line\n\n  for (let i = 0; i < this.length; i += 1) {\n    const childNodes = this[i].children;\n\n    for (let j = 0; j < childNodes.length; j += 1) {\n      if (!selector || $(childNodes[j]).is(selector)) {\n        children.push(childNodes[j]);\n      }\n    }\n  }\n\n  return $(children);\n}\n\nfunction remove() {\n  for (let i = 0; i < this.length; i += 1) {\n    if (this[i].parentNode) this[i].parentNode.removeChild(this[i]);\n  }\n\n  return this;\n}\n\nfunction detach() {\n  return this.remove();\n}\n\nfunction add(...els) {\n  const dom = this;\n  let i;\n  let j;\n\n  for (i = 0; i < els.length; i += 1) {\n    const toAdd = $(els[i]);\n\n    for (j = 0; j < toAdd.length; j += 1) {\n      dom.push(toAdd[j]);\n    }\n  }\n\n  return dom;\n}\n\nfunction empty() {\n  for (let i = 0; i < this.length; i += 1) {\n    const el = this[i];\n\n    if (el.nodeType === 1) {\n      for (let j = 0; j < el.childNodes.length; j += 1) {\n        if (el.childNodes[j].parentNode) {\n          el.childNodes[j].parentNode.removeChild(el.childNodes[j]);\n        }\n      }\n\n      el.textContent = '';\n    }\n  }\n\n  return this;\n}\n\n// eslint-disable-next-line\n\nfunction scrollTo(...args) {\n  const window = getWindow();\n  let [left, top, duration, easing, callback] = args;\n\n  if (args.length === 4 && typeof easing === 'function') {\n    callback = easing;\n    [left, top, duration, callback, easing] = args;\n  }\n\n  if (typeof easing === 'undefined') easing = 'swing';\n  return this.each(function animate() {\n    const el = this;\n    let currentTop;\n    let currentLeft;\n    let maxTop;\n    let maxLeft;\n    let newTop;\n    let newLeft;\n    let scrollTop; // eslint-disable-line\n\n    let scrollLeft; // eslint-disable-line\n\n    let animateTop = top > 0 || top === 0;\n    let animateLeft = left > 0 || left === 0;\n\n    if (typeof easing === 'undefined') {\n      easing = 'swing';\n    }\n\n    if (animateTop) {\n      currentTop = el.scrollTop;\n\n      if (!duration) {\n        el.scrollTop = top;\n      }\n    }\n\n    if (animateLeft) {\n      currentLeft = el.scrollLeft;\n\n      if (!duration) {\n        el.scrollLeft = left;\n      }\n    }\n\n    if (!duration) return;\n\n    if (animateTop) {\n      maxTop = el.scrollHeight - el.offsetHeight;\n      newTop = Math.max(Math.min(top, maxTop), 0);\n    }\n\n    if (animateLeft) {\n      maxLeft = el.scrollWidth - el.offsetWidth;\n      newLeft = Math.max(Math.min(left, maxLeft), 0);\n    }\n\n    let startTime = null;\n    if (animateTop && newTop === currentTop) animateTop = false;\n    if (animateLeft && newLeft === currentLeft) animateLeft = false;\n\n    function render(time = new Date().getTime()) {\n      if (startTime === null) {\n        startTime = time;\n      }\n\n      const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n      const easeProgress = easing === 'linear' ? progress : 0.5 - Math.cos(progress * Math.PI) / 2;\n      let done;\n      if (animateTop) scrollTop = currentTop + easeProgress * (newTop - currentTop);\n      if (animateLeft) scrollLeft = currentLeft + easeProgress * (newLeft - currentLeft);\n\n      if (animateTop && newTop > currentTop && scrollTop >= newTop) {\n        el.scrollTop = newTop;\n        done = true;\n      }\n\n      if (animateTop && newTop < currentTop && scrollTop <= newTop) {\n        el.scrollTop = newTop;\n        done = true;\n      }\n\n      if (animateLeft && newLeft > currentLeft && scrollLeft >= newLeft) {\n        el.scrollLeft = newLeft;\n        done = true;\n      }\n\n      if (animateLeft && newLeft < currentLeft && scrollLeft <= newLeft) {\n        el.scrollLeft = newLeft;\n        done = true;\n      }\n\n      if (done) {\n        if (callback) callback();\n        return;\n      }\n\n      if (animateTop) el.scrollTop = scrollTop;\n      if (animateLeft) el.scrollLeft = scrollLeft;\n      window.requestAnimationFrame(render);\n    }\n\n    window.requestAnimationFrame(render);\n  });\n} // scrollTop(top, duration, easing, callback) {\n\n\nfunction scrollTop(...args) {\n  let [top, duration, easing, callback] = args;\n\n  if (args.length === 3 && typeof easing === 'function') {\n    [top, duration, callback, easing] = args;\n  }\n\n  const dom = this;\n\n  if (typeof top === 'undefined') {\n    if (dom.length > 0) return dom[0].scrollTop;\n    return null;\n  }\n\n  return dom.scrollTo(undefined, top, duration, easing, callback);\n}\n\nfunction scrollLeft(...args) {\n  let [left, duration, easing, callback] = args;\n\n  if (args.length === 3 && typeof easing === 'function') {\n    [left, duration, callback, easing] = args;\n  }\n\n  const dom = this;\n\n  if (typeof left === 'undefined') {\n    if (dom.length > 0) return dom[0].scrollLeft;\n    return null;\n  }\n\n  return dom.scrollTo(left, undefined, duration, easing, callback);\n}\n\n// eslint-disable-next-line\n\nfunction animate(initialProps, initialParams) {\n  const window = getWindow();\n  const els = this;\n  const a = {\n    props: Object.assign({}, initialProps),\n    params: Object.assign({\n      duration: 300,\n      easing: 'swing' // or 'linear'\n\n      /* Callbacks\n      begin(elements)\n      complete(elements)\n      progress(elements, complete, remaining, start, tweenValue)\n      */\n\n    }, initialParams),\n    elements: els,\n    animating: false,\n    que: [],\n\n    easingProgress(easing, progress) {\n      if (easing === 'swing') {\n        return 0.5 - Math.cos(progress * Math.PI) / 2;\n      }\n\n      if (typeof easing === 'function') {\n        return easing(progress);\n      }\n\n      return progress;\n    },\n\n    stop() {\n      if (a.frameId) {\n        window.cancelAnimationFrame(a.frameId);\n      }\n\n      a.animating = false;\n      a.elements.each(el => {\n        const element = el;\n        delete element.dom7AnimateInstance;\n      });\n      a.que = [];\n    },\n\n    done(complete) {\n      a.animating = false;\n      a.elements.each(el => {\n        const element = el;\n        delete element.dom7AnimateInstance;\n      });\n      if (complete) complete(els);\n\n      if (a.que.length > 0) {\n        const que = a.que.shift();\n        a.animate(que[0], que[1]);\n      }\n    },\n\n    animate(props, params) {\n      if (a.animating) {\n        a.que.push([props, params]);\n        return a;\n      }\n\n      const elements = []; // Define & Cache Initials & Units\n\n      a.elements.each((el, index) => {\n        let initialFullValue;\n        let initialValue;\n        let unit;\n        let finalValue;\n        let finalFullValue;\n        if (!el.dom7AnimateInstance) a.elements[index].dom7AnimateInstance = a;\n        elements[index] = {\n          container: el\n        };\n        Object.keys(props).forEach(prop => {\n          initialFullValue = window.getComputedStyle(el, null).getPropertyValue(prop).replace(',', '.');\n          initialValue = parseFloat(initialFullValue);\n          unit = initialFullValue.replace(initialValue, '');\n          finalValue = parseFloat(props[prop]);\n          finalFullValue = props[prop] + unit;\n          elements[index][prop] = {\n            initialFullValue,\n            initialValue,\n            unit,\n            finalValue,\n            finalFullValue,\n            currentValue: initialValue\n          };\n        });\n      });\n      let startTime = null;\n      let time;\n      let elementsDone = 0;\n      let propsDone = 0;\n      let done;\n      let began = false;\n      a.animating = true;\n\n      function render() {\n        time = new Date().getTime();\n        let progress;\n        let easeProgress; // let el;\n\n        if (!began) {\n          began = true;\n          if (params.begin) params.begin(els);\n        }\n\n        if (startTime === null) {\n          startTime = time;\n        }\n\n        if (params.progress) {\n          // eslint-disable-next-line\n          params.progress(els, Math.max(Math.min((time - startTime) / params.duration, 1), 0), startTime + params.duration - time < 0 ? 0 : startTime + params.duration - time, startTime);\n        }\n\n        elements.forEach(element => {\n          const el = element;\n          if (done || el.done) return;\n          Object.keys(props).forEach(prop => {\n            if (done || el.done) return;\n            progress = Math.max(Math.min((time - startTime) / params.duration, 1), 0);\n            easeProgress = a.easingProgress(params.easing, progress);\n            const {\n              initialValue,\n              finalValue,\n              unit\n            } = el[prop];\n            el[prop].currentValue = initialValue + easeProgress * (finalValue - initialValue);\n            const currentValue = el[prop].currentValue;\n\n            if (finalValue > initialValue && currentValue >= finalValue || finalValue < initialValue && currentValue <= finalValue) {\n              el.container.style[prop] = finalValue + unit;\n              propsDone += 1;\n\n              if (propsDone === Object.keys(props).length) {\n                el.done = true;\n                elementsDone += 1;\n              }\n\n              if (elementsDone === elements.length) {\n                done = true;\n              }\n            }\n\n            if (done) {\n              a.done(params.complete);\n              return;\n            }\n\n            el.container.style[prop] = currentValue + unit;\n          });\n        });\n        if (done) return; // Then call\n\n        a.frameId = window.requestAnimationFrame(render);\n      }\n\n      a.frameId = window.requestAnimationFrame(render);\n      return a;\n    }\n\n  };\n\n  if (a.elements.length === 0) {\n    return els;\n  }\n\n  let animateInstance;\n\n  for (let i = 0; i < a.elements.length; i += 1) {\n    if (a.elements[i].dom7AnimateInstance) {\n      animateInstance = a.elements[i].dom7AnimateInstance;\n    } else a.elements[i].dom7AnimateInstance = a;\n  }\n\n  if (!animateInstance) {\n    animateInstance = a;\n  }\n\n  if (initialProps === 'stop') {\n    animateInstance.stop();\n  } else {\n    animateInstance.animate(a.props, a.params);\n  }\n\n  return els;\n}\n\nfunction stop() {\n  const els = this;\n\n  for (let i = 0; i < els.length; i += 1) {\n    if (els[i].dom7AnimateInstance) {\n      els[i].dom7AnimateInstance.stop();\n    }\n  }\n}\n\nconst noTrigger = 'resize scroll'.split(' ');\n\nfunction shortcut(name) {\n  function eventHandler(...args) {\n    if (typeof args[0] === 'undefined') {\n      for (let i = 0; i < this.length; i += 1) {\n        if (noTrigger.indexOf(name) < 0) {\n          if (name in this[i]) this[i][name]();else {\n            $(this[i]).trigger(name);\n          }\n        }\n      }\n\n      return this;\n    }\n\n    return this.on(name, ...args);\n  }\n\n  return eventHandler;\n}\n\nconst click = shortcut('click');\nconst blur = shortcut('blur');\nconst focus = shortcut('focus');\nconst focusin = shortcut('focusin');\nconst focusout = shortcut('focusout');\nconst keyup = shortcut('keyup');\nconst keydown = shortcut('keydown');\nconst keypress = shortcut('keypress');\nconst submit = shortcut('submit');\nconst change = shortcut('change');\nconst mousedown = shortcut('mousedown');\nconst mousemove = shortcut('mousemove');\nconst mouseup = shortcut('mouseup');\nconst mouseenter = shortcut('mouseenter');\nconst mouseleave = shortcut('mouseleave');\nconst mouseout = shortcut('mouseout');\nconst mouseover = shortcut('mouseover');\nconst touchstart = shortcut('touchstart');\nconst touchend = shortcut('touchend');\nconst touchmove = shortcut('touchmove');\nconst resize = shortcut('resize');\nconst scroll = shortcut('scroll');\n\nexport default $;\nexport { $, add, addClass, animate, animationEnd, append, appendTo, attr, blur, change, children, click, closest, css, data, dataset, detach, each, empty, eq, filter, find, focus, focusin, focusout, hasClass, height, hide, html, index, insertAfter, insertBefore, is, keydown, keypress, keyup, mousedown, mouseenter, mouseleave, mousemove, mouseout, mouseover, mouseup, next, nextAll, off, offset, on, once, outerHeight, outerWidth, parent, parents, prepend, prependTo, prev, prevAll, prop, remove, removeAttr, removeClass, removeData, resize, scroll, scrollLeft, scrollTo, scrollTop, show, siblings, stop, styles, submit, text, toggleClass, touchend, touchmove, touchstart, transform, transition, transitionEnd, trigger, val, value, width };\n", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "import { $, addClass, removeClass, hasClass, toggleClass, attr, removeAttr, transform, transition, on, off, trigger, transitionEnd, outerWidth, outerHeight, styles, offset, css, each, html, text, is, index, eq, append, prepend, next, nextAll, prev, prevAll, parent, parents, closest, find, children, filter, remove } from 'dom7';\nconst Methods = {\n  addClass,\n  removeClass,\n  hasClass,\n  toggleClass,\n  attr,\n  removeAttr,\n  transform,\n  transition,\n  on,\n  off,\n  trigger,\n  transitionEnd,\n  outerWidth,\n  outerHeight,\n  styles,\n  offset,\n  css,\n  each,\n  html,\n  text,\n  is,\n  index,\n  eq,\n  append,\n  prepend,\n  next,\n  nextAll,\n  prev,\n  prevAll,\n  parent,\n  parents,\n  closest,\n  find,\n  children,\n  filter,\n  remove\n};\nObject.keys(Methods).forEach(methodName => {\n  Object.defineProperty($.fn, methodName, {\n    value: Methods[methodName],\n    writable: true\n  });\n});\nexport default $;", "import { getWindow, getDocument } from 'ssr-window';\nlet support;\n\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch),\n    passiveListener: function checkPassiveListener() {\n      let supportsPassive = false;\n\n      try {\n        const opts = Object.defineProperty({}, 'passive', {\n          // eslint-disable-next-line\n          get() {\n            supportsPassive = true;\n          }\n\n        });\n        window.addEventListener('testPassiveListener', null, opts);\n      } catch (e) {// No support\n      }\n\n      return supportsPassive;\n    }(),\n    gestures: function checkGestures() {\n      return 'ongesturestart' in window;\n    }()\n  };\n}\n\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n\n  return support;\n}\n\nexport { getSupport };", "import { getWindow } from 'ssr-window';\nimport { getSupport } from './get-support.js';\nlet deviceCached;\n\nfunction calcDevice({\n  userAgent\n} = {}) {\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel'; // iPadOs 13 fix\n\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  } // Android\n\n\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  } // Export object\n\n\n  return device;\n}\n\nfunction getDevice(overrides = {}) {\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n\n  return deviceCached;\n}\n\nexport { getDevice };", "import { getWindow } from 'ssr-window';\nlet browser;\n\nfunction calcBrowser() {\n  const window = getWindow();\n\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n\n  return {\n    isSafari: isSafari(),\n    isWebView: /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent)\n  };\n}\n\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n\n  return browser;\n}\n\nexport { getBrowser };", "import { getWindow } from 'ssr-window';\n\nfunction deleteProps(obj) {\n  const object = obj;\n  Object.keys(object).forEach(key => {\n    try {\n      object[key] = null;\n    } catch (e) {// no getter for object\n    }\n\n    try {\n      delete object[key];\n    } catch (e) {// something got wrong\n    }\n  });\n}\n\nfunction nextTick(callback, delay = 0) {\n  return setTimeout(callback, delay);\n}\n\nfunction now() {\n  return Date.now();\n}\n\nfunction getComputedStyle(el) {\n  const window = getWindow();\n  let style;\n\n  if (window.getComputedStyle) {\n    style = window.getComputedStyle(el, null);\n  }\n\n  if (!style && el.currentStyle) {\n    style = el.currentStyle;\n  }\n\n  if (!style) {\n    style = el.style;\n  }\n\n  return style;\n}\n\nfunction getTranslate(el, axis = 'x') {\n  const window = getWindow();\n  let matrix;\n  let curTransform;\n  let transformMatrix;\n  const curStyle = getComputedStyle(el, null);\n\n  if (window.WebKitCSSMatrix) {\n    curTransform = curStyle.transform || curStyle.webkitTransform;\n\n    if (curTransform.split(',').length > 6) {\n      curTransform = curTransform.split(', ').map(a => a.replace(',', '.')).join(', ');\n    } // Some old versions of Webkit choke when 'none' is passed; pass\n    // empty string instead in this case\n\n\n    transformMatrix = new window.WebKitCSSMatrix(curTransform === 'none' ? '' : curTransform);\n  } else {\n    transformMatrix = curStyle.MozTransform || curStyle.OTransform || curStyle.MsTransform || curStyle.msTransform || curStyle.transform || curStyle.getPropertyValue('transform').replace('translate(', 'matrix(1, 0, 0, 1,');\n    matrix = transformMatrix.toString().split(',');\n  }\n\n  if (axis === 'x') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m41; // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[12]); // Normal Browsers\n    else curTransform = parseFloat(matrix[4]);\n  }\n\n  if (axis === 'y') {\n    // Latest Chrome and webkits Fix\n    if (window.WebKitCSSMatrix) curTransform = transformMatrix.m42; // Crazy IE10 Matrix\n    else if (matrix.length === 16) curTransform = parseFloat(matrix[13]); // Normal Browsers\n    else curTransform = parseFloat(matrix[5]);\n  }\n\n  return curTransform || 0;\n}\n\nfunction isObject(o) {\n  return typeof o === 'object' && o !== null && o.constructor && Object.prototype.toString.call(o).slice(8, -1) === 'Object';\n}\n\nfunction isNode(node) {\n  // eslint-disable-next-line\n  if (typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined') {\n    return node instanceof HTMLElement;\n  }\n\n  return node && (node.nodeType === 1 || node.nodeType === 11);\n}\n\nfunction extend(...args) {\n  const to = Object(args[0]);\n  const noExtend = ['__proto__', 'constructor', 'prototype'];\n\n  for (let i = 1; i < args.length; i += 1) {\n    const nextSource = args[i];\n\n    if (nextSource !== undefined && nextSource !== null && !isNode(nextSource)) {\n      const keysArray = Object.keys(Object(nextSource)).filter(key => noExtend.indexOf(key) < 0);\n\n      for (let nextIndex = 0, len = keysArray.length; nextIndex < len; nextIndex += 1) {\n        const nextKey = keysArray[nextIndex];\n        const desc = Object.getOwnPropertyDescriptor(nextSource, nextKey);\n\n        if (desc !== undefined && desc.enumerable) {\n          if (isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else if (!isObject(to[nextKey]) && isObject(nextSource[nextKey])) {\n            to[nextKey] = {};\n\n            if (nextSource[nextKey].__swiper__) {\n              to[nextKey] = nextSource[nextKey];\n            } else {\n              extend(to[nextKey], nextSource[nextKey]);\n            }\n          } else {\n            to[nextKey] = nextSource[nextKey];\n          }\n        }\n      }\n    }\n  }\n\n  return to;\n}\n\nfunction setCSSProperty(el, varName, varValue) {\n  el.style.setProperty(varName, varValue);\n}\n\nfunction animateCSSModeScroll({\n  swiper,\n  targetPosition,\n  side\n}) {\n  const window = getWindow();\n  const startPosition = -swiper.translate;\n  let startTime = null;\n  let time;\n  const duration = swiper.params.speed;\n  swiper.wrapperEl.style.scrollSnapType = 'none';\n  window.cancelAnimationFrame(swiper.cssModeFrameID);\n  const dir = targetPosition > startPosition ? 'next' : 'prev';\n\n  const isOutOfBound = (current, target) => {\n    return dir === 'next' && current >= target || dir === 'prev' && current <= target;\n  };\n\n  const animate = () => {\n    time = new Date().getTime();\n\n    if (startTime === null) {\n      startTime = time;\n    }\n\n    const progress = Math.max(Math.min((time - startTime) / duration, 1), 0);\n    const easeProgress = 0.5 - Math.cos(progress * Math.PI) / 2;\n    let currentPosition = startPosition + easeProgress * (targetPosition - startPosition);\n\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      currentPosition = targetPosition;\n    }\n\n    swiper.wrapperEl.scrollTo({\n      [side]: currentPosition\n    });\n\n    if (isOutOfBound(currentPosition, targetPosition)) {\n      swiper.wrapperEl.style.overflow = 'hidden';\n      swiper.wrapperEl.style.scrollSnapType = '';\n      setTimeout(() => {\n        swiper.wrapperEl.style.overflow = '';\n        swiper.wrapperEl.scrollTo({\n          [side]: currentPosition\n        });\n      });\n      window.cancelAnimationFrame(swiper.cssModeFrameID);\n      return;\n    }\n\n    swiper.cssModeFrameID = window.requestAnimationFrame(animate);\n  };\n\n  animate();\n}\n\nexport { animateCSSModeScroll, deleteProps, nextTick, now, getTranslate, isObject, extend, getComputedStyle, setCSSProperty };", "export default function transitionEmit({\n  swiper,\n  runCallbacks,\n  direction,\n  step\n}) {\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n\n  swiper.emit(`transition${step}`);\n\n  if (runCallbacks && activeIndex !== previousIndex) {\n    if (dir === 'reset') {\n      swiper.emit(`slideResetTransition${step}`);\n      return;\n    }\n\n    swiper.emit(`slideChangeTransition${step}`);\n\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}", "import { getWindow, getDocument } from 'ssr-window';\nimport $ from '../../shared/dom.js';\nimport { now } from '../../shared/utils.js'; // Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\n\nfunction closestElement(selector, base = this) {\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n\n    return found || __closestFrom(el.getRootNode().host);\n  }\n\n  return __closestFrom(base);\n}\n\nexport default function onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  const window = getWindow();\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let $targetEl = $(e.target);\n\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!$targetEl.closest(swiper.wrapperEl).length) return;\n  }\n\n  data.isTouchEvent = e.type === 'touchstart';\n  if (!data.isTouchEvent && 'which' in e && e.which === 3) return;\n  if (!data.isTouchEvent && 'button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return; // change target el for shadow root component\n\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== ''; // eslint-disable-next-line\n\n  const eventPath = event.composedPath ? event.composedPath() : event.path;\n\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    $targetEl = $(eventPath[0]);\n  }\n\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot); // use closestElement for shadow root element to get the actual closest for nested shadow root element\n\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, $targetEl[0]) : $targetEl.closest(noSwipingSelector)[0])) {\n    swiper.allowClick = true;\n    return;\n  }\n\n  if (params.swipeHandler) {\n    if (!$targetEl.closest(params.swipeHandler)[0]) return;\n  }\n\n  touches.currentX = e.type === 'touchstart' ? e.targetTouches[0].pageX : e.pageX;\n  touches.currentY = e.type === 'touchstart' ? e.targetTouches[0].pageY : e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY; // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  const edgeSwipeDetection = params.edgeSwipeDetection || params.iOSEdgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold || params.iOSEdgeSwipeThreshold;\n\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n    } else {\n      return;\n    }\n  }\n\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n\n  if (e.type !== 'touchstart') {\n    let preventDefault = true;\n\n    if ($targetEl.is(data.focusableElements)) {\n      preventDefault = false;\n\n      if ($targetEl[0].nodeName === 'SELECT') {\n        data.isTouched = false;\n      }\n    }\n\n    if (document.activeElement && $(document.activeElement).is(data.focusableElements) && document.activeElement !== $targetEl[0]) {\n      document.activeElement.blur();\n    }\n\n    const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n\n    if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !$targetEl[0].isContentEditable) {\n      e.preventDefault();\n    }\n  }\n\n  if (swiper.params.freeMode && swiper.params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n\n  swiper.emit('touchStart', e);\n}", "import { getDocument } from 'ssr-window';\nimport $ from '../../shared/dom.js';\nimport { now } from '../../shared/utils.js';\nexport default function onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n\n    return;\n  }\n\n  if (data.isTouchEvent && e.type !== 'touchmove') return;\n  const targetTouch = e.type === 'touchmove' && e.targetTouches && (e.targetTouches[0] || e.changedTouches[0]);\n  const pageX = e.type === 'touchmove' ? targetTouch.pageX : e.pageX;\n  const pageY = e.type === 'touchmove' ? targetTouch.pageY : e.pageY;\n\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n\n  if (!swiper.allowTouchMove) {\n    if (!$(e.target).is(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n\n    return;\n  }\n\n  if (data.isTouchEvent && params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate()) {\n      return;\n    }\n  }\n\n  if (data.isTouchEvent && document.activeElement) {\n    if (e.target === document.activeElement && $(e.target).is(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n\n  if (e.targetTouches && e.targetTouches.length > 1) return;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n\n  if (data.isScrolling) {\n    data.isTouched = false;\n    return;\n  }\n\n  if (!data.startMoving) {\n    return;\n  }\n\n  swiper.allowClick = false;\n\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n\n  if (!data.isMoved) {\n    if (params.loop && !params.cssMode) {\n      swiper.loopFix();\n    }\n\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n\n    if (swiper.animating) {\n      swiper.$wrapperEl.trigger('webkitTransitionEnd transitionend');\n    }\n\n    data.allowMomentumBounce = false; // Grab Cursor\n\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n\n    swiper.emit('sliderFirstMove', e);\n  }\n\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) diff = -diff;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n\n  if (diff > 0 && data.currentTranslate > swiper.minTranslate()) {\n    disableParentSwiper = false;\n    if (params.resistance) data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n  } else if (diff < 0 && data.currentTranslate < swiper.maxTranslate()) {\n    disableParentSwiper = false;\n    if (params.resistance) data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n  }\n\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  } // Directions locks\n\n\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  } // Threshold\n\n\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n\n  if (!params.followFinger || params.cssMode) return; // Update active index in free mode\n\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n\n  if (swiper.params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  } // Update progress\n\n\n  swiper.updateProgress(data.currentTranslate); // Update translate\n\n  swiper.setTranslate(data.currentTranslate);\n}", "import { now, nextTick } from '../../shared/utils.js';\nexport default function onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n\n  data.allowTouchCallbacks = false;\n\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  } // Return Grab Cursor\n\n\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  } // Time diff\n\n\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime; // Tap, doubleTap, Click\n\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target);\n    swiper.emit('tap click', e);\n\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 || data.currentTranslate === data.startTranslate) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n\n  if (params.cssMode) {\n    return;\n  }\n\n  if (swiper.params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  } // Find current slide\n\n\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  } // Find current slide size\n\n\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}", "export default function onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return; // Breakpoints\n\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  } // Save locks\n\n\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper; // Disable locks on resize\n\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    swiper.slideTo(swiper.activeIndex, 0, false, true);\n  }\n\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    swiper.autoplay.run();\n  } // Return locks after resize\n\n\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}", "export default function onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}", "export default function onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  } // eslint-disable-next-line\n\n\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, false);\n}", "import { getDocument } from 'ssr-window';\nimport onTouchStart from './onTouchStart.js';\nimport onTouchMove from './onTouchMove.js';\nimport onTouchEnd from './onTouchEnd.js';\nimport onResize from './onResize.js';\nimport onClick from './onClick.js';\nimport onScroll from './onScroll.js';\nlet dummyEventAttached = false;\n\nfunction dummyEventListener() {}\n\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    touchEvents,\n    el,\n    wrapperEl,\n    device,\n    support\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method; // Touch Events\n\n  if (!support.touch) {\n    el[domMethod](touchEvents.start, swiper.onTouchStart, false);\n    document[domMethod](touchEvents.move, swiper.onTouchMove, capture);\n    document[domMethod](touchEvents.end, swiper.onTouchEnd, false);\n  } else {\n    const passiveListener = touchEvents.start === 'touchstart' && support.passiveListener && params.passiveListeners ? {\n      passive: true,\n      capture: false\n    } : false;\n    el[domMethod](touchEvents.start, swiper.onTouchStart, passiveListener);\n    el[domMethod](touchEvents.move, swiper.onTouchMove, support.passiveListener ? {\n      passive: false,\n      capture\n    } : capture);\n    el[domMethod](touchEvents.end, swiper.onTouchEnd, passiveListener);\n\n    if (touchEvents.cancel) {\n      el[domMethod](touchEvents.cancel, swiper.onTouchEnd, passiveListener);\n    }\n  } // Prevent Links Clicks\n\n\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  } // Resize handler\n\n\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n};\n\nfunction attachEvents() {\n  const swiper = this;\n  const document = getDocument();\n  const {\n    params,\n    support\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n\n  swiper.onClick = onClick.bind(swiper);\n\n  if (support.touch && !dummyEventAttached) {\n    document.addEventListener('touchstart', dummyEventListener);\n    dummyEventAttached = true;\n  }\n\n  events(swiper, 'on');\n}\n\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\n\nexport default {\n  attachEvents,\n  detachEvents\n};", "import { extend } from '../../shared/utils.js';\n\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\n\nexport default function setBreakpoint() {\n  const swiper = this;\n  const {\n    activeIndex,\n    initialized,\n    loopedSlides = 0,\n    params,\n    $el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return; // Get breakpoint for window width and update parameters\n\n  const breakpoint = swiper.getBreakpoint(breakpoints, swiper.params.breakpointsBase, swiper.el);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasEnabled = params.enabled;\n\n  if (wasMultiRow && !isMultiRow) {\n    $el.removeClass(`${params.containerModifierClass}grid ${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    $el.addClass(`${params.containerModifierClass}grid`);\n\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      $el.addClass(`${params.containerModifierClass}grid-column`);\n    }\n\n    swiper.emitContainerClasses();\n  } // Toggle navigation, pagination, scrollbar\n\n\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n\n  if (needsReLoop && initialized) {\n    swiper.loopDestroy();\n    swiper.loopCreate();\n    swiper.updateSlides();\n    swiper.slideTo(activeIndex - loopedSlides + swiper.loopedSlides, 0, false);\n  }\n\n  swiper.emit('breakpoint', breakpointParams);\n}", "function checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\n\nexport default {\n  checkOverflow\n};", "export default {\n  init: true,\n  direction: 'horizontal',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 0,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // Images\n  preloadImages: true,\n  updateOnImagesReady: true,\n  // loop\n  loop: false,\n  loopAdditionalSlides: 0,\n  loopedSlides: null,\n  loopedSlidesLimit: true,\n  loopFillGroupWithBlank: false,\n  loopPreventsSlide: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-invisible-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideDuplicateActiveClass: 'swiper-slide-duplicate-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideDuplicateClass: 'swiper-slide-duplicate',\n  slideNextClass: 'swiper-slide-next',\n  slideDuplicateNextClass: 'swiper-slide-duplicate-next',\n  slidePrevClass: 'swiper-slide-prev',\n  slideDuplicatePrevClass: 'swiper-slide-duplicate-prev',\n  wrapperClass: 'swiper-wrapper',\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};", "import { extend } from '../shared/utils.js';\nexport default function moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj = {}) {\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n\n    if (['navigation', 'pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        auto: true\n      };\n    }\n\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}", "/* eslint no-param-reassign: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport $ from '../shared/dom.js';\nimport { extend, now, deleteProps } from '../shared/utils.js';\nimport { getSupport } from '../shared/get-support.js';\nimport { getDevice } from '../shared/get-device.js';\nimport { getBrowser } from '../shared/get-browser.js';\nimport Resize from './modules/resize/resize.js';\nimport Observer from './modules/observer/observer.js';\nimport eventsEmitter from './events-emitter.js';\nimport update from './update/index.js';\nimport translate from './translate/index.js';\nimport transition from './transition/index.js';\nimport slide from './slide/index.js';\nimport loop from './loop/index.js';\nimport grabCursor from './grab-cursor/index.js';\nimport events from './events/index.js';\nimport breakpoints from './breakpoints/index.js';\nimport classes from './classes/index.js';\nimport images from './images/index.js';\nimport checkOverflow from './check-overflow/index.js';\nimport defaults from './defaults.js';\nimport moduleExtendParams from './moduleExtendParams.js';\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events,\n  breakpoints,\n  checkOverflow,\n  classes,\n  images\n};\nconst extendedDefaults = {};\n\nclass Swiper {\n  constructor(...args) {\n    let el;\n    let params;\n\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n\n    if (params.el && $(params.el).length > 1) {\n      const swipers = [];\n      $(params.el).each(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      }); // eslint-disable-next-line no-constructor-return\n\n      return swipers;\n    } // Swiper Instance\n\n\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    }); // Extend defaults with modules params\n\n    const swiperParams = extend({}, defaults, allModulesParams); // Extend defaults with passed params\n\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params); // add event listeners\n\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    } // Save Dom lib\n\n\n    swiper.$ = $; // Extend Swiper\n\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: $(),\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEvents: function touchEvents() {\n        const touch = ['touchstart', 'touchmove', 'touchend', 'touchcancel'];\n        const desktop = ['pointerdown', 'pointermove', 'pointerup'];\n        swiper.touchEventsTouch = {\n          start: touch[0],\n          move: touch[1],\n          end: touch[2],\n          cancel: touch[3]\n        };\n        swiper.touchEventsDesktop = {\n          start: desktop[0],\n          move: desktop[1],\n          end: desktop[2]\n        };\n        return swiper.support.touch || !swiper.params.simulateTouch ? swiper.touchEventsTouch : swiper.touchEventsDesktop;\n      }(),\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: now(),\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        isTouchEvent: undefined,\n        startMoving: undefined\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper'); // Init\n\n    if (swiper.params.init) {\n      swiper.init();\n    } // Return app instance\n    // eslint-disable-next-line no-constructor-return\n\n\n    return swiper;\n  }\n\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n\n    swiper.emit('enable');\n  }\n\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n\n    swiper.emit('disable');\n  }\n\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.each(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n\n  slidesPerViewDynamic(view = 'current', exact = false) {\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex].swiperSlideSize;\n      let breakLoop;\n\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n\n    return spv;\n  }\n\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper; // Breakpoints\n\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n\n    let translated;\n\n    if (swiper.params.freeMode && swiper.params.freeMode.enabled) {\n      setTranslate();\n\n      if (swiper.params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((swiper.params.slidesPerView === 'auto' || swiper.params.slidesPerView > 1) && swiper.isEnd && !swiper.params.centeredSlides) {\n        translated = swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n\n      if (!translated) {\n        setTranslate();\n      }\n    }\n\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n\n    swiper.emit('update');\n  }\n\n  changeDirection(newDirection, needUpdate = true) {\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n\n    swiper.$el.removeClass(`${swiper.params.containerModifierClass}${currentDirection}`).addClass(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.each(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n\n    if (swiper.rtl) {\n      swiper.$el.addClass(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.$el.removeClass(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n\n    swiper.update();\n  }\n\n  mount(el) {\n    const swiper = this;\n    if (swiper.mounted) return true; // Find el\n\n    const $el = $(el || swiper.params.el);\n    el = $el[0];\n\n    if (!el) {\n      return false;\n    }\n\n    el.swiper = swiper;\n\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = $(el.shadowRoot.querySelector(getWrapperSelector())); // Children needs to return slot items\n\n        res.children = options => $el.children(options);\n\n        return res;\n      }\n\n      if (!$el.children) {\n        return $($el).children(getWrapperSelector());\n      }\n\n      return $el.children(getWrapperSelector());\n    }; // Find Wrapper\n\n\n    let $wrapperEl = getWrapper();\n\n    if ($wrapperEl.length === 0 && swiper.params.createElements) {\n      const document = getDocument();\n      const wrapper = document.createElement('div');\n      $wrapperEl = $(wrapper);\n      wrapper.className = swiper.params.wrapperClass;\n      $el.append(wrapper);\n      $el.children(`.${swiper.params.slideClass}`).each(slideEl => {\n        $wrapperEl.append(slideEl);\n      });\n    }\n\n    Object.assign(swiper, {\n      $el,\n      el,\n      $wrapperEl,\n      wrapperEl: $wrapperEl[0],\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || $el.css('direction') === 'rtl'),\n      wrongRTL: $wrapperEl.css('display') === '-webkit-box'\n    });\n    return true;\n  }\n\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit'); // Set breakpoint\n\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    } // Add Classes\n\n\n    swiper.addClasses(); // Create loop\n\n    if (swiper.params.loop) {\n      swiper.loopCreate();\n    } // Update size\n\n\n    swiper.updateSize(); // Update slides\n\n    swiper.updateSlides();\n\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    } // Set Grab Cursor\n\n\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    if (swiper.params.preloadImages) {\n      swiper.preloadImages();\n    } // Slide To Initial Slide\n\n\n    if (swiper.params.loop) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.loopedSlides, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    } // Attach events\n\n\n    swiper.attachEvents(); // Init Flag\n\n    swiper.initialized = true; // Emit\n\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n\n  destroy(deleteInstance = true, cleanStyles = true) {\n    const swiper = this;\n    const {\n      params,\n      $el,\n      $wrapperEl,\n      slides\n    } = swiper;\n\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n\n    swiper.emit('beforeDestroy'); // Init Flag\n\n    swiper.initialized = false; // Detach events\n\n    swiper.detachEvents(); // Destroy loop\n\n    if (params.loop) {\n      swiper.loopDestroy();\n    } // Cleanup styles\n\n\n    if (cleanStyles) {\n      swiper.removeClasses();\n      $el.removeAttr('style');\n      $wrapperEl.removeAttr('style');\n\n      if (slides && slides.length) {\n        slides.removeClass([params.slideVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass].join(' ')).removeAttr('style').removeAttr('data-swiper-slide-index');\n      }\n    }\n\n    swiper.emit('destroy'); // Detach emitter events\n\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n\n    if (deleteInstance !== false) {\n      swiper.$el[0].swiper = null;\n      deleteProps(swiper);\n    }\n\n    swiper.destroyed = true;\n    return null;\n  }\n\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n\n  static get defaults() {\n    return defaults;\n  }\n\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n\n    Swiper.installModule(module);\n    return Swiper;\n  }\n\n}\n\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport default Swiper;", "/* eslint-disable no-underscore-dangle */\nexport default {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n\n    function onceHandler(...args) {\n      self.off(events, onceHandler);\n\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n\n      handler.apply(self, args);\n    }\n\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n\n    return self;\n  },\n\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n\n    return self;\n  },\n\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n\n  emit(...args) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n\n};", "import updateSize from './updateSize.js';\nimport updateSlides from './updateSlides.js';\nimport updateAutoHeight from './updateAutoHeight.js';\nimport updateSlidesOffset from './updateSlidesOffset.js';\nimport updateSlidesProgress from './updateSlidesProgress.js';\nimport updateProgress from './updateProgress.js';\nimport updateSlidesClasses from './updateSlidesClasses.js';\nimport updateActiveIndex from './updateActiveIndex.js';\nimport updateClickedSlide from './updateClickedSlide.js';\nexport default {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};", "export default function updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const $el = swiper.$el;\n\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = $el[0].clientWidth;\n  }\n\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = $el[0].clientHeight;\n  }\n\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  } // Subtract paddings\n\n\n  width = width - parseInt($el.css('padding-left') || 0, 10) - parseInt($el.css('padding-right') || 0, 10);\n  height = height - parseInt($el.css('padding-top') || 0, 10) - parseInt($el.css('padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}", "import { setCSSProperty } from '../../shared/utils.js';\nexport default function updateSlides() {\n  const swiper = this;\n\n  function getDirectionLabel(property) {\n    if (swiper.isHorizontal()) {\n      return property;\n    } // prettier-ignore\n\n\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(getDirectionLabel(label)) || 0);\n  }\n\n  const params = swiper.params;\n  const {\n    $wrapperEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = $wrapperEl.children(`.${swiper.params.slideClass}`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n\n  let offsetAfter = params.slidesOffsetAfter;\n\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  }\n\n  swiper.virtualSize = -spaceBetween; // reset margins\n\n  if (rtl) slides.css({\n    marginLeft: '',\n    marginBottom: '',\n    marginTop: ''\n  });else slides.css({\n    marginRight: '',\n    marginBottom: '',\n    marginTop: ''\n  }); // reset cssMode offsets\n\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(swiper.wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(swiper.wrapperEl, '--swiper-centered-offset-after', '');\n  }\n\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n\n  if (gridEnabled) {\n    swiper.grid.initSlides(slidesLength);\n  } // Calc slides\n\n\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    const slide = slides.eq(i);\n\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slidesLength, getDirectionLabel);\n    }\n\n    if (slide.css('display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[getDirectionLabel('width')] = ``;\n      }\n\n      const slideStyles = getComputedStyle(slide[0]);\n      const currentTransform = slide[0].style.transform;\n      const currentWebKitTransform = slide[0].style.webkitTransform;\n\n      if (currentTransform) {\n        slide[0].style.transform = 'none';\n      }\n\n      if (currentWebKitTransform) {\n        slide[0].style.webkitTransform = 'none';\n      }\n\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? slide.outerWidth(true) : slide.outerHeight(true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide[0];\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n\n      if (currentTransform) {\n        slide[0].style.transform = currentTransform;\n      }\n\n      if (currentWebKitTransform) {\n        slide[0].style.webkitTransform = currentWebKitTransform;\n      }\n\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n\n      if (slides[i]) {\n        slides[i].style[getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n\n    slidesSizesGrid.push(slideSize);\n\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    $wrapperEl.css({\n      width: `${swiper.virtualSize + params.spaceBetween}px`\n    });\n  }\n\n  if (params.setWrapperSize) {\n    $wrapperEl.css({\n      [getDirectionLabel('width')]: `${swiper.virtualSize + params.spaceBetween}px`\n    });\n  }\n\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid, getDirectionLabel);\n  } // Remove last grid elements depending on width\n\n\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n\n    snapGrid = newSlidesGrid;\n\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n\n  if (snapGrid.length === 0) snapGrid = [0];\n\n  if (params.spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode) return true;\n\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n\n      return true;\n    }).css({\n      [key]: `${spaceBetween}px`\n    });\n  }\n\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n    const maxSnap = allSlidesSize - swiperSize;\n    snapGrid = snapGrid.map(snap => {\n      if (snap < 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (params.spaceBetween ? params.spaceBetween : 0);\n    });\n    allSlidesSize -= params.spaceBetween;\n\n    if (allSlidesSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(swiper.wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(swiper.wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.$el.hasClass(backFaceHiddenClass);\n\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.$el.addClass(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.$el.removeClass(backFaceHiddenClass);\n    }\n  }\n}", "import $ from '../../shared/dom.js';\nexport default function updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides.filter(el => parseInt(el.getAttribute('data-swiper-slide-index'), 10) === index)[0];\n    }\n\n    return swiper.slides.eq(index)[0];\n  }; // Find slides currently in view\n\n\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || $([])).each(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  } // Find new height from highest slide in view\n\n\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  } // Update Height\n\n\n  if (newHeight || newHeight === 0) swiper.$wrapperEl.css('height', `${newHeight}px`);\n}", "export default function updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop;\n  }\n}", "import $ from '../../shared/dom.js';\nexport default function updateSlidesProgress(translate = this && this.translate || 0) {\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate; // Visible Slides\n\n  slides.removeClass(params.slideVisibleClass);\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + params.spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + params.spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n      slides.eq(i).addClass(params.slideVisibleClass);\n    }\n\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n\n  swiper.visibleSlides = $(swiper.visibleSlides);\n}", "export default function updateProgress(translate) {\n  const swiper = this;\n\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1; // eslint-disable-next-line\n\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    isBeginning = progress <= 0;\n    isEnd = progress >= 1;\n  }\n\n  Object.assign(swiper, {\n    progress,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n\n  swiper.emit('progress', progress);\n}", "export default function updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    $wrapperEl,\n    activeIndex,\n    realIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  slides.removeClass(`${params.slideActiveClass} ${params.slideNextClass} ${params.slidePrevClass} ${params.slideDuplicateActiveClass} ${params.slideDuplicateNextClass} ${params.slideDuplicatePrevClass}`);\n  let activeSlide;\n\n  if (isVirtual) {\n    activeSlide = swiper.$wrapperEl.find(`.${params.slideClass}[data-swiper-slide-index=\"${activeIndex}\"]`);\n  } else {\n    activeSlide = slides.eq(activeIndex);\n  } // Active classes\n\n\n  activeSlide.addClass(params.slideActiveClass);\n\n  if (params.loop) {\n    // Duplicate to all looped slides\n    if (activeSlide.hasClass(params.slideDuplicateClass)) {\n      $wrapperEl.children(`.${params.slideClass}:not(.${params.slideDuplicateClass})[data-swiper-slide-index=\"${realIndex}\"]`).addClass(params.slideDuplicateActiveClass);\n    } else {\n      $wrapperEl.children(`.${params.slideClass}.${params.slideDuplicateClass}[data-swiper-slide-index=\"${realIndex}\"]`).addClass(params.slideDuplicateActiveClass);\n    }\n  } // Next Slide\n\n\n  let nextSlide = activeSlide.nextAll(`.${params.slideClass}`).eq(0).addClass(params.slideNextClass);\n\n  if (params.loop && nextSlide.length === 0) {\n    nextSlide = slides.eq(0);\n    nextSlide.addClass(params.slideNextClass);\n  } // Prev Slide\n\n\n  let prevSlide = activeSlide.prevAll(`.${params.slideClass}`).eq(0).addClass(params.slidePrevClass);\n\n  if (params.loop && prevSlide.length === 0) {\n    prevSlide = slides.eq(-1);\n    prevSlide.addClass(params.slidePrevClass);\n  }\n\n  if (params.loop) {\n    // Duplicate to all looped slides\n    if (nextSlide.hasClass(params.slideDuplicateClass)) {\n      $wrapperEl.children(`.${params.slideClass}:not(.${params.slideDuplicateClass})[data-swiper-slide-index=\"${nextSlide.attr('data-swiper-slide-index')}\"]`).addClass(params.slideDuplicateNextClass);\n    } else {\n      $wrapperEl.children(`.${params.slideClass}.${params.slideDuplicateClass}[data-swiper-slide-index=\"${nextSlide.attr('data-swiper-slide-index')}\"]`).addClass(params.slideDuplicateNextClass);\n    }\n\n    if (prevSlide.hasClass(params.slideDuplicateClass)) {\n      $wrapperEl.children(`.${params.slideClass}:not(.${params.slideDuplicateClass})[data-swiper-slide-index=\"${prevSlide.attr('data-swiper-slide-index')}\"]`).addClass(params.slideDuplicatePrevClass);\n    } else {\n      $wrapperEl.children(`.${params.slideClass}.${params.slideDuplicateClass}[data-swiper-slide-index=\"${prevSlide.attr('data-swiper-slide-index')}\"]`).addClass(params.slideDuplicatePrevClass);\n    }\n  }\n\n  swiper.emitSlidesClasses();\n}", "export default function updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    slidesGrid,\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n\n  if (typeof activeIndex === 'undefined') {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n          activeIndex = i;\n        } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n          activeIndex = i + 1;\n        }\n      } else if (translate >= slidesGrid[i]) {\n        activeIndex = i;\n      }\n    } // Normalize slideIndex\n\n\n    if (params.normalizeSlideIndex) {\n      if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n    }\n  }\n\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n\n  if (activeIndex === previousIndex) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n\n    return;\n  } // Get real index\n\n\n  const realIndex = parseInt(swiper.slides.eq(activeIndex).attr('data-swiper-slide-index') || activeIndex, 10);\n  Object.assign(swiper, {\n    snapIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n\n  if (previousRealIndex !== realIndex) {\n    swiper.emit('realIndexChange');\n  }\n\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    swiper.emit('slideChange');\n  }\n}", "import $ from '../../shared/dom.js';\nexport default function updateClickedSlide(e) {\n  const swiper = this;\n  const params = swiper.params;\n  const slide = $(e).closest(`.${params.slideClass}`)[0];\n  let slideFound = false;\n  let slideIndex;\n\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt($(slide).attr('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}", "import getTranslate from './getTranslate.js';\nimport setTranslate from './setTranslate.js';\nimport minTranslate from './minTranslate.js';\nimport maxTranslate from './maxTranslate.js';\nimport translateTo from './translateTo.js';\nexport default {\n  getTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};", "import { getTranslate } from '../../shared/utils.js';\nexport default function getSwiperTranslate(axis = this.isHorizontal() ? 'x' : 'y') {\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    $wrapperEl\n  } = swiper;\n\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n\n  if (params.cssMode) {\n    return translate;\n  }\n\n  let currentTranslate = getTranslate($wrapperEl[0], axis);\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}", "export default function setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    $wrapperEl,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    $wrapperEl.transform(`translate3d(${x}px, ${y}px, ${z}px)`);\n  }\n\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y; // Check if we need to update progress\n\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n\n  swiper.emit('setTranslate', swiper.translate, byController);\n}", "export default function minTranslate() {\n  return -this.snapGrid[0];\n}", "export default function maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}", "import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function translateTo(translate = 0, speed = this.params.speed, runCallbacks = true, translateBounds = true, internal) {\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate; // Update progress\n\n  swiper.updateProgress(newTranslate);\n\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n\n    return true;\n  }\n\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n\n    if (!swiper.animating) {\n      swiper.animating = true;\n\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n\n      swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n      swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n\n  return true;\n}", "import setTransition from './setTransition.js';\nimport transitionStart from './transitionStart.js';\nimport transitionEnd from './transitionEnd.js';\nexport default {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};", "export default function setTransition(duration, byController) {\n  const swiper = this;\n\n  if (!swiper.params.cssMode) {\n    swiper.$wrapperEl.transition(duration);\n  }\n\n  swiper.emit('setTransition', duration, byController);\n}", "import transitionEmit from './transitionEmit.js';\nexport default function transitionStart(runCallbacks = true, direction) {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}", "import transitionEmit from './transitionEmit.js';\nexport default function transitionEnd(runCallbacks = true, direction) {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}", "import slideTo from './slideTo.js';\nimport slideToLoop from './slideToLoop.js';\nimport slideNext from './slideNext.js';\nimport slidePrev from './slidePrev.js';\nimport slideReset from './slideReset.js';\nimport slideToClosest from './slideToClosest.js';\nimport slideToClickedSlide from './slideToClickedSlide.js';\nexport default {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};", "import { animateCSSModeScroll } from '../../shared/utils.js';\nexport default function slideTo(index = 0, speed = this.params.speed, runCallbacks = true, internal, initial) {\n  if (typeof index !== 'number' && typeof index !== 'string') {\n    throw new Error(`The 'index' argument cannot have type other than 'number' or 'string'. [${typeof index}] given.`);\n  }\n\n  if (typeof index === 'string') {\n    /**\n     * The `index` argument converted from `string` to `number`.\n     * @type {number}\n     */\n    const indexAsNumber = parseInt(index, 10);\n    /**\n     * Determines whether the `index` argument is a valid `number`\n     * after being converted from the `string` type.\n     * @type {boolean}\n     */\n\n    const isValidNumber = isFinite(indexAsNumber);\n\n    if (!isValidNumber) {\n      throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${index}] given.`);\n    } // Knowing that the converted `index` is a valid number,\n    // we can update the original argument's value.\n\n\n    index = indexAsNumber;\n  }\n\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n\n  if (swiper.animating && params.preventInteractionOnTransition || !enabled && !internal && !initial) {\n    return false;\n  }\n\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex]; // Normalize slideIndex\n\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  } // Directions locks\n\n\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && translate < swiper.translate && translate < swiper.minTranslate()) {\n      return false;\n    }\n\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) return false;\n    }\n  }\n\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  } // Update progress\n\n\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset'; // Update Index\n\n  if (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate) {\n    swiper.updateActiveIndex(slideIndex); // Update Height\n\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n\n    swiper.updateSlidesClasses();\n\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n\n    return false;\n  }\n\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n\n    if (speed === 0) {\n      const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._swiperImmediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n\n    return true;\n  }\n\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.$wrapperEl[0].removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.$wrapperEl[0].removeEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n\n    swiper.$wrapperEl[0].addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n    swiper.$wrapperEl[0].addEventListener('webkitTransitionEnd', swiper.onSlideToWrapperTransitionEnd);\n  }\n\n  return true;\n}", "export default function slideToLoop(index = 0, speed = this.params.speed, runCallbacks = true, internal) {\n  if (typeof index === 'string') {\n    /**\n     * The `index` argument converted from `string` to `number`.\n     * @type {number}\n     */\n    const indexAsNumber = parseInt(index, 10);\n    /**\n     * Determines whether the `index` argument is a valid `number`\n     * after being converted from the `string` type.\n     * @type {boolean}\n     */\n\n    const isValidNumber = isFinite(indexAsNumber);\n\n    if (!isValidNumber) {\n      throw new Error(`The passed-in 'index' (string) couldn't be converted to 'number'. [${index}] given.`);\n    } // Knowing that the converted `index` is a valid number,\n    // we can update the original argument's value.\n\n\n    index = indexAsNumber;\n  }\n\n  const swiper = this;\n  let newIndex = index;\n\n  if (swiper.params.loop) {\n    newIndex += swiper.loopedSlides;\n  }\n\n  return swiper.slideTo(newIndex, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slideNext(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    animating,\n    enabled,\n    params\n  } = swiper;\n  if (!enabled) return swiper;\n  let perGroup = params.slidesPerGroup;\n\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n\n  if (params.loop) {\n    if (animating && params.loopPreventsSlide) return false;\n    swiper.loopFix(); // eslint-disable-next-line\n\n    swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n  }\n\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slidePrev(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  const {\n    params,\n    animating,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return swiper;\n\n  if (params.loop) {\n    if (animating && params.loopPreventsSlide) return false;\n    swiper.loopFix(); // eslint-disable-next-line\n\n    swiper._clientLeft = swiper.$wrapperEl[0].clientLeft;\n  }\n\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n\n  if (typeof prevSnap === 'undefined' && params.cssMode) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n\n  let prevIndex = 0;\n\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  }\n\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slideReset(speed = this.params.speed, runCallbacks = true, internal) {\n  const swiper = this;\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}", "/* eslint no-unused-vars: \"off\" */\nexport default function slideToClosest(speed = this.params.speed, runCallbacks = true, internal, threshold = 0.5) {\n  const swiper = this;\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}", "import $ from '../../shared/dom.js';\nimport { nextTick } from '../../shared/utils.js';\nexport default function slideToClickedSlide() {\n  const swiper = this;\n  const {\n    params,\n    $wrapperEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.clickedIndex;\n  let realIndex;\n\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt($(swiper.clickedSlide).attr('data-swiper-slide-index'), 10);\n\n    if (params.centeredSlides) {\n      if (slideToIndex < swiper.loopedSlides - slidesPerView / 2 || slideToIndex > swiper.slides.length - swiper.loopedSlides + slidesPerView / 2) {\n        swiper.loopFix();\n        slideToIndex = $wrapperEl.children(`.${params.slideClass}[data-swiper-slide-index=\"${realIndex}\"]:not(.${params.slideDuplicateClass})`).eq(0).index();\n        nextTick(() => {\n          swiper.slideTo(slideToIndex);\n        });\n      } else {\n        swiper.slideTo(slideToIndex);\n      }\n    } else if (slideToIndex > swiper.slides.length - slidesPerView) {\n      swiper.loopFix();\n      slideToIndex = $wrapperEl.children(`.${params.slideClass}[data-swiper-slide-index=\"${realIndex}\"]:not(.${params.slideDuplicateClass})`).eq(0).index();\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}", "import loopCreate from './loopCreate.js';\nimport loopFix from './loopFix.js';\nimport loopDestroy from './loopDestroy.js';\nexport default {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};", "import { getDocument } from 'ssr-window';\nimport $ from '../../shared/dom.js';\nexport default function loopCreate() {\n  const swiper = this;\n  const document = getDocument();\n  const {\n    params,\n    $wrapperEl\n  } = swiper; // Remove duplicated slides\n\n  const $selector = $wrapperEl.children().length > 0 ? $($wrapperEl.children()[0].parentNode) : $wrapperEl;\n  $selector.children(`.${params.slideClass}.${params.slideDuplicateClass}`).remove();\n  let slides = $selector.children(`.${params.slideClass}`);\n\n  if (params.loopFillGroupWithBlank) {\n    const blankSlidesNum = params.slidesPerGroup - slides.length % params.slidesPerGroup;\n\n    if (blankSlidesNum !== params.slidesPerGroup) {\n      for (let i = 0; i < blankSlidesNum; i += 1) {\n        const blankNode = $(document.createElement('div')).addClass(`${params.slideClass} ${params.slideBlankClass}`);\n        $selector.append(blankNode);\n      }\n\n      slides = $selector.children(`.${params.slideClass}`);\n    }\n  }\n\n  if (params.slidesPerView === 'auto' && !params.loopedSlides) params.loopedSlides = slides.length;\n  swiper.loopedSlides = Math.ceil(parseFloat(params.loopedSlides || params.slidesPerView, 10));\n  swiper.loopedSlides += params.loopAdditionalSlides;\n\n  if (swiper.loopedSlides > slides.length && swiper.params.loopedSlidesLimit) {\n    swiper.loopedSlides = slides.length;\n  }\n\n  const prependSlides = [];\n  const appendSlides = [];\n  slides.each((el, index) => {\n    const slide = $(el);\n    slide.attr('data-swiper-slide-index', index);\n  });\n\n  for (let i = 0; i < swiper.loopedSlides; i += 1) {\n    const index = i - Math.floor(i / slides.length) * slides.length;\n    appendSlides.push(slides.eq(index)[0]);\n    prependSlides.unshift(slides.eq(slides.length - index - 1)[0]);\n  }\n\n  for (let i = 0; i < appendSlides.length; i += 1) {\n    $selector.append($(appendSlides[i].cloneNode(true)).addClass(params.slideDuplicateClass));\n  }\n\n  for (let i = prependSlides.length - 1; i >= 0; i -= 1) {\n    $selector.prepend($(prependSlides[i].cloneNode(true)).addClass(params.slideDuplicateClass));\n  }\n}", "export default function loopFix() {\n  const swiper = this;\n  swiper.emit('beforeLoopFix');\n  const {\n    activeIndex,\n    slides,\n    loopedSlides,\n    allowSlidePrev,\n    allowSlideNext,\n    snapGrid,\n    rtlTranslate: rtl\n  } = swiper;\n  let newIndex;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  const snapTranslate = -snapGrid[activeIndex];\n  const diff = snapTranslate - swiper.getTranslate(); // Fix For Negative Oversliding\n\n  if (activeIndex < loopedSlides) {\n    newIndex = slides.length - loopedSlides * 3 + activeIndex;\n    newIndex += loopedSlides;\n    const slideChanged = swiper.slideTo(newIndex, 0, false, true);\n\n    if (slideChanged && diff !== 0) {\n      swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n    }\n  } else if (activeIndex >= slides.length - loopedSlides) {\n    // Fix For Positive Oversliding\n    newIndex = -slides.length + activeIndex + loopedSlides;\n    newIndex += loopedSlides;\n    const slideChanged = swiper.slideTo(newIndex, 0, false, true);\n\n    if (slideChanged && diff !== 0) {\n      swiper.setTranslate((rtl ? -swiper.translate : swiper.translate) - diff);\n    }\n  }\n\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  swiper.emit('loopFix');\n}", "export default function loopDestroy() {\n  const swiper = this;\n  const {\n    $wrapperEl,\n    params,\n    slides\n  } = swiper;\n  $wrapperEl.children(`.${params.slideClass}.${params.slideDuplicateClass},.${params.slideClass}.${params.slideBlankClass}`).remove();\n  slides.removeAttr('data-swiper-slide-index');\n}", "import setGrabCursor from './setGrabCursor.js';\nimport unsetGrabCursor from './unsetGrabCursor.js';\nexport default {\n  setGrabCursor,\n  unsetGrabCursor\n};", "export default function setGrabCursor(moving) {\n  const swiper = this;\n  if (swiper.support.touch || !swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n}", "export default function unsetGrabCursor() {\n  const swiper = this;\n\n  if (swiper.support.touch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n}", "import setBreakpoint from './setBreakpoint.js';\nimport getBreakpoint from './getBreakpoint.js';\nexport default {\n  setBreakpoint,\n  getBreakpoint\n};", "import { getWindow } from 'ssr-window';\nexport default function getBreakpoint(breakpoints, base = 'window', containerEl) {\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n\n  return breakpoint || 'max';\n}", "import addClasses from './addClasses.js';\nimport removeClasses from './removeClasses.js';\nexport default {\n  addClasses,\n  removeClasses\n};", "function prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\n\nexport default function addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    $el,\n    device,\n    support\n  } = swiper; // prettier-ignore\n\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'pointer-events': !support.touch\n  }, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  $el.addClass([...classNames].join(' '));\n  swiper.emitContainerClasses();\n}", "export default function removeClasses() {\n  const swiper = this;\n  const {\n    $el,\n    classNames\n  } = swiper;\n  $el.removeClass(classNames.join(' '));\n  swiper.emitContainerClasses();\n}", "import loadImage from './loadImage.js';\nimport preloadImages from './preloadImages.js';\nexport default {\n  loadImage,\n  preloadImages\n};", "import { getWindow } from 'ssr-window';\nimport $ from '../../shared/dom.js';\nexport default function loadImage(imageEl, src, srcset, sizes, checkForComplete, callback) {\n  const window = getWindow();\n  let image;\n\n  function onReady() {\n    if (callback) callback();\n  }\n\n  const isPicture = $(imageEl).parent('picture')[0];\n\n  if (!isPicture && (!imageEl.complete || !checkForComplete)) {\n    if (src) {\n      image = new window.Image();\n      image.onload = onReady;\n      image.onerror = onReady;\n\n      if (sizes) {\n        image.sizes = sizes;\n      }\n\n      if (srcset) {\n        image.srcset = srcset;\n      }\n\n      if (src) {\n        image.src = src;\n      }\n    } else {\n      onReady();\n    }\n  } else {\n    // image already loaded...\n    onReady();\n  }\n}", "export default function preloadImages() {\n  const swiper = this;\n  swiper.imagesToLoad = swiper.$el.find('img');\n\n  function onReady() {\n    if (typeof swiper === 'undefined' || swiper === null || !swiper || swiper.destroyed) return;\n    if (swiper.imagesLoaded !== undefined) swiper.imagesLoaded += 1;\n\n    if (swiper.imagesLoaded === swiper.imagesToLoad.length) {\n      if (swiper.params.updateOnImagesReady) swiper.update();\n      swiper.emit('imagesReady');\n    }\n  }\n\n  for (let i = 0; i < swiper.imagesToLoad.length; i += 1) {\n    const imageEl = swiper.imagesToLoad[i];\n    swiper.loadImage(imageEl, imageEl.currentSrc || imageEl.getAttribute('src'), imageEl.srcset || imageEl.getAttribute('srcset'), imageEl.sizes || imageEl.getAttribute('sizes'), true, onReady);\n  }\n}", "import { getWindow } from 'ssr-window';\nexport default function Resize({\n  swiper,\n  on,\n  emit\n}) {\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(({\n          contentBoxSize,\n          contentRect,\n          target\n        }) => {\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}", "import { getWindow } from 'ssr-window';\nexport default function Observer({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const observers = [];\n  const window = getWindow();\n\n  const attach = (target, options = {}) => {\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: typeof options.childList === 'undefined' ? true : options.childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n\n  const init = () => {\n    if (!swiper.params.observer) return;\n\n    if (swiper.params.observeParents) {\n      const containerParents = swiper.$el.parents();\n\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    } // Observe container\n\n\n    attach(swiper.$el[0], {\n      childList: swiper.params.observeSlideChildren\n    }); // Observe wrapper\n\n    attach(swiper.$wrapperEl[0], {\n      attributes: false\n    });\n  };\n\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}", "import { getDocument } from 'ssr-window';\nexport default function createElementIfNotDefined(swiper, originalParams, params, checkProps) {\n  const document = getDocument();\n\n  if (swiper.params.createElements) {\n    Object.keys(checkProps).forEach(key => {\n      if (!params[key] && params.auto === true) {\n        let element = swiper.$el.children(`.${checkProps[key]}`)[0];\n\n        if (!element) {\n          element = document.createElement('div');\n          element.className = checkProps[key];\n          swiper.$el.append(element);\n        }\n\n        params[key] = element;\n        originalParams[key] = element;\n      }\n    });\n  }\n\n  return params;\n}", "import createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nimport $ from '../../shared/dom.js';\nexport default function Navigation({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  extendParams({\n    navigation: {\n      nextEl: null,\n      prevEl: null,\n      hideOnClick: false,\n      disabledClass: 'swiper-button-disabled',\n      hiddenClass: 'swiper-button-hidden',\n      lockClass: 'swiper-button-lock',\n      navigationDisabledClass: 'swiper-navigation-disabled'\n    }\n  });\n  swiper.navigation = {\n    nextEl: null,\n    $nextEl: null,\n    prevEl: null,\n    $prevEl: null\n  };\n\n  function getEl(el) {\n    let $el;\n\n    if (el) {\n      $el = $(el);\n\n      if (swiper.params.uniqueNavElements && typeof el === 'string' && $el.length > 1 && swiper.$el.find(el).length === 1) {\n        $el = swiper.$el.find(el);\n      }\n    }\n\n    return $el;\n  }\n\n  function toggleEl($el, disabled) {\n    const params = swiper.params.navigation;\n\n    if ($el && $el.length > 0) {\n      $el[disabled ? 'addClass' : 'removeClass'](params.disabledClass);\n      if ($el[0] && $el[0].tagName === 'BUTTON') $el[0].disabled = disabled;\n\n      if (swiper.params.watchOverflow && swiper.enabled) {\n        $el[swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n      }\n    }\n  }\n\n  function update() {\n    // Update Navigation Buttons\n    if (swiper.params.loop) return;\n    const {\n      $nextEl,\n      $prevEl\n    } = swiper.navigation;\n    toggleEl($prevEl, swiper.isBeginning && !swiper.params.rewind);\n    toggleEl($nextEl, swiper.isEnd && !swiper.params.rewind);\n  }\n\n  function onPrevClick(e) {\n    e.preventDefault();\n    if (swiper.isBeginning && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slidePrev();\n    emit('navigationPrev');\n  }\n\n  function onNextClick(e) {\n    e.preventDefault();\n    if (swiper.isEnd && !swiper.params.loop && !swiper.params.rewind) return;\n    swiper.slideNext();\n    emit('navigationNext');\n  }\n\n  function init() {\n    const params = swiper.params.navigation;\n    swiper.params.navigation = createElementIfNotDefined(swiper, swiper.originalParams.navigation, swiper.params.navigation, {\n      nextEl: 'swiper-button-next',\n      prevEl: 'swiper-button-prev'\n    });\n    if (!(params.nextEl || params.prevEl)) return;\n    const $nextEl = getEl(params.nextEl);\n    const $prevEl = getEl(params.prevEl);\n\n    if ($nextEl && $nextEl.length > 0) {\n      $nextEl.on('click', onNextClick);\n    }\n\n    if ($prevEl && $prevEl.length > 0) {\n      $prevEl.on('click', onPrevClick);\n    }\n\n    Object.assign(swiper.navigation, {\n      $nextEl,\n      nextEl: $nextEl && $nextEl[0],\n      $prevEl,\n      prevEl: $prevEl && $prevEl[0]\n    });\n\n    if (!swiper.enabled) {\n      if ($nextEl) $nextEl.addClass(params.lockClass);\n      if ($prevEl) $prevEl.addClass(params.lockClass);\n    }\n  }\n\n  function destroy() {\n    const {\n      $nextEl,\n      $prevEl\n    } = swiper.navigation;\n\n    if ($nextEl && $nextEl.length) {\n      $nextEl.off('click', onNextClick);\n      $nextEl.removeClass(swiper.params.navigation.disabledClass);\n    }\n\n    if ($prevEl && $prevEl.length) {\n      $prevEl.off('click', onPrevClick);\n      $prevEl.removeClass(swiper.params.navigation.disabledClass);\n    }\n  }\n\n  on('init', () => {\n    if (swiper.params.navigation.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      update();\n    }\n  });\n  on('toEdge fromEdge lock unlock', () => {\n    update();\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    const {\n      $nextEl,\n      $prevEl\n    } = swiper.navigation;\n\n    if ($nextEl) {\n      $nextEl[swiper.enabled ? 'removeClass' : 'addClass'](swiper.params.navigation.lockClass);\n    }\n\n    if ($prevEl) {\n      $prevEl[swiper.enabled ? 'removeClass' : 'addClass'](swiper.params.navigation.lockClass);\n    }\n  });\n  on('click', (_s, e) => {\n    const {\n      $nextEl,\n      $prevEl\n    } = swiper.navigation;\n    const targetEl = e.target;\n\n    if (swiper.params.navigation.hideOnClick && !$(targetEl).is($prevEl) && !$(targetEl).is($nextEl)) {\n      if (swiper.pagination && swiper.params.pagination && swiper.params.pagination.clickable && (swiper.pagination.el === targetEl || swiper.pagination.el.contains(targetEl))) return;\n      let isHidden;\n\n      if ($nextEl) {\n        isHidden = $nextEl.hasClass(swiper.params.navigation.hiddenClass);\n      } else if ($prevEl) {\n        isHidden = $prevEl.hasClass(swiper.params.navigation.hiddenClass);\n      }\n\n      if (isHidden === true) {\n        emit('navigationShow');\n      } else {\n        emit('navigationHide');\n      }\n\n      if ($nextEl) {\n        $nextEl.toggleClass(swiper.params.navigation.hiddenClass);\n      }\n\n      if ($prevEl) {\n        $prevEl.toggleClass(swiper.params.navigation.hiddenClass);\n      }\n    }\n  });\n\n  const enable = () => {\n    swiper.$el.removeClass(swiper.params.navigation.navigationDisabledClass);\n    init();\n    update();\n  };\n\n  const disable = () => {\n    swiper.$el.addClass(swiper.params.navigation.navigationDisabledClass);\n    destroy();\n  };\n\n  Object.assign(swiper.navigation, {\n    enable,\n    disable,\n    update,\n    init,\n    destroy\n  });\n}", "export default function classesToSelector(classes = '') {\n  return `.${classes.trim().replace(/([\\.:!\\/])/g, '\\\\$1') // eslint-disable-line\n  .replace(/ /g, '.')}`;\n}", "import $ from '../../shared/dom.js';\nimport classesToSelector from '../../shared/classes-to-selector.js';\nimport createElementIfNotDefined from '../../shared/create-element-if-not-defined.js';\nexport default function Pagination({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  const pfx = 'swiper-pagination';\n  extendParams({\n    pagination: {\n      el: null,\n      bulletElement: 'span',\n      clickable: false,\n      hideOnClick: false,\n      renderBullet: null,\n      renderProgressbar: null,\n      renderFraction: null,\n      renderCustom: null,\n      progressbarOpposite: false,\n      type: 'bullets',\n      // 'bullets' or 'progressbar' or 'fraction' or 'custom'\n      dynamicBullets: false,\n      dynamicMainBullets: 1,\n      formatFractionCurrent: number => number,\n      formatFractionTotal: number => number,\n      bulletClass: `${pfx}-bullet`,\n      bulletActiveClass: `${pfx}-bullet-active`,\n      modifierClass: `${pfx}-`,\n      currentClass: `${pfx}-current`,\n      totalClass: `${pfx}-total`,\n      hiddenClass: `${pfx}-hidden`,\n      progressbarFillClass: `${pfx}-progressbar-fill`,\n      progressbarOppositeClass: `${pfx}-progressbar-opposite`,\n      clickableClass: `${pfx}-clickable`,\n      lockClass: `${pfx}-lock`,\n      horizontalClass: `${pfx}-horizontal`,\n      verticalClass: `${pfx}-vertical`,\n      paginationDisabledClass: `${pfx}-disabled`\n    }\n  });\n  swiper.pagination = {\n    el: null,\n    $el: null,\n    bullets: []\n  };\n  let bulletSize;\n  let dynamicBulletIndex = 0;\n\n  function isPaginationDisabled() {\n    return !swiper.params.pagination.el || !swiper.pagination.el || !swiper.pagination.$el || swiper.pagination.$el.length === 0;\n  }\n\n  function setSideBullets($bulletEl, position) {\n    const {\n      bulletActiveClass\n    } = swiper.params.pagination;\n    $bulletEl[position]().addClass(`${bulletActiveClass}-${position}`)[position]().addClass(`${bulletActiveClass}-${position}-${position}`);\n  }\n\n  function update() {\n    // Render || Update Pagination bullets/items\n    const rtl = swiper.rtl;\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const $el = swiper.pagination.$el; // Current/Total\n\n    let current;\n    const total = swiper.params.loop ? Math.ceil((slidesLength - swiper.loopedSlides * 2) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n\n    if (swiper.params.loop) {\n      current = Math.ceil((swiper.activeIndex - swiper.loopedSlides) / swiper.params.slidesPerGroup);\n\n      if (current > slidesLength - 1 - swiper.loopedSlides * 2) {\n        current -= slidesLength - swiper.loopedSlides * 2;\n      }\n\n      if (current > total - 1) current -= total;\n      if (current < 0 && swiper.params.paginationType !== 'bullets') current = total + current;\n    } else if (typeof swiper.snapIndex !== 'undefined') {\n      current = swiper.snapIndex;\n    } else {\n      current = swiper.activeIndex || 0;\n    } // Types\n\n\n    if (params.type === 'bullets' && swiper.pagination.bullets && swiper.pagination.bullets.length > 0) {\n      const bullets = swiper.pagination.bullets;\n      let firstIndex;\n      let lastIndex;\n      let midIndex;\n\n      if (params.dynamicBullets) {\n        bulletSize = bullets.eq(0)[swiper.isHorizontal() ? 'outerWidth' : 'outerHeight'](true);\n        $el.css(swiper.isHorizontal() ? 'width' : 'height', `${bulletSize * (params.dynamicMainBullets + 4)}px`);\n\n        if (params.dynamicMainBullets > 1 && swiper.previousIndex !== undefined) {\n          dynamicBulletIndex += current - (swiper.previousIndex - swiper.loopedSlides || 0);\n\n          if (dynamicBulletIndex > params.dynamicMainBullets - 1) {\n            dynamicBulletIndex = params.dynamicMainBullets - 1;\n          } else if (dynamicBulletIndex < 0) {\n            dynamicBulletIndex = 0;\n          }\n        }\n\n        firstIndex = Math.max(current - dynamicBulletIndex, 0);\n        lastIndex = firstIndex + (Math.min(bullets.length, params.dynamicMainBullets) - 1);\n        midIndex = (lastIndex + firstIndex) / 2;\n      }\n\n      bullets.removeClass(['', '-next', '-next-next', '-prev', '-prev-prev', '-main'].map(suffix => `${params.bulletActiveClass}${suffix}`).join(' '));\n\n      if ($el.length > 1) {\n        bullets.each(bullet => {\n          const $bullet = $(bullet);\n          const bulletIndex = $bullet.index();\n\n          if (bulletIndex === current) {\n            $bullet.addClass(params.bulletActiveClass);\n          }\n\n          if (params.dynamicBullets) {\n            if (bulletIndex >= firstIndex && bulletIndex <= lastIndex) {\n              $bullet.addClass(`${params.bulletActiveClass}-main`);\n            }\n\n            if (bulletIndex === firstIndex) {\n              setSideBullets($bullet, 'prev');\n            }\n\n            if (bulletIndex === lastIndex) {\n              setSideBullets($bullet, 'next');\n            }\n          }\n        });\n      } else {\n        const $bullet = bullets.eq(current);\n        const bulletIndex = $bullet.index();\n        $bullet.addClass(params.bulletActiveClass);\n\n        if (params.dynamicBullets) {\n          const $firstDisplayedBullet = bullets.eq(firstIndex);\n          const $lastDisplayedBullet = bullets.eq(lastIndex);\n\n          for (let i = firstIndex; i <= lastIndex; i += 1) {\n            bullets.eq(i).addClass(`${params.bulletActiveClass}-main`);\n          }\n\n          if (swiper.params.loop) {\n            if (bulletIndex >= bullets.length) {\n              for (let i = params.dynamicMainBullets; i >= 0; i -= 1) {\n                bullets.eq(bullets.length - i).addClass(`${params.bulletActiveClass}-main`);\n              }\n\n              bullets.eq(bullets.length - params.dynamicMainBullets - 1).addClass(`${params.bulletActiveClass}-prev`);\n            } else {\n              setSideBullets($firstDisplayedBullet, 'prev');\n              setSideBullets($lastDisplayedBullet, 'next');\n            }\n          } else {\n            setSideBullets($firstDisplayedBullet, 'prev');\n            setSideBullets($lastDisplayedBullet, 'next');\n          }\n        }\n      }\n\n      if (params.dynamicBullets) {\n        const dynamicBulletsLength = Math.min(bullets.length, params.dynamicMainBullets + 4);\n        const bulletsOffset = (bulletSize * dynamicBulletsLength - bulletSize) / 2 - midIndex * bulletSize;\n        const offsetProp = rtl ? 'right' : 'left';\n        bullets.css(swiper.isHorizontal() ? offsetProp : 'top', `${bulletsOffset}px`);\n      }\n    }\n\n    if (params.type === 'fraction') {\n      $el.find(classesToSelector(params.currentClass)).text(params.formatFractionCurrent(current + 1));\n      $el.find(classesToSelector(params.totalClass)).text(params.formatFractionTotal(total));\n    }\n\n    if (params.type === 'progressbar') {\n      let progressbarDirection;\n\n      if (params.progressbarOpposite) {\n        progressbarDirection = swiper.isHorizontal() ? 'vertical' : 'horizontal';\n      } else {\n        progressbarDirection = swiper.isHorizontal() ? 'horizontal' : 'vertical';\n      }\n\n      const scale = (current + 1) / total;\n      let scaleX = 1;\n      let scaleY = 1;\n\n      if (progressbarDirection === 'horizontal') {\n        scaleX = scale;\n      } else {\n        scaleY = scale;\n      }\n\n      $el.find(classesToSelector(params.progressbarFillClass)).transform(`translate3d(0,0,0) scaleX(${scaleX}) scaleY(${scaleY})`).transition(swiper.params.speed);\n    }\n\n    if (params.type === 'custom' && params.renderCustom) {\n      $el.html(params.renderCustom(swiper, current + 1, total));\n      emit('paginationRender', $el[0]);\n    } else {\n      emit('paginationUpdate', $el[0]);\n    }\n\n    if (swiper.params.watchOverflow && swiper.enabled) {\n      $el[swiper.isLocked ? 'addClass' : 'removeClass'](params.lockClass);\n    }\n  }\n\n  function render() {\n    // Render Container\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const slidesLength = swiper.virtual && swiper.params.virtual.enabled ? swiper.virtual.slides.length : swiper.slides.length;\n    const $el = swiper.pagination.$el;\n    let paginationHTML = '';\n\n    if (params.type === 'bullets') {\n      let numberOfBullets = swiper.params.loop ? Math.ceil((slidesLength - swiper.loopedSlides * 2) / swiper.params.slidesPerGroup) : swiper.snapGrid.length;\n\n      if (swiper.params.freeMode && swiper.params.freeMode.enabled && !swiper.params.loop && numberOfBullets > slidesLength) {\n        numberOfBullets = slidesLength;\n      }\n\n      for (let i = 0; i < numberOfBullets; i += 1) {\n        if (params.renderBullet) {\n          paginationHTML += params.renderBullet.call(swiper, i, params.bulletClass);\n        } else {\n          paginationHTML += `<${params.bulletElement} class=\"${params.bulletClass}\"></${params.bulletElement}>`;\n        }\n      }\n\n      $el.html(paginationHTML);\n      swiper.pagination.bullets = $el.find(classesToSelector(params.bulletClass));\n    }\n\n    if (params.type === 'fraction') {\n      if (params.renderFraction) {\n        paginationHTML = params.renderFraction.call(swiper, params.currentClass, params.totalClass);\n      } else {\n        paginationHTML = `<span class=\"${params.currentClass}\"></span>` + ' / ' + `<span class=\"${params.totalClass}\"></span>`;\n      }\n\n      $el.html(paginationHTML);\n    }\n\n    if (params.type === 'progressbar') {\n      if (params.renderProgressbar) {\n        paginationHTML = params.renderProgressbar.call(swiper, params.progressbarFillClass);\n      } else {\n        paginationHTML = `<span class=\"${params.progressbarFillClass}\"></span>`;\n      }\n\n      $el.html(paginationHTML);\n    }\n\n    if (params.type !== 'custom') {\n      emit('paginationRender', swiper.pagination.$el[0]);\n    }\n  }\n\n  function init() {\n    swiper.params.pagination = createElementIfNotDefined(swiper, swiper.originalParams.pagination, swiper.params.pagination, {\n      el: 'swiper-pagination'\n    });\n    const params = swiper.params.pagination;\n    if (!params.el) return;\n    let $el = $(params.el);\n    if ($el.length === 0) return;\n\n    if (swiper.params.uniqueNavElements && typeof params.el === 'string' && $el.length > 1) {\n      $el = swiper.$el.find(params.el); // check if it belongs to another nested Swiper\n\n      if ($el.length > 1) {\n        $el = $el.filter(el => {\n          if ($(el).parents('.swiper')[0] !== swiper.el) return false;\n          return true;\n        });\n      }\n    }\n\n    if (params.type === 'bullets' && params.clickable) {\n      $el.addClass(params.clickableClass);\n    }\n\n    $el.addClass(params.modifierClass + params.type);\n    $el.addClass(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n\n    if (params.type === 'bullets' && params.dynamicBullets) {\n      $el.addClass(`${params.modifierClass}${params.type}-dynamic`);\n      dynamicBulletIndex = 0;\n\n      if (params.dynamicMainBullets < 1) {\n        params.dynamicMainBullets = 1;\n      }\n    }\n\n    if (params.type === 'progressbar' && params.progressbarOpposite) {\n      $el.addClass(params.progressbarOppositeClass);\n    }\n\n    if (params.clickable) {\n      $el.on('click', classesToSelector(params.bulletClass), function onClick(e) {\n        e.preventDefault();\n        let index = $(this).index() * swiper.params.slidesPerGroup;\n        if (swiper.params.loop) index += swiper.loopedSlides;\n        swiper.slideTo(index);\n      });\n    }\n\n    Object.assign(swiper.pagination, {\n      $el,\n      el: $el[0]\n    });\n\n    if (!swiper.enabled) {\n      $el.addClass(params.lockClass);\n    }\n  }\n\n  function destroy() {\n    const params = swiper.params.pagination;\n    if (isPaginationDisabled()) return;\n    const $el = swiper.pagination.$el;\n    $el.removeClass(params.hiddenClass);\n    $el.removeClass(params.modifierClass + params.type);\n    $el.removeClass(swiper.isHorizontal() ? params.horizontalClass : params.verticalClass);\n    if (swiper.pagination.bullets && swiper.pagination.bullets.removeClass) swiper.pagination.bullets.removeClass(params.bulletActiveClass);\n\n    if (params.clickable) {\n      $el.off('click', classesToSelector(params.bulletClass));\n    }\n  }\n\n  on('init', () => {\n    if (swiper.params.pagination.enabled === false) {\n      // eslint-disable-next-line\n      disable();\n    } else {\n      init();\n      render();\n      update();\n    }\n  });\n  on('activeIndexChange', () => {\n    if (swiper.params.loop) {\n      update();\n    } else if (typeof swiper.snapIndex === 'undefined') {\n      update();\n    }\n  });\n  on('snapIndexChange', () => {\n    if (!swiper.params.loop) {\n      update();\n    }\n  });\n  on('slidesLengthChange', () => {\n    if (swiper.params.loop) {\n      render();\n      update();\n    }\n  });\n  on('snapGridLengthChange', () => {\n    if (!swiper.params.loop) {\n      render();\n      update();\n    }\n  });\n  on('destroy', () => {\n    destroy();\n  });\n  on('enable disable', () => {\n    const {\n      $el\n    } = swiper.pagination;\n\n    if ($el) {\n      $el[swiper.enabled ? 'removeClass' : 'addClass'](swiper.params.pagination.lockClass);\n    }\n  });\n  on('lock unlock', () => {\n    update();\n  });\n  on('click', (_s, e) => {\n    const targetEl = e.target;\n    const {\n      $el\n    } = swiper.pagination;\n\n    if (swiper.params.pagination.el && swiper.params.pagination.hideOnClick && $el && $el.length > 0 && !$(targetEl).hasClass(swiper.params.pagination.bulletClass)) {\n      if (swiper.navigation && (swiper.navigation.nextEl && targetEl === swiper.navigation.nextEl || swiper.navigation.prevEl && targetEl === swiper.navigation.prevEl)) return;\n      const isHidden = $el.hasClass(swiper.params.pagination.hiddenClass);\n\n      if (isHidden === true) {\n        emit('paginationShow');\n      } else {\n        emit('paginationHide');\n      }\n\n      $el.toggleClass(swiper.params.pagination.hiddenClass);\n    }\n  });\n\n  const enable = () => {\n    swiper.$el.removeClass(swiper.params.pagination.paginationDisabledClass);\n\n    if (swiper.pagination.$el) {\n      swiper.pagination.$el.removeClass(swiper.params.pagination.paginationDisabledClass);\n    }\n\n    init();\n    render();\n    update();\n  };\n\n  const disable = () => {\n    swiper.$el.addClass(swiper.params.pagination.paginationDisabledClass);\n\n    if (swiper.pagination.$el) {\n      swiper.pagination.$el.addClass(swiper.params.pagination.paginationDisabledClass);\n    }\n\n    destroy();\n  };\n\n  Object.assign(swiper.pagination, {\n    enable,\n    disable,\n    render,\n    update,\n    init,\n    destroy\n  });\n}", "/* eslint no-underscore-dangle: \"off\" */\n\n/* eslint no-use-before-define: \"off\" */\nimport { getDocument } from 'ssr-window';\nimport { nextTick } from '../../shared/utils.js';\nexport default function Autoplay({\n  swiper,\n  extendParams,\n  on,\n  emit\n}) {\n  let timeout;\n  swiper.autoplay = {\n    running: false,\n    paused: false\n  };\n  extendParams({\n    autoplay: {\n      enabled: false,\n      delay: 3000,\n      waitForTransition: true,\n      disableOnInteraction: true,\n      stopOnLastSlide: false,\n      reverseDirection: false,\n      pauseOnMouseEnter: false\n    }\n  });\n\n  function run() {\n    if (!swiper.size) {\n      swiper.autoplay.running = false;\n      swiper.autoplay.paused = false;\n      return;\n    }\n\n    const $activeSlideEl = swiper.slides.eq(swiper.activeIndex);\n    let delay = swiper.params.autoplay.delay;\n\n    if ($activeSlideEl.attr('data-swiper-autoplay')) {\n      delay = $activeSlideEl.attr('data-swiper-autoplay') || swiper.params.autoplay.delay;\n    }\n\n    clearTimeout(timeout);\n    timeout = nextTick(() => {\n      let autoplayResult;\n\n      if (swiper.params.autoplay.reverseDirection) {\n        if (swiper.params.loop) {\n          swiper.loopFix();\n          autoplayResult = swiper.slidePrev(swiper.params.speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.isBeginning) {\n          autoplayResult = swiper.slidePrev(swiper.params.speed, true, true);\n          emit('autoplay');\n        } else if (!swiper.params.autoplay.stopOnLastSlide) {\n          autoplayResult = swiper.slideTo(swiper.slides.length - 1, swiper.params.speed, true, true);\n          emit('autoplay');\n        } else {\n          stop();\n        }\n      } else if (swiper.params.loop) {\n        swiper.loopFix();\n        autoplayResult = swiper.slideNext(swiper.params.speed, true, true);\n        emit('autoplay');\n      } else if (!swiper.isEnd) {\n        autoplayResult = swiper.slideNext(swiper.params.speed, true, true);\n        emit('autoplay');\n      } else if (!swiper.params.autoplay.stopOnLastSlide) {\n        autoplayResult = swiper.slideTo(0, swiper.params.speed, true, true);\n        emit('autoplay');\n      } else {\n        stop();\n      }\n\n      if (swiper.params.cssMode && swiper.autoplay.running) run();else if (autoplayResult === false) {\n        run();\n      }\n    }, delay);\n  }\n\n  function start() {\n    if (typeof timeout !== 'undefined') return false;\n    if (swiper.autoplay.running) return false;\n    swiper.autoplay.running = true;\n    emit('autoplayStart');\n    run();\n    return true;\n  }\n\n  function stop() {\n    if (!swiper.autoplay.running) return false;\n    if (typeof timeout === 'undefined') return false;\n\n    if (timeout) {\n      clearTimeout(timeout);\n      timeout = undefined;\n    }\n\n    swiper.autoplay.running = false;\n    emit('autoplayStop');\n    return true;\n  }\n\n  function pause(speed) {\n    if (!swiper.autoplay.running) return;\n    if (swiper.autoplay.paused) return;\n    if (timeout) clearTimeout(timeout);\n    swiper.autoplay.paused = true;\n\n    if (speed === 0 || !swiper.params.autoplay.waitForTransition) {\n      swiper.autoplay.paused = false;\n      run();\n    } else {\n      ['transitionend', 'webkitTransitionEnd'].forEach(event => {\n        swiper.$wrapperEl[0].addEventListener(event, onTransitionEnd);\n      });\n    }\n  }\n\n  function onVisibilityChange() {\n    const document = getDocument();\n\n    if (document.visibilityState === 'hidden' && swiper.autoplay.running) {\n      pause();\n    }\n\n    if (document.visibilityState === 'visible' && swiper.autoplay.paused) {\n      run();\n      swiper.autoplay.paused = false;\n    }\n  }\n\n  function onTransitionEnd(e) {\n    if (!swiper || swiper.destroyed || !swiper.$wrapperEl) return;\n    if (e.target !== swiper.$wrapperEl[0]) return;\n    ['transitionend', 'webkitTransitionEnd'].forEach(event => {\n      swiper.$wrapperEl[0].removeEventListener(event, onTransitionEnd);\n    });\n    swiper.autoplay.paused = false;\n\n    if (!swiper.autoplay.running) {\n      stop();\n    } else {\n      run();\n    }\n  }\n\n  function onMouseEnter() {\n    if (swiper.params.autoplay.disableOnInteraction) {\n      stop();\n    } else {\n      emit('autoplayPause');\n      pause();\n    }\n\n    ['transitionend', 'webkitTransitionEnd'].forEach(event => {\n      swiper.$wrapperEl[0].removeEventListener(event, onTransitionEnd);\n    });\n  }\n\n  function onMouseLeave() {\n    if (swiper.params.autoplay.disableOnInteraction) {\n      return;\n    }\n\n    swiper.autoplay.paused = false;\n    emit('autoplayResume');\n    run();\n  }\n\n  function attachMouseEvents() {\n    if (swiper.params.autoplay.pauseOnMouseEnter) {\n      swiper.$el.on('mouseenter', onMouseEnter);\n      swiper.$el.on('mouseleave', onMouseLeave);\n    }\n  }\n\n  function detachMouseEvents() {\n    swiper.$el.off('mouseenter', onMouseEnter);\n    swiper.$el.off('mouseleave', onMouseLeave);\n  }\n\n  on('init', () => {\n    if (swiper.params.autoplay.enabled) {\n      start();\n      const document = getDocument();\n      document.addEventListener('visibilitychange', onVisibilityChange);\n      attachMouseEvents();\n    }\n  });\n  on('beforeTransitionStart', (_s, speed, internal) => {\n    if (swiper.autoplay.running) {\n      if (internal || !swiper.params.autoplay.disableOnInteraction) {\n        swiper.autoplay.pause(speed);\n      } else {\n        stop();\n      }\n    }\n  });\n  on('sliderFirstMove', () => {\n    if (swiper.autoplay.running) {\n      if (swiper.params.autoplay.disableOnInteraction) {\n        stop();\n      } else {\n        pause();\n      }\n    }\n  });\n  on('touchEnd', () => {\n    if (swiper.params.cssMode && swiper.autoplay.paused && !swiper.params.autoplay.disableOnInteraction) {\n      run();\n    }\n  });\n  on('destroy', () => {\n    detachMouseEvents();\n\n    if (swiper.autoplay.running) {\n      stop();\n    }\n\n    const document = getDocument();\n    document.removeEventListener('visibilitychange', onVisibilityChange);\n  });\n  Object.assign(swiper.autoplay, {\n    pause,\n    run,\n    start,\n    stop\n  });\n}", "export default function effectInit(params) {\n  const {\n    effect,\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    overwriteParams,\n    perspective,\n    recreateShadows,\n    getEffectParams\n  } = params;\n  on('beforeInit', () => {\n    if (swiper.params.effect !== effect) return;\n    swiper.classNames.push(`${swiper.params.containerModifierClass}${effect}`);\n\n    if (perspective && perspective()) {\n      swiper.classNames.push(`${swiper.params.containerModifierClass}3d`);\n    }\n\n    const overwriteParamsResult = overwriteParams ? overwriteParams() : {};\n    Object.assign(swiper.params, overwriteParamsResult);\n    Object.assign(swiper.originalParams, overwriteParamsResult);\n  });\n  on('setTranslate', () => {\n    if (swiper.params.effect !== effect) return;\n    setTranslate();\n  });\n  on('setTransition', (_s, duration) => {\n    if (swiper.params.effect !== effect) return;\n    setTransition(duration);\n  });\n  on('transitionEnd', () => {\n    if (swiper.params.effect !== effect) return;\n\n    if (recreateShadows) {\n      if (!getEffectParams || !getEffectParams().slideShadows) return; // remove shadows\n\n      swiper.slides.each(slideEl => {\n        const $slideEl = swiper.$(slideEl);\n        $slideEl.find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').remove();\n      }); // create new one\n\n      recreateShadows();\n    }\n  });\n  let requireUpdateOnVirtual;\n  on('virtualUpdate', () => {\n    if (swiper.params.effect !== effect) return;\n\n    if (!swiper.slides.length) {\n      requireUpdateOnVirtual = true;\n    }\n\n    requestAnimationFrame(() => {\n      if (requireUpdateOnVirtual && swiper.slides && swiper.slides.length) {\n        setTranslate();\n        requireUpdateOnVirtual = false;\n      }\n    });\n  });\n}", "export default function effectTarget(effectParams, $slideEl) {\n  if (effectParams.transformEl) {\n    return $slideEl.find(effectParams.transformEl).css({\n      'backface-visibility': 'hidden',\n      '-webkit-backface-visibility': 'hidden'\n    });\n  }\n\n  return $slideEl;\n}", "import $ from './dom.js';\nexport default function createShadow(params, $slideEl, side) {\n  const shadowClass = `swiper-slide-shadow${side ? `-${side}` : ''}`;\n  const $shadowContainer = params.transformEl ? $slideEl.find(params.transformEl) : $slideEl;\n  let $shadowEl = $shadowContainer.children(`.${shadowClass}`);\n\n  if (!$shadowEl.length) {\n    $shadowEl = $(`<div class=\"swiper-slide-shadow${side ? `-${side}` : ''}\"></div>`);\n    $shadowContainer.append($shadowEl);\n  }\n\n  return $shadowEl;\n}", "import createShadow from '../../shared/create-shadow.js';\nimport effectInit from '../../shared/effect-init.js';\nimport effectTarget from '../../shared/effect-target.js';\nexport default function EffectCoverflow({\n  swiper,\n  extendParams,\n  on\n}) {\n  extendParams({\n    coverflowEffect: {\n      rotate: 50,\n      stretch: 0,\n      depth: 100,\n      scale: 1,\n      modifier: 1,\n      slideShadows: true,\n      transformEl: null\n    }\n  });\n\n  const setTranslate = () => {\n    const {\n      width: swiperWidth,\n      height: swiperHeight,\n      slides,\n      slidesSizesGrid\n    } = swiper;\n    const params = swiper.params.coverflowEffect;\n    const isHorizontal = swiper.isHorizontal();\n    const transform = swiper.translate;\n    const center = isHorizontal ? -transform + swiperWidth / 2 : -transform + swiperHeight / 2;\n    const rotate = isHorizontal ? params.rotate : -params.rotate;\n    const translate = params.depth; // Each slide offset from center\n\n    for (let i = 0, length = slides.length; i < length; i += 1) {\n      const $slideEl = slides.eq(i);\n      const slideSize = slidesSizesGrid[i];\n      const slideOffset = $slideEl[0].swiperSlideOffset;\n      const centerOffset = (center - slideOffset - slideSize / 2) / slideSize;\n      const offsetMultiplier = typeof params.modifier === 'function' ? params.modifier(centerOffset) : centerOffset * params.modifier;\n      let rotateY = isHorizontal ? rotate * offsetMultiplier : 0;\n      let rotateX = isHorizontal ? 0 : rotate * offsetMultiplier; // var rotateZ = 0\n\n      let translateZ = -translate * Math.abs(offsetMultiplier);\n      let stretch = params.stretch; // Allow percentage to make a relative stretch for responsive sliders\n\n      if (typeof stretch === 'string' && stretch.indexOf('%') !== -1) {\n        stretch = parseFloat(params.stretch) / 100 * slideSize;\n      }\n\n      let translateY = isHorizontal ? 0 : stretch * offsetMultiplier;\n      let translateX = isHorizontal ? stretch * offsetMultiplier : 0;\n      let scale = 1 - (1 - params.scale) * Math.abs(offsetMultiplier); // Fix for ultra small values\n\n      if (Math.abs(translateX) < 0.001) translateX = 0;\n      if (Math.abs(translateY) < 0.001) translateY = 0;\n      if (Math.abs(translateZ) < 0.001) translateZ = 0;\n      if (Math.abs(rotateY) < 0.001) rotateY = 0;\n      if (Math.abs(rotateX) < 0.001) rotateX = 0;\n      if (Math.abs(scale) < 0.001) scale = 0;\n      const slideTransform = `translate3d(${translateX}px,${translateY}px,${translateZ}px)  rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${scale})`;\n      const $targetEl = effectTarget(params, $slideEl);\n      $targetEl.transform(slideTransform);\n      $slideEl[0].style.zIndex = -Math.abs(Math.round(offsetMultiplier)) + 1;\n\n      if (params.slideShadows) {\n        // Set shadows\n        let $shadowBeforeEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-left') : $slideEl.find('.swiper-slide-shadow-top');\n        let $shadowAfterEl = isHorizontal ? $slideEl.find('.swiper-slide-shadow-right') : $slideEl.find('.swiper-slide-shadow-bottom');\n\n        if ($shadowBeforeEl.length === 0) {\n          $shadowBeforeEl = createShadow(params, $slideEl, isHorizontal ? 'left' : 'top');\n        }\n\n        if ($shadowAfterEl.length === 0) {\n          $shadowAfterEl = createShadow(params, $slideEl, isHorizontal ? 'right' : 'bottom');\n        }\n\n        if ($shadowBeforeEl.length) $shadowBeforeEl[0].style.opacity = offsetMultiplier > 0 ? offsetMultiplier : 0;\n        if ($shadowAfterEl.length) $shadowAfterEl[0].style.opacity = -offsetMultiplier > 0 ? -offsetMultiplier : 0;\n      }\n    }\n  };\n\n  const setTransition = duration => {\n    const {\n      transformEl\n    } = swiper.params.coverflowEffect;\n    const $transitionElements = transformEl ? swiper.slides.find(transformEl) : swiper.slides;\n    $transitionElements.transition(duration).find('.swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left').transition(duration);\n  };\n\n  effectInit({\n    effect: 'coverflow',\n    swiper,\n    on,\n    setTranslate,\n    setTransition,\n    perspective: () => true,\n    overwriteParams: () => ({\n      watchSlidesProgress: true\n    })\n  });\n}", "import { extend } from 'flarum/extend';\nimport app from 'flarum/app';\nimport HeaderPrimary from 'flarum/forum/components/HeaderPrimary';\n\nimport Swiper from 'swiper';\nimport { EffectCoverflow, Navigation, Pagination, Autoplay} from \"swiper\";\n\nconst checkTime = 10;\nlet tronscanListLoading = false;\nlet tronscanList = null;\nlet linksQueueListLoading = false;\nlet linksQueueList = null;\nlet linksQueuePointer = 0;\nlet buttonsCustomizationListLoading = false;\nlet buttonsCustomizationList = null;\nconst isMobileView = mobileCheck();\n\napp.initializers.add('wusong8899-client1-header-adv', () => {\n    extend(HeaderPrimary.prototype, 'view', function (vnode) {\n        const routeName = app.current.get('routeName');\n\n        if(routeName){\n            if(routeName!==\"tags\"){\n\n            }else{\n                attachAdvertiseHeader(vnode);\n            }\n        }\n    });\n\n    // extend(HeaderPrimary.prototype, 'oncreate', function (vnode) {\n    //     $(\"#app .App-content\").append($(\"#customFooter\"));\n    // });\n});\n\nfunction mobileCheck() {\n  let check = false;\n  (function(a){if(/(android|bb\\d+|meego).+mobile|avantgo|bada\\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\\-(n|u)|c55\\/|capi|ccwa|cdm\\-|cell|chtm|cldc|cmd\\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\\-s|devi|dica|dmob|do(c|p)o|ds(12|\\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\\-|_)|g1 u|g560|gene|gf\\-5|g\\-mo|go(\\.w|od)|gr(ad|un)|haie|hcit|hd\\-(m|p|t)|hei\\-|hi(pt|ta)|hp( i|ip)|hs\\-c|ht(c(\\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\\-(20|go|ma)|i230|iac( |\\-|\\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\\/)|klon|kpt |kwc\\-|kyo(c|k)|le(no|xi)|lg( g|\\/(k|l|u)|50|54|\\-[a-w])|libw|lynx|m1\\-w|m3ga|m50\\/|ma(te|ui|xo)|mc(01|21|ca)|m\\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\\-2|po(ck|rt|se)|prox|psio|pt\\-g|qa\\-a|qc(07|12|21|32|60|\\-[2-7]|i\\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\\-|oo|p\\-)|sdk\\/|se(c(\\-|0|1)|47|mc|nd|ri)|sgh\\-|shar|sie(\\-|m)|sk\\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\\-|v\\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\\-|tdg\\-|tel(i|m)|tim\\-|t\\-mo|to(pl|sh)|ts(70|m\\-|m3|m5)|tx\\-9|up(\\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\\-|your|zeto|zte\\-/i.test(a.substr(0,4))) check = true;})(navigator.userAgent||navigator.vendor||window.opera);\n  return check;\n};\n\nfunction parseTronscanResults(results){\n    tronscanList = [];\n    [].push.apply(tronscanList, results);\n    return results;\n}\n\nfunction loadTronscanList(){\n    if(tronscanListLoading===false){\n        tronscanListLoading = true;\n        return app.store\n            .find(\"syncTronscanList\")\n            .catch(() => {})\n            .then(parseTronscanResults.bind(this));\n    }\n}\n\nfunction parseButtonsCustomizationResults(results){\n    buttonsCustomizationList = [];\n    [].push.apply(buttonsCustomizationList, results);\n    return results;\n}\n\nfunction loadButtonsCustomizationList(){\n    if(buttonsCustomizationListLoading===false){\n        buttonsCustomizationListLoading = true;\n        return app.store\n          .find(\"buttonsCustomizationList\")\n          .catch(() => {})\n          .then(parseButtonsCustomizationResults.bind(this));\n    }\n}\n\nfunction parseLinksQueueResults(results) {\n    linksQueueList = [];\n    [].push.apply(linksQueueList, results);\n    return results;\n}\n\nfunction loadLinksQueueList() {\n    if(linksQueueListLoading===false){\n        linksQueueListLoading = true;\n        return app.store\n            .find(\"linksQueueList\")\n            .catch(() => {})\n            .then(parseLinksQueueResults.bind(this));\n    }\n}\n\nfunction changeCategoryLayout(){\n    const tagTile = $(\".TagTile\");\n\n    if($(\"#swiperTagContainer\").length===0){\n        let swiperContainer = document.createElement(\"div\");\n        swiperContainer.className = \"swiperTagContainer\";\n        swiperContainer.id = \"swiperTagContainer\";\n\n        let swiper = document.createElement(\"div\");\n        swiper.className = \"swiper tagSwiper\";\n\n        let TagTextOuterContainer = document.createElement(\"div\");\n        TagTextOuterContainer.className = \"TagTextOuterContainer\";\n\n        swiperContainer.appendChild(TagTextOuterContainer);\n        TagTextOuterContainer.appendChild(swiper);\n\n        let swiper_wrapper = document.createElement(\"div\");\n        swiper_wrapper.className = \"swiper-wrapper\";\n        swiper_wrapper.id = \"swiperTagWrapper\";\n        swiper.appendChild(swiper_wrapper);\n\n        for(let i=0;i<tagTile.length;i++){\n            let tag = tagTile[i];\n            let tagURL = $(tag).find(\"a\").attr(\"href\")\n            let tagBackground = $(tag).css(\"background\");\n            let tagName = $(tag).find(\".TagTile-name\").text();\n            let tagNameColor = $(tag).find(\".TagTile-name\").css(\"color\");\n            let tagDesc = $(tag).find(\".TagTile-description\").text();\n            let tagDescColor = $(tag).find(\".TagTile-description\").css(\"color\");\n\n            if(tagName===\"Review\"){\n                // tagBackground = \"url(https://k-img.picimgfield.com/live/image/games/psh_mysterymissiontothemoon-en-US.png);\";\n            }\n\n            let swiper_slide = document.createElement(\"div\");\n            swiper_slide.className = \"swiper-slide swiper-slide-tag\";\n            swiper_slide.innerHTML = \"<a href='\"+tagURL+\"'><div class='\"+(isMobileView?'swiper-slide-tag-inner-mobile':'swiper-slide-tag-inner')+\"' style='background:\"+tagBackground+\";background-size: cover;background-position: center;background-repeat: no-repeat;'><div style='font-weight:bold;font-size:14px;color:\"+tagNameColor+\"'>\"+tagName+\"</div></div></a>\";\n\n            swiper_wrapper.appendChild(swiper_slide);\n        }\n\n        $(\"#content .container .TagsPage-content\").prepend(swiperContainer);\n        $(TagTextOuterContainer).prepend(\"<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>\");\n        $(TagTextOuterContainer).append('<div style=\"text-align:center;padding-top: 10px;\"><button class=\"Button Button--primary\" type=\"button\" style=\"font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;\"><div style=\"margin-top: 5px;\" class=\"Button-label\"><img onClick=\"window.open(\\'https://kick.com/wangming886\\', \\'_blank\\')\" style=\"width: 32px;\" src=\"https://mutluresim.com/images/2023/04/10/KcgSG.png\"><img onClick=\"window.open(\\'https://m.facebook.com\\', \\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcF6i.png\"><img onClick=\"window.open(\\'https://twitter.com/youngron131_\\', \\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcDas.png\"><img onClick=\"window.open(\\'https://m.youtube.com/@ag8888\\',\\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcQjd.png\"><img onClick=\"window.open(\\'https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=\\', \\'_blank\\')\" style=\"width: 32px;margin-left: 20px;\" src=\"https://mutluresim.com/images/2023/04/10/KcBAL.png\"></div></button></div>');\n        // $(\"#content .container .TagsPage-content\").append(\"<iframe style='width: 110%;border: 0;height: 500px;margin-top: 30px;margin-left: -20px;' name='contentOnly' src='https://lg666.cc/biddingRank'></iframe>\");\n        $(\".TagTiles\").remove();\n\n        if(isMobileView===true){\n            $(\"#app\").css(\"overflow-x\",\"hidden\");\n            $(\".App-content\").css(\"min-height\",\"auto\");\n            $(\".App-content\").css(\"background\",\"\");\n        }\n\n        new Swiper(\".tagSwiper\", {\n            loop:true,\n            spaceBetween: isMobileView?90:10,\n            slidesPerView: isMobileView?2:7,\n            autoplay: {\n              delay: 3000,\n              disableOnInteraction: false,\n            },\n            modules: [Autoplay]\n          });\n\n        addTronscan();\n    }\n}\n\nfunction addTronscan(){\n    let TronscanTextContainer = document.getElementById(\"TronscanTextContainer\");\n\n    if(TronscanTextContainer===null){\n        TronscanTextContainer = document.createElement(\"div\");\n        TronscanTextContainer.id = \"TronscanTextContainer\";\n        TronscanTextContainer.innerHTML = \"<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度\";\n        TronscanTextContainer.className = \"TronscanTextContainer\";\n        $(\"#swiperTagContainer\").append(TronscanTextContainer);\n\n        let swiper = document.createElement(\"div\");\n        swiper.className = \"swiper tronscanSwiper\";\n        $(\"#swiperTagContainer\").append(swiper);\n\n        let swiper_wrapper = document.createElement(\"div\");\n        swiper_wrapper.className = \"swiper-wrapper\";\n        swiper.appendChild(swiper_wrapper);\n\n        for(let i=0;i<tronscanList.length;i++){\n            let tronscanData = tronscanList[i];\n            let tronscanName = tronscanData.name();\n            let tronscanValueUsd = parseInt(tronscanData.valueUsd())+\" USD\";\n            let tronscanBackground = \"url(\"+tronscanData.img()+\");\";\n\n            let swiper_slide = document.createElement(\"div\");\n            swiper_slide.className = \"swiper-slide swiper-slide-tag\";\n            swiper_slide.innerHTML = \"<div style='width:100px;height:130px;border-radius: 12px;background: \"+tronscanBackground+\";background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'><div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div><div class='tronscanMask'><div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'><span>\"+tronscanValueUsd+\"</span></div></div></div>\";\n\n            swiper_wrapper.appendChild(swiper_slide);\n        }\n\n        new Swiper(\".tronscanSwiper\", {\n            loop:true,\n            spaceBetween: isMobileView?80:10,\n            slidesPerView: isMobileView?4:7,\n          });\n    }\n\n}\n\nfunction moveLeaderBoard(){\n    $(\".item-MoneyLeaderboard\").addClass(\"App-primaryControl\");\n    $(\".item-forum-checkin\").parent().append($(\".item-MoneyLeaderboard\"));\n    $(\".item-MoneyLeaderboard\").css(\"right\",\"75px\");\n}\n\nfunction addButtons(){\n    let selectTitleContainer = document.getElementById(\"selectTitleContainer\");\n\n    if(selectTitleContainer===null){\n        selectTitleContainer = document.createElement(\"div\");\n        selectTitleContainer.id = \"selectTitleContainer\";\n        selectTitleContainer.className = \"selectTitleContainer\";\n\n        let buttonsCustomization = \"\";\n        let buttonsCustomizationMap = {};\n        let totalButtons = 3;\n        for(let i=0;i<buttonsCustomizationList.length;i++){\n            let buttonsCustomizationData = buttonsCustomizationList[i];\n            let buttonsCustomizationName = buttonsCustomizationData.name();\n            let buttonsCustomizationIcon = buttonsCustomizationData.icon();\n            let buttonsCustomizationColor = buttonsCustomizationData.color();\n            let buttonsCustomizationURL = buttonsCustomizationData.url();\n\n            totalButtons++;\n            buttonsCustomizationMap[totalButtons] = {url:buttonsCustomizationURL};\n            buttonsCustomization+='<button id=\"client1HeaderButton'+totalButtons+'\" number=\"'+totalButtons+'\" type=\"button\" class=\"u-btn\"><i class=\"'+buttonsCustomizationIcon+'\"></i><div class=\"u-btn-text\">'+buttonsCustomizationName+'</div></button>';\n        }\n\n        let selectTitle = document.createElement(\"div\");\n        selectTitle.className = \"selectTitle\";\n        selectTitle.innerHTML = '<div class=\"switch-btns\" style=\"max-width:'+$(\".TagsPage-content\").width()+'px\"><div class=\"btns-container\"><button id=\"client1HeaderButton0\" type=\"button\" class=\"u-btn\" number=\"0\"><i class=\"fas fa-paw\"></i><div class=\"u-btn-text\">论坛</div></button><button id=\"client1HeaderButton1\" number=\"1\" type=\"button\" class=\"u-btn\"><i class=\"fab fa-twitch\"></i><div class=\"u-btn-text\">直播</div></button><button id=\"client1HeaderButton2\" number=\"2\" type=\"button\" class=\"u-btn\"><i class=\"fas fa-dice\"></i><div class=\"u-btn-text\">游戏</div></button><button id=\"client1HeaderButton3\" number=\"3\" type=\"button\" class=\"u-btn\"><i class=\"fas fa-gifts\"></i><div class=\"u-btn-text\">商城</div></button>'+buttonsCustomization+'<div id=\"buttonSelectedBackground\" class=\"selected-bg\" style=\"left: 0px; top: 0px; opacity: 1;\"></div></div></div>';  \n        selectTitleContainer.appendChild(selectTitle);\n\n        $(\"#content .TagsPage-content\").prepend(selectTitleContainer);\n\n        const eventType = isMobileView?\"touchend\":\"click\";\n        let leftValuePrev = 0;\n        let leftValueMap = {};\n        let leftModifier = isMobileView?3:0;\n\n        leftValueMap[0] = 0;\n        for(let i=0;i<totalButtons;i++){\n            let leftValue = $(\"#client1HeaderButton\"+i).outerWidth();\n           \n            if(i===1 || i===2){\n                continue;\n            }\n\n            if(i===0){\n                $(\"#buttonSelectedBackground\").width(leftValue);\n            }\n\n            leftValueMap[i+1] = leftValue+leftValuePrev-leftModifier;\n            leftValuePrev += leftValue;\n        }\n\n        $('.u-btn').on(eventType, function(){\n            const number = parseInt($(this).attr('number'));\n            let zhiboIframe = document.getElementById(\"zhiboIframe\");\n\n            $(\".App\").css(\"min-height\",\"100vh\");\n\n            if(number===0){\n                $(\".swiperTagContainer\").css(\"display\",\"\");\n                $(\".zhiboContainer\").css(\"display\",\"none\");\n                $(\".youxiContainer\").css(\"display\",\"none\");\n                $(\".buttonCustomizationContainer\").css(\"display\",\"none\");\n                $(\".shangchengContainer\").css(\"display\",\"none\");\n                $(\".App\").css(\"min-height\",\"50vh\");\n                zhiboIframe.src = \"\";\n            }else if(number===1){\n                $(\".swiperTagContainer\").css(\"display\",\"none\");\n                $(\".zhiboContainer\").css(\"display\",\"inline-block\");\n                $(\".youxiContainer\").css(\"display\",\"none\");\n                $(\".buttonCustomizationContainer\").css(\"display\",\"none\");\n                $(\".shangchengContainer\").css(\"display\",\"none\");\n\n                const iframeHeight = window.innerHeight-$(\"#app-navigation\").outerHeight()-$(\".selectTitleContainer\").outerHeight()-$(\"#linksQueuePrev\").outerHeight();\n                $(\"#zhiboIframe\").css(\"height\",iframeHeight+\"px\");\n\n                if(linksQueueList[linksQueuePointer]){\n                    let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\n\n                    if(zhiboIframe.src!==linksQueueURL){\n                        zhiboIframe.src = linksQueueURL;\n                    }\n                }\n            }else if(number===2){\n                $(\".swiperTagContainer\").css(\"display\",\"none\");\n                $(\".zhiboContainer\").css(\"display\",\"none\");\n                $(\".youxiContainer\").css(\"display\",\"flex\");\n                $(\".buttonCustomizationContainer\").css(\"display\",\"none\");\n                $(\".shangchengContainer\").css(\"display\",\"none\");\n                zhiboIframe.src = \"\";\n            }else if(number===3){\n                $(\".swiperTagContainer\").css(\"display\",\"none\");\n                $(\".zhiboContainer\").css(\"display\",\"none\");\n                $(\".youxiContainer\").css(\"display\",\"none\");\n                $(\".buttonCustomizationContainer\").css(\"display\",\"none\");\n                $(\".shangchengContainer\").css(\"display\",\"flex\");\n                zhiboIframe.src = \"\";\n            }else{\n                const customButtonData = buttonsCustomizationMap[number];\n\n                if(customButtonData){\n                    let iframeHeight = window.innerHeight-$(\"#app-navigation\").outerHeight()-$(\".selectTitleContainer\").outerHeight()-$(\"#linksQueuePrev\").outerHeight();\n                    let paddingBottom = 0;\n                    let scrolling = \"yes\";\n                    let containerHeight = $(\".swiperTagContainer\").css(\"height\");\n\n                    if(number==5){\n                        iframeHeight = 550;\n                        containerHeight = 550;\n                    }else if(number==6){\n                        iframeHeight = containerHeight = 380;\n                        scrolling = \"no\";\n                    }else if(number==7){\n                        paddingBottom = 20;\n                    }else if(number==8){\n                        paddingBottom = 20;\n                    }\n\n                    $(\".buttonCustomizationContainer\").css(\"padding-bottom\",paddingBottom+\"px\");\n                    $(\"#customButtonIframe\").css(\"padding-bottom\",paddingBottom+\"px\");\n                    $('#customButtonIframe').attr(\"scrolling\",scrolling);\n\n                    $(\".buttonCustomizationContainer\").css(\"height\",containerHeight+\"px\");\n                    $(\"#customButtonIframe\").css(\"height\",iframeHeight+\"px\");\n\n                    let customButtonIframe = document.getElementById(\"customButtonIframe\");\n                    $(\".swiperTagContainer\").css(\"display\",\"none\");\n                    $(\".zhiboContainer\").css(\"display\",\"none\");\n                    $(\".youxiContainer\").css(\"display\",\"none\");\n                    $(\".buttonCustomizationContainer\").css(\"display\",\"inline-block\");\n                    $(\".shangchengContainer\").css(\"display\",\"none\");\n\n                    customButtonIframe.src = customButtonData.url;\n                }\n            }\n\n            $(\"#buttonSelectedBackground\").width($(this).outerWidth());\n\n            if(leftValueMap[number]!==undefined){\n                $(\"#buttonSelectedBackground\").css(\"left\",leftValueMap[number]);\n            }\n        });\n\n    }\n}\n\nfunction addZhiBoContainer(){\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\n\n    let zhiboContainer = document.createElement(\"div\");\n    zhiboContainer.className = \"zhiboContainer\";\n    zhiboContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\n\n    const refreshButton = \"<div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div>\";\n    const prevButton = \"<div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div>\";\n    const nextButton = \"<div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div>\";\n    zhiboContainer.innerHTML = \"<div id='linksQueueRefresh' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: \"+(isMobileView?0:-6)+\"px;'>\"+refreshButton+\"</div><div class='zhiboSubContainer'><div id='linksQueuePrev' style='display:inline-block;scale:0.8'>\"+prevButton+\"</div><div id='linksQueueNext' style='display:inline-block;scale:0.8'>\"+nextButton+\"</div></div><iframe id='zhiboIframe' name='contentOnly' class='zhiboIframe' src=''></iframe>\";\n\n    $(\"#content .TagsPage-content\").prepend(zhiboContainer);\n\n    const eventType = isMobileView?\"touchend\":\"click\";\n    let zhiboIframe = document.getElementById(\"zhiboIframe\");\n\n    $('#linksQueueRefresh').on(eventType, function(){\n        zhiboIframe.src = \"\";\n\n        setTimeout(function(){\n            let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\n            zhiboIframe.src = linksQueueURL;\n        },100)\n    });\n\n    $('#linksQueuePrev').on(eventType, function(){\n\n        linksQueuePointer--;\n\n        if(linksQueueList[linksQueuePointer]!==undefined){\n            let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\n            zhiboIframe.src = linksQueueURL;\n        }\n\n        $(\"#nextZhiBoButton\").css(\"color\",\"\");\n        if(linksQueueList[linksQueuePointer-1]===undefined){\n            $(\"#prevZhiBoButton\").css(\"color\",\"#666\");\n        }else{\n            $(\"#prevZhiBoButton\").css(\"color\",\"\");\n        }\n    });\n\n    $('#linksQueueNext').on(eventType, function(){\n        linksQueuePointer++;\n\n        if(linksQueueList[linksQueuePointer]!==undefined){\n            let linksQueueURL = linksQueueList[linksQueuePointer].attribute(\"links\");\n            zhiboIframe.src = linksQueueURL;\n        }\n\n        $(\"#prevZhiBoButton\").css(\"color\",\"\");\n        if(linksQueueList[linksQueuePointer+1]===undefined){\n            $(\"#nextZhiBoButton\").css(\"color\",\"#666\");\n        }else{\n            $(\"#nextZhiBoButton\").css(\"color\",\"\");\n        }\n    });\n}\n\nfunction addButtonCustomizationContainer(){\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\n\n    let buttonCustomizationContainer = document.createElement(\"div\");\n    buttonCustomizationContainer.className = \"buttonCustomizationContainer\";\n    buttonCustomizationContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\n    buttonCustomizationContainer.innerHTML = \"<iframe id='customButtonIframe' name='contentOnly' class='customButtonIframe' src=''></iframe>\"\n\n    $(\"#content .TagsPage-content\").prepend(buttonCustomizationContainer);\n}\n\nfunction addYouXiContainer(){\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\n\n    let youxiContainer = document.createElement(\"div\");\n    youxiContainer.className = \"youxiContainer\";\n    youxiContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\n    youxiContainer.innerText = app.translator.trans(\"wusong8899-client1.forum.under-construction\");\n\n    $(\"#content .TagsPage-content\").prepend(youxiContainer);\n}\n\nfunction addShangChengContainer(){\n    const swiperTagContainerHeight = $(\".swiperTagContainer\").css(\"height\");\n\n    let shangchengContainer = document.createElement(\"div\");\n    shangchengContainer.className = \"shangchengContainer\";\n    shangchengContainer.style.height = $(\".swiperTagContainer\").css(\"height\");\n    shangchengContainer.innerText = app.translator.trans(\"wusong8899-client1.forum.under-construction\");\n\n    $(\"#content .TagsPage-content\").prepend(shangchengContainer);\n}\n\nfunction addHeaderIcon(){\n    let headerIconContainer = document.getElementById(\"wusong8899Client1HeaderIcon\");\n\n    if(headerIconContainer===null){\n        headerIconContainer = document.createElement(\"div\");\n        headerIconContainer.id = \"wusong8899Client1HeaderIcon\";\n        headerIconContainer.style.display = 'inline-block';\n        headerIconContainer.style.marginTop = '8px';\n        headerIconContainer.innerHTML = '<img src=\"https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png\" style=\"height: 24px;\" />';\n\n        $(\"#app-navigation\").find(\".App-backControl\").prepend(headerIconContainer);\n    }\n}\n\nfunction attachAdvertiseHeader(vdom: Vnode<any>): void {\n    if(isMobileView){\n        $(\".item-newDiscussion\").find(\"span.Button-label\").html(\"<div class='buttonRegister'>登录</div>\");\n        $(\".item-newDiscussion\").find(\"span.Button-label\").css(\"display\",\"block\");\n        $(\".item-newDiscussion\").find(\"span.Button-label\").css(\"font-size\",\"14px\");\n        $(\".item-newDiscussion\").find(\"span.Button-label\").css(\"word-spacing\",\"-1px\");\n    }\n\n    $(\".item-newDiscussion\").find(\"i\").css(\"display\",\"none\");\n    $(\".item-nav\").remove();\n    $(\".TagTiles\").css(\"display\",\"none\");\n\n    let task = setInterval(function(){\n        if(vdom.dom){\n            clearInterval(task);\n\n            if(vdom.dom!==undefined){\n                let TransitionTime = app.forum.attribute('Client1HeaderAdvTransitionTime');\n\n                if(!TransitionTime){\n                    TransitionTime = 5000;\n                }\n\n                let screenWidth = $(window).width();\n                let styleWidth = screenWidth*2-50;\n\n                let swiperContainer = document.getElementById(\"swiperAdContainer\");\n\n                if(swiperContainer!==null){\n                    return;\n                }\n\n                swiperContainer = document.createElement(\"div\");\n                swiperContainer.className = \"swiperAdContainer\";\n                swiperContainer.id = \"swiperAdContainer\";\n\n                if(isMobileView===true){\n                    swiperContainer.style.width = styleWidth+\"px\";\n                    swiperContainer.style.marginLeft = -(styleWidth*0.254)+\"px\";\n                }\n\n                let swiper = document.createElement(\"div\");\n                swiper.className = \"swiper adSwiper\";\n                swiperContainer.appendChild(swiper);\n\n                let swiper_wrapper = document.createElement(\"div\");\n                swiper_wrapper.className = \"swiper-wrapper\";\n                swiper.appendChild(swiper_wrapper);\n\n                for(let i=1;i<=30;i++){\n                    let swiper_slide = document.createElement(\"div\");\n                    let imageSrc = app.forum.attribute('Client1HeaderAdvImage'+i);\n                    let imageLink = app.forum.attribute('Client1HeaderAdvLink'+i);\n\n                    if(imageSrc){\n                        swiper_slide.className = \"swiper-slide\";\n                        swiper_slide.innerHTML = \"<img onclick='window.location.href=\\\"\"+imageLink+\"\\\"' src='\"+imageSrc+\"' />\";\n                        swiper_wrapper.appendChild(swiper_slide);\n                    }\n                }\n\n                let swiper_button_next = document.createElement(\"div\");\n                swiper_button_next.className = \"swiper-button-next\";\n                swiper.appendChild(swiper_button_next);\n\n                let swiper_button_prev = document.createElement(\"div\");\n                swiper_button_prev.className = \"swiper-button-prev\";\n                swiper.appendChild(swiper_button_prev);\n\n                let swiper_pagination = document.createElement(\"div\");\n                swiper_pagination.className = \"swiper-pagination\";\n                swiper.appendChild(swiper_pagination);\n\n                $(\"#content .container\").prepend(swiperContainer);\n\n                new Swiper(\".adSwiper\", {\n                    autoplay: {\n                       delay: TransitionTime,\n                     },\n                    loop: true,\n                    spaceBetween: 30,\n                    effect: \"coverflow\",\n                    centeredSlides: true,\n                    slidesPerView: 2,\n                    coverflowEffect: {\n                        rotate: 0,\n                        depth: 100,\n                        modifier: 1,\n                        slideShadows: true,\n                        stretch:0\n                    },\n                    pagination: {\n                        el: '.swiper-pagination',\n                        type: 'bullets',\n                    },\n                    navigation: {\n                        nextEl: '.swiper-button-next',\n                        prevEl: '.swiper-button-prev',\n                    },\n                    modules: [EffectCoverflow, Navigation, Pagination, Autoplay]\n                  });\n\n                loadLinksQueueList();\n                loadTronscanList();\n                loadButtonsCustomizationList();\n\n                let checkDataTask = setInterval(function(){\n                    if(tronscanList!==null && linksQueueList!==null && buttonsCustomizationList!==null){\n                        clearInterval(checkDataTask);\n\n                        if($(\"#swiperTagContainer\").length===0){\n                            changeCategoryLayout();\n                            addZhiBoContainer();\n                            addYouXiContainer();\n                            addButtonCustomizationContainer();\n                            addShangChengContainer();\n                            addButtons();\n                            moveLeaderBoard();\n                        }\n\n                        if(!app.session.user){\n                            addHeaderIcon();\n                        }\n                    }\n                },100);\n            }\n        }\n    },checkTime);\n}\n\n\n\n\n\n"], "sourceRoot": ""}