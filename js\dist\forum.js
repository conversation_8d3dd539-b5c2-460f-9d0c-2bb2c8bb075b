module.exports=function(e){var t={};function i(s){if(t[s])return t[s].exports;var n=t[s]={i:s,l:!1,exports:{}};return e[s].call(n.exports,n,n.exports,i),n.l=!0,n.exports}return i.m=e,i.c=t,i.d=function(e,t,s){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:s})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var s=Object.create(null);if(i.r(s),Object.defineProperty(s,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(s,n,function(t){return e[t]}.bind(null,n));return s},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=3)}([function(e,t){e.exports=flarum.core.compat.app},function(e,t){e.exports=flarum.core.compat.extend},function(e,t){e.exports=flarum.core.compat["forum/components/HeaderPrimary"]},function(e,t,i){"use strict";i.r(t);var s=i(1),n=i(0),a=i.n(n),r=i(2),o=i.n(r);function l(e,t){for(var i=0;i<t.length;i++){var s=t[i];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function d(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function c(e,t){void 0===e&&(e={}),void 0===t&&(t={}),Object.keys(t).forEach((function(i){void 0===e[i]?e[i]=t[i]:d(t[i])&&d(e[i])&&Object.keys(t[i]).length>0&&c(e[i],t[i])}))}var p={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function u(){var e="undefined"!=typeof document?document:{};return c(e,p),e}var h={document:p,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function f(){var e="undefined"!=typeof window?window:{};return c(e,h),e}function v(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function m(e,t){return(m=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function g(e){return(g=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function b(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function w(e,t,i){return(w=b()?Reflect.construct:function(e,t,i){var s=[null];s.push.apply(s,t);var n=new(Function.bind.apply(e,s));return i&&m(n,i.prototype),n}).apply(null,arguments)}function y(e){var t="function"==typeof Map?new Map:void 0;return(y=function(e){if(null===e||(i=e,-1===Function.toString.call(i).indexOf("[native code]")))return e;var i;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,s)}function s(){return w(e,arguments,g(this).constructor)}return s.prototype=Object.create(e.prototype,{constructor:{value:s,enumerable:!1,writable:!0,configurable:!0}}),m(s,e)})(e)}var C=function(e){var t,i;function s(t){var i,s,n;return"number"==typeof t?i=e.call(this,t)||this:(i=e.call.apply(e,[this].concat(t||[]))||this,s=v(i),n=s.__proto__,Object.defineProperty(s,"__proto__",{get:function(){return n},set:function(e){n.__proto__=e}})),v(i)}return i=e,(t=s).prototype=Object.create(i.prototype),t.prototype.constructor=t,m(t,i),s}(y(Array));function T(e){void 0===e&&(e=[]);var t=[];return e.forEach((function(e){Array.isArray(e)?t.push.apply(t,T(e)):t.push(e)})),t}function x(e,t){return Array.prototype.filter.call(e,t)}function S(e,t){var i=f(),s=u(),n=[];if(!t&&e instanceof C)return e;if(!e)return new C(n);if("string"==typeof e){var a=e.trim();if(a.indexOf("<")>=0&&a.indexOf(">")>=0){var r="div";0===a.indexOf("<li")&&(r="ul"),0===a.indexOf("<tr")&&(r="tbody"),0!==a.indexOf("<td")&&0!==a.indexOf("<th")||(r="tr"),0===a.indexOf("<tbody")&&(r="table"),0===a.indexOf("<option")&&(r="select");var o=s.createElement(r);o.innerHTML=a;for(var l=0;l<o.childNodes.length;l+=1)n.push(o.childNodes[l])}else n=function(e,t){if("string"!=typeof e)return[e];for(var i=[],s=t.querySelectorAll(e),n=0;n<s.length;n+=1)i.push(s[n]);return i}(e.trim(),t||s)}else if(e.nodeType||e===i||e===s)n.push(e);else if(Array.isArray(e)){if(e instanceof C)return e;n=e}return new C(function(e){for(var t=[],i=0;i<e.length;i+=1)-1===t.indexOf(e[i])&&t.push(e[i]);return t}(n))}S.fn=C.prototype;var E="resize scroll".split(" ");function k(e){return function(){for(var t=arguments.length,i=new Array(t),s=0;s<t;s++)i[s]=arguments[s];if(void 0===i[0]){for(var n=0;n<this.length;n+=1)E.indexOf(e)<0&&(e in this[n]?this[n][e]():S(this[n]).trigger(e));return this}return this.on.apply(this,[e].concat(i))}}k("click"),k("blur"),k("focus"),k("focusin"),k("focusout"),k("keyup"),k("keydown"),k("keypress"),k("submit"),k("change"),k("mousedown"),k("mousemove"),k("mouseup"),k("mouseenter"),k("mouseleave"),k("mouseout"),k("mouseover"),k("touchstart"),k("touchend"),k("touchmove"),k("resize"),k("scroll");var M={addClass:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=T(t.map((function(e){return e.split(" ")})));return this.forEach((function(e){var t;(t=e.classList).add.apply(t,s)})),this},removeClass:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=T(t.map((function(e){return e.split(" ")})));return this.forEach((function(e){var t;(t=e.classList).remove.apply(t,s)})),this},hasClass:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=T(t.map((function(e){return e.split(" ")})));return x(this,(function(e){return s.filter((function(t){return e.classList.contains(t)})).length>0})).length>0},toggleClass:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=T(t.map((function(e){return e.split(" ")})));this.forEach((function(e){s.forEach((function(t){e.classList.toggle(t)}))}))},attr:function(e,t){if(1===arguments.length&&"string"==typeof e)return this[0]?this[0].getAttribute(e):void 0;for(var i=0;i<this.length;i+=1)if(2===arguments.length)this[i].setAttribute(e,t);else for(var s in e)this[i][s]=e[s],this[i].setAttribute(s,e[s]);return this},removeAttr:function(e){for(var t=0;t<this.length;t+=1)this[t].removeAttribute(e);return this},transform:function(e){for(var t=0;t<this.length;t+=1)this[t].style.transform=e;return this},transition:function(e){for(var t=0;t<this.length;t+=1)this[t].style.transitionDuration="string"!=typeof e?e+"ms":e;return this},on:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=t[0],n=t[1],a=t[2],r=t[3];function o(e){var t=e.target;if(t){var i=e.target.dom7EventData||[];if(i.indexOf(e)<0&&i.unshift(e),S(t).is(n))a.apply(t,i);else for(var s=S(t).parents(),r=0;r<s.length;r+=1)S(s[r]).is(n)&&a.apply(s[r],i)}}function l(e){var t=e&&e.target&&e.target.dom7EventData||[];t.indexOf(e)<0&&t.unshift(e),a.apply(this,t)}"function"==typeof t[1]&&(s=t[0],a=t[1],r=t[2],n=void 0),r||(r=!1);for(var d,c=s.split(" "),p=0;p<this.length;p+=1){var u=this[p];if(n)for(d=0;d<c.length;d+=1){var h=c[d];u.dom7LiveListeners||(u.dom7LiveListeners={}),u.dom7LiveListeners[h]||(u.dom7LiveListeners[h]=[]),u.dom7LiveListeners[h].push({listener:a,proxyListener:o}),u.addEventListener(h,o,r)}else for(d=0;d<c.length;d+=1){var f=c[d];u.dom7Listeners||(u.dom7Listeners={}),u.dom7Listeners[f]||(u.dom7Listeners[f]=[]),u.dom7Listeners[f].push({listener:a,proxyListener:l}),u.addEventListener(f,l,r)}}return this},off:function(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=t[0],n=t[1],a=t[2],r=t[3];"function"==typeof t[1]&&(s=t[0],a=t[1],r=t[2],n=void 0),r||(r=!1);for(var o=s.split(" "),l=0;l<o.length;l+=1)for(var d=o[l],c=0;c<this.length;c+=1){var p=this[c],u=void 0;if(!n&&p.dom7Listeners?u=p.dom7Listeners[d]:n&&p.dom7LiveListeners&&(u=p.dom7LiveListeners[d]),u&&u.length)for(var h=u.length-1;h>=0;h-=1){var f=u[h];a&&f.listener===a||a&&f.listener&&f.listener.dom7proxy&&f.listener.dom7proxy===a?(p.removeEventListener(d,f.proxyListener,r),u.splice(h,1)):a||(p.removeEventListener(d,f.proxyListener,r),u.splice(h,1))}}return this},trigger:function(){for(var e=f(),t=arguments.length,i=new Array(t),s=0;s<t;s++)i[s]=arguments[s];for(var n=i[0].split(" "),a=i[1],r=0;r<n.length;r+=1)for(var o=n[r],l=0;l<this.length;l+=1){var d=this[l];if(e.CustomEvent){var c=new e.CustomEvent(o,{detail:a,bubbles:!0,cancelable:!0});d.dom7EventData=i.filter((function(e,t){return t>0})),d.dispatchEvent(c),d.dom7EventData=[],delete d.dom7EventData}}return this},transitionEnd:function(e){var t=this;return e&&t.on("transitionend",(function i(s){s.target===this&&(e.call(this,s),t.off("transitionend",i))})),this},outerWidth:function(e){if(this.length>0){if(e){var t=this.styles();return this[0].offsetWidth+parseFloat(t.getPropertyValue("margin-right"))+parseFloat(t.getPropertyValue("margin-left"))}return this[0].offsetWidth}return null},outerHeight:function(e){if(this.length>0){if(e){var t=this.styles();return this[0].offsetHeight+parseFloat(t.getPropertyValue("margin-top"))+parseFloat(t.getPropertyValue("margin-bottom"))}return this[0].offsetHeight}return null},styles:function(){var e=f();return this[0]?e.getComputedStyle(this[0],null):{}},offset:function(){if(this.length>0){var e=f(),t=u(),i=this[0],s=i.getBoundingClientRect(),n=t.body,a=i.clientTop||n.clientTop||0,r=i.clientLeft||n.clientLeft||0,o=i===e?e.scrollY:i.scrollTop,l=i===e?e.scrollX:i.scrollLeft;return{top:s.top+o-a,left:s.left+l-r}}return null},css:function(e,t){var i,s=f();if(1===arguments.length){if("string"!=typeof e){for(i=0;i<this.length;i+=1)for(var n in e)this[i].style[n]=e[n];return this}if(this[0])return s.getComputedStyle(this[0],null).getPropertyValue(e)}if(2===arguments.length&&"string"==typeof e){for(i=0;i<this.length;i+=1)this[i].style[e]=t;return this}return this},each:function(e){return e?(this.forEach((function(t,i){e.apply(t,[t,i])})),this):this},html:function(e){if(void 0===e)return this[0]?this[0].innerHTML:null;for(var t=0;t<this.length;t+=1)this[t].innerHTML=e;return this},text:function(e){if(void 0===e)return this[0]?this[0].textContent.trim():null;for(var t=0;t<this.length;t+=1)this[t].textContent=e;return this},is:function(e){var t,i,s=f(),n=u(),a=this[0];if(!a||void 0===e)return!1;if("string"==typeof e){if(a.matches)return a.matches(e);if(a.webkitMatchesSelector)return a.webkitMatchesSelector(e);if(a.msMatchesSelector)return a.msMatchesSelector(e);for(t=S(e),i=0;i<t.length;i+=1)if(t[i]===a)return!0;return!1}if(e===n)return a===n;if(e===s)return a===s;if(e.nodeType||e instanceof C){for(t=e.nodeType?[e]:e,i=0;i<t.length;i+=1)if(t[i]===a)return!0;return!1}return!1},index:function(){var e,t=this[0];if(t){for(e=0;null!==(t=t.previousSibling);)1===t.nodeType&&(e+=1);return e}},eq:function(e){if(void 0===e)return this;var t=this.length;if(e>t-1)return S([]);if(e<0){var i=t+e;return S(i<0?[]:[this[i]])}return S([this[e]])},append:function(){for(var e,t=u(),i=0;i<arguments.length;i+=1){e=i<0||arguments.length<=i?void 0:arguments[i];for(var s=0;s<this.length;s+=1)if("string"==typeof e){var n=t.createElement("div");for(n.innerHTML=e;n.firstChild;)this[s].appendChild(n.firstChild)}else if(e instanceof C)for(var a=0;a<e.length;a+=1)this[s].appendChild(e[a]);else this[s].appendChild(e)}return this},prepend:function(e){var t,i,s=u();for(t=0;t<this.length;t+=1)if("string"==typeof e){var n=s.createElement("div");for(n.innerHTML=e,i=n.childNodes.length-1;i>=0;i-=1)this[t].insertBefore(n.childNodes[i],this[t].childNodes[0])}else if(e instanceof C)for(i=0;i<e.length;i+=1)this[t].insertBefore(e[i],this[t].childNodes[0]);else this[t].insertBefore(e,this[t].childNodes[0]);return this},next:function(e){return this.length>0?e?this[0].nextElementSibling&&S(this[0].nextElementSibling).is(e)?S([this[0].nextElementSibling]):S([]):this[0].nextElementSibling?S([this[0].nextElementSibling]):S([]):S([])},nextAll:function(e){var t=[],i=this[0];if(!i)return S([]);for(;i.nextElementSibling;){var s=i.nextElementSibling;e?S(s).is(e)&&t.push(s):t.push(s),i=s}return S(t)},prev:function(e){if(this.length>0){var t=this[0];return e?t.previousElementSibling&&S(t.previousElementSibling).is(e)?S([t.previousElementSibling]):S([]):t.previousElementSibling?S([t.previousElementSibling]):S([])}return S([])},prevAll:function(e){var t=[],i=this[0];if(!i)return S([]);for(;i.previousElementSibling;){var s=i.previousElementSibling;e?S(s).is(e)&&t.push(s):t.push(s),i=s}return S(t)},parent:function(e){for(var t=[],i=0;i<this.length;i+=1)null!==this[i].parentNode&&(e?S(this[i].parentNode).is(e)&&t.push(this[i].parentNode):t.push(this[i].parentNode));return S(t)},parents:function(e){for(var t=[],i=0;i<this.length;i+=1)for(var s=this[i].parentNode;s;)e?S(s).is(e)&&t.push(s):t.push(s),s=s.parentNode;return S(t)},closest:function(e){var t=this;return void 0===e?S([]):(t.is(e)||(t=t.parents(e).eq(0)),t)},find:function(e){for(var t=[],i=0;i<this.length;i+=1)for(var s=this[i].querySelectorAll(e),n=0;n<s.length;n+=1)t.push(s[n]);return S(t)},children:function(e){for(var t=[],i=0;i<this.length;i+=1)for(var s=this[i].children,n=0;n<s.length;n+=1)e&&!S(s[n]).is(e)||t.push(s[n]);return S(t)},filter:function(e){return S(x(this,e))},remove:function(){for(var e=0;e<this.length;e+=1)this[e].parentNode&&this[e].parentNode.removeChild(this[e]);return this}};Object.keys(M).forEach((function(e){Object.defineProperty(S.fn,e,{value:M[e],writable:!0})}));var P,L,O,z=S;function I(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function B(){return Date.now()}function A(e,t){void 0===t&&(t="x");var i,s,n,a=f(),r=function(e){var t,i=f();return i.getComputedStyle&&(t=i.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((s=r.transform||r.webkitTransform).split(",").length>6&&(s=s.split(", ").map((function(e){return e.replace(",",".")})).join(", ")),n=new a.WebKitCSSMatrix("none"===s?"":s)):i=(n=r.MozTransform||r.OTransform||r.MsTransform||r.msTransform||r.transform||r.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(s=a.WebKitCSSMatrix?n.m41:16===i.length?parseFloat(i[12]):parseFloat(i[4])),"y"===t&&(s=a.WebKitCSSMatrix?n.m42:16===i.length?parseFloat(i[13]):parseFloat(i[5])),s||0}function D(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function N(e){return"undefined"!=typeof window&&void 0!==window.HTMLElement?e instanceof HTMLElement:e&&(1===e.nodeType||11===e.nodeType)}function G(){for(var e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"],i=1;i<arguments.length;i+=1){var s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&!N(s))for(var n=Object.keys(Object(s)).filter((function(e){return t.indexOf(e)<0})),a=0,r=n.length;a<r;a+=1){var o=n[a],l=Object.getOwnPropertyDescriptor(s,o);void 0!==l&&l.enumerable&&(D(e[o])&&D(s[o])?s[o].__swiper__?e[o]=s[o]:G(e[o],s[o]):!D(e[o])&&D(s[o])?(e[o]={},s[o].__swiper__?e[o]=s[o]:G(e[o],s[o])):e[o]=s[o])}}return e}function _(e,t,i){e.style.setProperty(t,i)}function H(e){var t,i=e.swiper,s=e.targetPosition,n=e.side,a=f(),r=-i.translate,o=null,l=i.params.speed;i.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(i.cssModeFrameID);var d=s>r?"next":"prev",c=function(e,t){return"next"===d&&e>=t||"prev"===d&&e<=t};!function e(){var d;t=(new Date).getTime(),null===o&&(o=t);var p=Math.max(Math.min((t-o)/l,1),0),u=.5-Math.cos(p*Math.PI)/2,h=r+u*(s-r);if(c(h,s)&&(h=s),i.wrapperEl.scrollTo(((d={})[n]=h,d)),c(h,s))return i.wrapperEl.style.overflow="hidden",i.wrapperEl.style.scrollSnapType="",setTimeout((function(){var e;i.wrapperEl.style.overflow="",i.wrapperEl.scrollTo(((e={})[n]=h,e))})),void a.cancelAnimationFrame(i.cssModeFrameID);i.cssModeFrameID=a.requestAnimationFrame(e)}()}function j(){return P||(P=function(){var e=f(),t=u();return{smoothScroll:t.documentElement&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch),passiveListener:function(){var t=!1;try{var i=Object.defineProperty({},"passive",{get:function(){t=!0}});e.addEventListener("testPassiveListener",null,i)}catch(e){}return t}(),gestures:"ongesturestart"in e}}()),P}function F(e){return void 0===e&&(e={}),L||(L=function(e){var t=(void 0===e?{}:e).userAgent,i=j(),s=f(),n=s.navigator.platform,a=t||s.navigator.userAgent,r={ios:!1,android:!1},o=s.screen.width,l=s.screen.height,d=a.match(/(Android);?[\s\/]+([\d.]+)?/),c=a.match(/(iPad).*OS\s([\d_]+)/),p=a.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="Win32"===n,v="MacIntel"===n;return!c&&v&&i.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(o+"x"+l)>=0&&((c=a.match(/(Version)\/([\d.]+)/))||(c=[0,1,"13_0_0"]),v=!1),d&&!h&&(r.os="android",r.android=!0),(c||u||p)&&(r.os="ios",r.ios=!0),r}(e)),L}function V(){return O||(O=function(){var e,t=f();return{isSafari:(e=t.navigator.userAgent.toLowerCase(),e.indexOf("safari")>=0&&e.indexOf("chrome")<0&&e.indexOf("android")<0),isWebView:/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(t.navigator.userAgent)}}()),O}function R(e){var t=e.swiper,i=e.runCallbacks,s=e.direction,n=e.step,a=t.activeIndex,r=t.previousIndex,o=s;if(o||(o=a>r?"next":a<r?"prev":"reset"),t.emit("transition"+n),i&&a!==r){if("reset"===o)return void t.emit("slideResetTransition"+n);t.emit("slideChangeTransition"+n),"next"===o?t.emit("slideNextTransition"+n):t.emit("slidePrevTransition"+n)}}function q(e){var t=u(),i=f(),s=this.touchEventsData,n=this.params,a=this.touches;if(this.enabled&&(!this.animating||!n.preventInteractionOnTransition)){!this.animating&&n.cssMode&&n.loop&&this.loopFix();var r=e;r.originalEvent&&(r=r.originalEvent);var o=z(r.target);if(("wrapper"!==n.touchEventsTarget||o.closest(this.wrapperEl).length)&&(s.isTouchEvent="touchstart"===r.type,(s.isTouchEvent||!("which"in r)||3!==r.which)&&!(!s.isTouchEvent&&"button"in r&&r.button>0||s.isTouched&&s.isMoved))){var l=!!n.noSwipingClass&&""!==n.noSwipingClass,d=e.composedPath?e.composedPath():e.path;l&&r.target&&r.target.shadowRoot&&d&&(o=z(d[0]));var c=n.noSwipingSelector?n.noSwipingSelector:"."+n.noSwipingClass,p=!(!r.target||!r.target.shadowRoot);if(n.noSwiping&&(p?function(e,t){return void 0===t&&(t=this),function t(i){if(!i||i===u()||i===f())return null;i.assignedSlot&&(i=i.assignedSlot);var s=i.closest(e);return s||i.getRootNode?s||t(i.getRootNode().host):null}(t)}(c,o[0]):o.closest(c)[0]))this.allowClick=!0;else if(!n.swipeHandler||o.closest(n.swipeHandler)[0]){a.currentX="touchstart"===r.type?r.targetTouches[0].pageX:r.pageX,a.currentY="touchstart"===r.type?r.targetTouches[0].pageY:r.pageY;var h=a.currentX,v=a.currentY,m=n.edgeSwipeDetection||n.iOSEdgeSwipeDetection,g=n.edgeSwipeThreshold||n.iOSEdgeSwipeThreshold;if(m&&(h<=g||h>=i.innerWidth-g)){if("prevent"!==m)return;e.preventDefault()}if(Object.assign(s,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=h,a.startY=v,s.touchStartTime=B(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,n.threshold>0&&(s.allowThresholdMove=!1),"touchstart"!==r.type){var b=!0;o.is(s.focusableElements)&&(b=!1,"SELECT"===o[0].nodeName&&(s.isTouched=!1)),t.activeElement&&z(t.activeElement).is(s.focusableElements)&&t.activeElement!==o[0]&&t.activeElement.blur();var w=b&&this.allowTouchMove&&n.touchStartPreventDefault;!n.touchStartForcePreventDefault&&!w||o[0].isContentEditable||r.preventDefault()}this.params.freeMode&&this.params.freeMode.enabled&&this.freeMode&&this.animating&&!n.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",r)}}}}function W(e){var t=u(),i=this.touchEventsData,s=this.params,n=this.touches,a=this.rtlTranslate;if(this.enabled){var r=e;if(r.originalEvent&&(r=r.originalEvent),i.isTouched){if(!i.isTouchEvent||"touchmove"===r.type){var o="touchmove"===r.type&&r.targetTouches&&(r.targetTouches[0]||r.changedTouches[0]),l="touchmove"===r.type?o.pageX:r.pageX,d="touchmove"===r.type?o.pageY:r.pageY;if(r.preventedByNestedSwiper)return n.startX=l,void(n.startY=d);if(!this.allowTouchMove)return z(r.target).is(i.focusableElements)||(this.allowClick=!1),void(i.isTouched&&(Object.assign(n,{startX:l,startY:d,currentX:l,currentY:d}),i.touchStartTime=B()));if(i.isTouchEvent&&s.touchReleaseOnEdges&&!s.loop)if(this.isVertical()){if(d<n.startY&&this.translate<=this.maxTranslate()||d>n.startY&&this.translate>=this.minTranslate())return i.isTouched=!1,void(i.isMoved=!1)}else if(l<n.startX&&this.translate<=this.maxTranslate()||l>n.startX&&this.translate>=this.minTranslate())return;if(i.isTouchEvent&&t.activeElement&&r.target===t.activeElement&&z(r.target).is(i.focusableElements))return i.isMoved=!0,void(this.allowClick=!1);if(i.allowTouchCallbacks&&this.emit("touchMove",r),!(r.targetTouches&&r.targetTouches.length>1)){n.currentX=l,n.currentY=d;var c=n.currentX-n.startX,p=n.currentY-n.startY;if(!(this.params.threshold&&Math.sqrt(Math.pow(c,2)+Math.pow(p,2))<this.params.threshold)){var h;if(void 0===i.isScrolling)this.isHorizontal()&&n.currentY===n.startY||this.isVertical()&&n.currentX===n.startX?i.isScrolling=!1:c*c+p*p>=25&&(h=180*Math.atan2(Math.abs(p),Math.abs(c))/Math.PI,i.isScrolling=this.isHorizontal()?h>s.touchAngle:90-h>s.touchAngle);if(i.isScrolling&&this.emit("touchMoveOpposite",r),void 0===i.startMoving&&(n.currentX===n.startX&&n.currentY===n.startY||(i.startMoving=!0)),i.isScrolling)i.isTouched=!1;else if(i.startMoving){this.allowClick=!1,!s.cssMode&&r.cancelable&&r.preventDefault(),s.touchMoveStopPropagation&&!s.nested&&r.stopPropagation(),i.isMoved||(s.loop&&!s.cssMode&&this.loopFix(),i.startTranslate=this.getTranslate(),this.setTransition(0),this.animating&&this.$wrapperEl.trigger("webkitTransitionEnd transitionend"),i.allowMomentumBounce=!1,!s.grabCursor||!0!==this.allowSlideNext&&!0!==this.allowSlidePrev||this.setGrabCursor(!0),this.emit("sliderFirstMove",r)),this.emit("sliderMove",r),i.isMoved=!0;var f=this.isHorizontal()?c:p;n.diff=f,f*=s.touchRatio,a&&(f=-f),this.swipeDirection=f>0?"prev":"next",i.currentTranslate=f+i.startTranslate;var v=!0,m=s.resistanceRatio;if(s.touchReleaseOnEdges&&(m=0),f>0&&i.currentTranslate>this.minTranslate()?(v=!1,s.resistance&&(i.currentTranslate=this.minTranslate()-1+Math.pow(-this.minTranslate()+i.startTranslate+f,m))):f<0&&i.currentTranslate<this.maxTranslate()&&(v=!1,s.resistance&&(i.currentTranslate=this.maxTranslate()+1-Math.pow(this.maxTranslate()-i.startTranslate-f,m))),v&&(r.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&i.currentTranslate<i.startTranslate&&(i.currentTranslate=i.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&i.currentTranslate>i.startTranslate&&(i.currentTranslate=i.startTranslate),this.allowSlidePrev||this.allowSlideNext||(i.currentTranslate=i.startTranslate),s.threshold>0){if(!(Math.abs(f)>s.threshold||i.allowThresholdMove))return void(i.currentTranslate=i.startTranslate);if(!i.allowThresholdMove)return i.allowThresholdMove=!0,n.startX=n.currentX,n.startY=n.currentY,i.currentTranslate=i.startTranslate,void(n.diff=this.isHorizontal()?n.currentX-n.startX:n.currentY-n.startY)}s.followFinger&&!s.cssMode&&((s.freeMode&&s.freeMode.enabled&&this.freeMode||s.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),this.params.freeMode&&s.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(i.currentTranslate),this.setTranslate(i.currentTranslate))}}}}}else i.startMoving&&i.isScrolling&&this.emit("touchMoveOpposite",r)}}function Y(e){var t=this,i=t.touchEventsData,s=t.params,n=t.touches,a=t.rtlTranslate,r=t.slidesGrid;if(t.enabled){var o=e;if(o.originalEvent&&(o=o.originalEvent),i.allowTouchCallbacks&&t.emit("touchEnd",o),i.allowTouchCallbacks=!1,!i.isTouched)return i.isMoved&&s.grabCursor&&t.setGrabCursor(!1),i.isMoved=!1,void(i.startMoving=!1);s.grabCursor&&i.isMoved&&i.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);var l,d=B(),c=d-i.touchStartTime;if(t.allowClick){var p=o.path||o.composedPath&&o.composedPath();t.updateClickedSlide(p&&p[0]||o.target),t.emit("tap click",o),c<300&&d-i.lastClickTime<300&&t.emit("doubleTap doubleClick",o)}if(i.lastClickTime=B(),I((function(){t.destroyed||(t.allowClick=!0)})),!i.isTouched||!i.isMoved||!t.swipeDirection||0===n.diff||i.currentTranslate===i.startTranslate)return i.isTouched=!1,i.isMoved=!1,void(i.startMoving=!1);if(i.isTouched=!1,i.isMoved=!1,i.startMoving=!1,l=s.followFinger?a?t.translate:-t.translate:-i.currentTranslate,!s.cssMode)if(t.params.freeMode&&s.freeMode.enabled)t.freeMode.onTouchEnd({currentPos:l});else{for(var u=0,h=t.slidesSizesGrid[0],f=0;f<r.length;f+=f<s.slidesPerGroupSkip?1:s.slidesPerGroup){var v=f<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;void 0!==r[f+v]?l>=r[f]&&l<r[f+v]&&(u=f,h=r[f+v]-r[f]):l>=r[f]&&(u=f,h=r[r.length-1]-r[r.length-2])}var m=null,g=null;s.rewind&&(t.isBeginning?g=t.params.virtual&&t.params.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(m=0));var b=(l-r[u])/h,w=u<s.slidesPerGroupSkip-1?1:s.slidesPerGroup;if(c>s.longSwipesMs){if(!s.longSwipes)return void t.slideTo(t.activeIndex);"next"===t.swipeDirection&&(b>=s.longSwipesRatio?t.slideTo(s.rewind&&t.isEnd?m:u+w):t.slideTo(u)),"prev"===t.swipeDirection&&(b>1-s.longSwipesRatio?t.slideTo(u+w):null!==g&&b<0&&Math.abs(b)>s.longSwipesRatio?t.slideTo(g):t.slideTo(u))}else{if(!s.shortSwipes)return void t.slideTo(t.activeIndex);t.navigation&&(o.target===t.navigation.nextEl||o.target===t.navigation.prevEl)?o.target===t.navigation.nextEl?t.slideTo(u+w):t.slideTo(u):("next"===t.swipeDirection&&t.slideTo(null!==m?m:u+w),"prev"===t.swipeDirection&&t.slideTo(null!==g?g:u))}}}}function X(){var e=this.params,t=this.el;if(!t||0!==t.offsetWidth){e.breakpoints&&this.setBreakpoint();var i=this.allowSlideNext,s=this.allowSlidePrev,n=this.snapGrid;this.allowSlideNext=!0,this.allowSlidePrev=!0,this.updateSize(),this.updateSlides(),this.updateSlidesClasses(),("auto"===e.slidesPerView||e.slidesPerView>1)&&this.isEnd&&!this.isBeginning&&!this.params.centeredSlides?this.slideTo(this.slides.length-1,0,!1,!0):this.slideTo(this.activeIndex,0,!1,!0),this.autoplay&&this.autoplay.running&&this.autoplay.paused&&this.autoplay.run(),this.allowSlidePrev=s,this.allowSlideNext=i,this.params.watchOverflow&&n!==this.snapGrid&&this.checkOverflow()}}function U(e){this.enabled&&(this.allowClick||(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation())))}function Z(){var e=this.wrapperEl,t=this.rtlTranslate;if(this.enabled){this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-e.scrollLeft:this.translate=-e.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();var i=this.maxTranslate()-this.minTranslate();(0===i?0:(this.translate-this.minTranslate())/i)!==this.progress&&this.updateProgress(t?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}}var K=!1;function Q(){}var J=function(e,t){var i=u(),s=e.params,n=e.touchEvents,a=e.el,r=e.wrapperEl,o=e.device,l=e.support,d=!!s.nested,c="on"===t?"addEventListener":"removeEventListener",p=t;if(l.touch){var h=!("touchstart"!==n.start||!l.passiveListener||!s.passiveListeners)&&{passive:!0,capture:!1};a[c](n.start,e.onTouchStart,h),a[c](n.move,e.onTouchMove,l.passiveListener?{passive:!1,capture:d}:d),a[c](n.end,e.onTouchEnd,h),n.cancel&&a[c](n.cancel,e.onTouchEnd,h)}else a[c](n.start,e.onTouchStart,!1),i[c](n.move,e.onTouchMove,d),i[c](n.end,e.onTouchEnd,!1);(s.preventClicks||s.preventClicksPropagation)&&a[c]("click",e.onClick,!0),s.cssMode&&r[c]("scroll",e.onScroll),s.updateOnWindowResize?e[p](o.ios||o.android?"resize orientationchange observerUpdate":"resize observerUpdate",X,!0):e[p]("observerUpdate",X,!0)};var ee=function(e,t){return e.grid&&t.grid&&t.grid.rows>1};var te={init:!0,direction:"horizontal",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:0,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,preloadImages:!0,updateOnImagesReady:!0,loop:!1,loopAdditionalSlides:0,loopedSlides:null,loopedSlidesLimit:!0,loopFillGroupWithBlank:!1,loopPreventsSlide:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-invisible-blank",slideActiveClass:"swiper-slide-active",slideDuplicateActiveClass:"swiper-slide-duplicate-active",slideVisibleClass:"swiper-slide-visible",slideDuplicateClass:"swiper-slide-duplicate",slideNextClass:"swiper-slide-next",slideDuplicateNextClass:"swiper-slide-duplicate-next",slidePrevClass:"swiper-slide-prev",slideDuplicatePrevClass:"swiper-slide-duplicate-prev",wrapperClass:"swiper-wrapper",runCallbacksOnInit:!0,_emitClasses:!1};function ie(e,t){return function(i){void 0===i&&(i={});var s=Object.keys(i)[0],n=i[s];"object"==typeof n&&null!==n?(["navigation","pagination","scrollbar"].indexOf(s)>=0&&!0===e[s]&&(e[s]={auto:!0}),s in e&&"enabled"in n?(!0===e[s]&&(e[s]={enabled:!0}),"object"!=typeof e[s]||"enabled"in e[s]||(e[s].enabled=!0),e[s]||(e[s]={enabled:!1}),G(t,i)):G(t,i)):G(t,i)}}var se={eventsEmitter:{on:function(e,t,i){var s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;var n=i?"unshift":"push";return e.split(" ").forEach((function(e){s.eventsListeners[e]||(s.eventsListeners[e]=[]),s.eventsListeners[e][n](t)})),s},once:function(e,t,i){var s=this;if(!s.eventsListeners||s.destroyed)return s;if("function"!=typeof t)return s;function n(){s.off(e,n),n.__emitterProxy&&delete n.__emitterProxy;for(var i=arguments.length,a=new Array(i),r=0;r<i;r++)a[r]=arguments[r];t.apply(s,a)}return n.__emitterProxy=t,s.on(e,n,i)},onAny:function(e,t){if(!this.eventsListeners||this.destroyed)return this;if("function"!=typeof e)return this;var i=t?"unshift":"push";return this.eventsAnyListeners.indexOf(e)<0&&this.eventsAnyListeners[i](e),this},offAny:function(e){if(!this.eventsListeners||this.destroyed)return this;if(!this.eventsAnyListeners)return this;var t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off:function(e,t){var i=this;return!i.eventsListeners||i.destroyed?i:i.eventsListeners?(e.split(" ").forEach((function(e){void 0===t?i.eventsListeners[e]=[]:i.eventsListeners[e]&&i.eventsListeners[e].forEach((function(s,n){(s===t||s.__emitterProxy&&s.__emitterProxy===t)&&i.eventsListeners[e].splice(n,1)}))})),i):i},emit:function(){var e,t,i,s=this;if(!s.eventsListeners||s.destroyed)return s;if(!s.eventsListeners)return s;for(var n=arguments.length,a=new Array(n),r=0;r<n;r++)a[r]=arguments[r];"string"==typeof a[0]||Array.isArray(a[0])?(e=a[0],t=a.slice(1,a.length),i=s):(e=a[0].events,t=a[0].data,i=a[0].context||s),t.unshift(i);var o=Array.isArray(e)?e:e.split(" ");return o.forEach((function(e){s.eventsAnyListeners&&s.eventsAnyListeners.length&&s.eventsAnyListeners.forEach((function(s){s.apply(i,[e].concat(t))})),s.eventsListeners&&s.eventsListeners[e]&&s.eventsListeners[e].forEach((function(e){e.apply(i,t)}))})),s}},update:{updateSize:function(){var e,t,i=this.$el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:i[0].clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:i[0].clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt(i.css("padding-left")||0,10)-parseInt(i.css("padding-right")||0,10),t=t-parseInt(i.css("padding-top")||0,10)-parseInt(i.css("padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){var e=this;function t(t){return e.isHorizontal()?t:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[t]}function i(e,i){return parseFloat(e.getPropertyValue(t(i))||0)}var s=e.params,n=e.$wrapperEl,a=e.size,r=e.rtlTranslate,o=e.wrongRTL,l=e.virtual&&s.virtual.enabled,d=l?e.virtual.slides.length:e.slides.length,c=n.children("."+e.params.slideClass),p=l?e.virtual.slides.length:c.length,u=[],h=[],f=[],v=s.slidesOffsetBefore;"function"==typeof v&&(v=s.slidesOffsetBefore.call(e));var m=s.slidesOffsetAfter;"function"==typeof m&&(m=s.slidesOffsetAfter.call(e));var g=e.snapGrid.length,b=e.slidesGrid.length,w=s.spaceBetween,y=-v,C=0,T=0;if(void 0!==a){"string"==typeof w&&w.indexOf("%")>=0&&(w=parseFloat(w.replace("%",""))/100*a),e.virtualSize=-w,r?c.css({marginLeft:"",marginBottom:"",marginTop:""}):c.css({marginRight:"",marginBottom:"",marginTop:""}),s.centeredSlides&&s.cssMode&&(_(e.wrapperEl,"--swiper-centered-offset-before",""),_(e.wrapperEl,"--swiper-centered-offset-after",""));var x,S=s.grid&&s.grid.rows>1&&e.grid;S&&e.grid.initSlides(p);for(var E,k="auto"===s.slidesPerView&&s.breakpoints&&Object.keys(s.breakpoints).filter((function(e){return void 0!==s.breakpoints[e].slidesPerView})).length>0,M=0;M<p;M+=1){x=0;var P=c.eq(M);if(S&&e.grid.updateSlide(M,P,p,t),"none"!==P.css("display")){if("auto"===s.slidesPerView){k&&(c[M].style[t("width")]="");var $=getComputedStyle(P[0]),L=P[0].style.transform,O=P[0].style.webkitTransform;if(L&&(P[0].style.transform="none"),O&&(P[0].style.webkitTransform="none"),s.roundLengths)x=e.isHorizontal()?P.outerWidth(!0):P.outerHeight(!0);else{var z=i($,"width"),I=i($,"padding-left"),B=i($,"padding-right"),A=i($,"margin-left"),D=i($,"margin-right"),N=$.getPropertyValue("box-sizing");if(N&&"border-box"===N)x=z+A+D;else{var G=P[0],H=G.clientWidth;x=z+I+B+A+D+(G.offsetWidth-H)}}L&&(P[0].style.transform=L),O&&(P[0].style.webkitTransform=O),s.roundLengths&&(x=Math.floor(x))}else x=(a-(s.slidesPerView-1)*w)/s.slidesPerView,s.roundLengths&&(x=Math.floor(x)),c[M]&&(c[M].style[t("width")]=x+"px");c[M]&&(c[M].swiperSlideSize=x),f.push(x),s.centeredSlides?(y=y+x/2+C/2+w,0===C&&0!==M&&(y=y-a/2-w),0===M&&(y=y-a/2-w),Math.abs(y)<.001&&(y=0),s.roundLengths&&(y=Math.floor(y)),T%s.slidesPerGroup==0&&u.push(y),h.push(y)):(s.roundLengths&&(y=Math.floor(y)),(T-Math.min(e.params.slidesPerGroupSkip,T))%e.params.slidesPerGroup==0&&u.push(y),h.push(y),y=y+x+w),e.virtualSize+=x+w,C=x,T+=1}}if(e.virtualSize=Math.max(e.virtualSize,a)+m,r&&o&&("slide"===s.effect||"coverflow"===s.effect)&&n.css({width:e.virtualSize+s.spaceBetween+"px"}),s.setWrapperSize)n.css(((E={})[t("width")]=e.virtualSize+s.spaceBetween+"px",E));if(S&&e.grid.updateWrapperSize(x,u,t),!s.centeredSlides){for(var j=[],F=0;F<u.length;F+=1){var V=u[F];s.roundLengths&&(V=Math.floor(V)),u[F]<=e.virtualSize-a&&j.push(V)}u=j,Math.floor(e.virtualSize-a)-Math.floor(u[u.length-1])>1&&u.push(e.virtualSize-a)}if(0===u.length&&(u=[0]),0!==s.spaceBetween){var R,q=e.isHorizontal()&&r?"marginLeft":t("marginRight");c.filter((function(e,t){return!s.cssMode||t!==c.length-1})).css(((R={})[q]=w+"px",R))}if(s.centeredSlides&&s.centeredSlidesBounds){var W=0;f.forEach((function(e){W+=e+(s.spaceBetween?s.spaceBetween:0)}));var Y=(W-=s.spaceBetween)-a;u=u.map((function(e){return e<0?-v:e>Y?Y+m:e}))}if(s.centerInsufficientSlides){var X=0;if(f.forEach((function(e){X+=e+(s.spaceBetween?s.spaceBetween:0)})),(X-=s.spaceBetween)<a){var U=(a-X)/2;u.forEach((function(e,t){u[t]=e-U})),h.forEach((function(e,t){h[t]=e+U}))}}if(Object.assign(e,{slides:c,snapGrid:u,slidesGrid:h,slidesSizesGrid:f}),s.centeredSlides&&s.cssMode&&!s.centeredSlidesBounds){_(e.wrapperEl,"--swiper-centered-offset-before",-u[0]+"px"),_(e.wrapperEl,"--swiper-centered-offset-after",e.size/2-f[f.length-1]/2+"px");var Z=-e.snapGrid[0],K=-e.slidesGrid[0];e.snapGrid=e.snapGrid.map((function(e){return e+Z})),e.slidesGrid=e.slidesGrid.map((function(e){return e+K}))}if(p!==d&&e.emit("slidesLengthChange"),u.length!==g&&(e.params.watchOverflow&&e.checkOverflow(),e.emit("snapGridLengthChange")),h.length!==b&&e.emit("slidesGridLengthChange"),s.watchSlidesProgress&&e.updateSlidesOffset(),!(l||s.cssMode||"slide"!==s.effect&&"fade"!==s.effect)){var Q=s.containerModifierClass+"backface-hidden",J=e.$el.hasClass(Q);p<=s.maxBackfaceHiddenSlides?J||e.$el.addClass(Q):J&&e.$el.removeClass(Q)}}},updateAutoHeight:function(e){var t,i=this,s=[],n=i.virtual&&i.params.virtual.enabled,a=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);var r=function(e){return n?i.slides.filter((function(t){return parseInt(t.getAttribute("data-swiper-slide-index"),10)===e}))[0]:i.slides.eq(e)[0]};if("auto"!==i.params.slidesPerView&&i.params.slidesPerView>1)if(i.params.centeredSlides)(i.visibleSlides||z([])).each((function(e){s.push(e)}));else for(t=0;t<Math.ceil(i.params.slidesPerView);t+=1){var o=i.activeIndex+t;if(o>i.slides.length&&!n)break;s.push(r(o))}else s.push(r(i.activeIndex));for(t=0;t<s.length;t+=1)if(void 0!==s[t]){var l=s[t].offsetHeight;a=l>a?l:a}(a||0===a)&&i.$wrapperEl.css("height",a+"px")},updateSlidesOffset:function(){for(var e=this.slides,t=0;t<e.length;t+=1)e[t].swiperSlideOffset=this.isHorizontal()?e[t].offsetLeft:e[t].offsetTop},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var t=this.params,i=this.slides,s=this.rtlTranslate,n=this.snapGrid;if(0!==i.length){void 0===i[0].swiperSlideOffset&&this.updateSlidesOffset();var a=-e;s&&(a=e),i.removeClass(t.slideVisibleClass),this.visibleSlidesIndexes=[],this.visibleSlides=[];for(var r=0;r<i.length;r+=1){var o=i[r],l=o.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(l-=i[0].swiperSlideOffset);var d=(a+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+t.spaceBetween),c=(a-n[0]+(t.centeredSlides?this.minTranslate():0)-l)/(o.swiperSlideSize+t.spaceBetween),p=-(a-l),u=p+this.slidesSizesGrid[r];(p>=0&&p<this.size-1||u>1&&u<=this.size||p<=0&&u>=this.size)&&(this.visibleSlides.push(o),this.visibleSlidesIndexes.push(r),i.eq(r).addClass(t.slideVisibleClass)),o.progress=s?-d:d,o.originalProgress=s?-c:c}this.visibleSlides=z(this.visibleSlides)}},updateProgress:function(e){if(void 0===e){var t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}var i=this.params,s=this.maxTranslate()-this.minTranslate(),n=this.progress,a=this.isBeginning,r=this.isEnd,o=a,l=r;0===s?(n=0,a=!0,r=!0):(a=(n=(e-this.minTranslate())/s)<=0,r=n>=1),Object.assign(this,{progress:n,isBeginning:a,isEnd:r}),(i.watchSlidesProgress||i.centeredSlides&&i.autoHeight)&&this.updateSlidesProgress(e),a&&!o&&this.emit("reachBeginning toEdge"),r&&!l&&this.emit("reachEnd toEdge"),(o&&!a||l&&!r)&&this.emit("fromEdge"),this.emit("progress",n)},updateSlidesClasses:function(){var e,t=this.slides,i=this.params,s=this.$wrapperEl,n=this.activeIndex,a=this.realIndex,r=this.virtual&&i.virtual.enabled;t.removeClass(i.slideActiveClass+" "+i.slideNextClass+" "+i.slidePrevClass+" "+i.slideDuplicateActiveClass+" "+i.slideDuplicateNextClass+" "+i.slideDuplicatePrevClass),(e=r?this.$wrapperEl.find("."+i.slideClass+'[data-swiper-slide-index="'+n+'"]'):t.eq(n)).addClass(i.slideActiveClass),i.loop&&(e.hasClass(i.slideDuplicateClass)?s.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+a+'"]').addClass(i.slideDuplicateActiveClass):s.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+a+'"]').addClass(i.slideDuplicateActiveClass));var o=e.nextAll("."+i.slideClass).eq(0).addClass(i.slideNextClass);i.loop&&0===o.length&&(o=t.eq(0)).addClass(i.slideNextClass);var l=e.prevAll("."+i.slideClass).eq(0).addClass(i.slidePrevClass);i.loop&&0===l.length&&(l=t.eq(-1)).addClass(i.slidePrevClass),i.loop&&(o.hasClass(i.slideDuplicateClass)?s.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+o.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass):s.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+o.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicateNextClass),l.hasClass(i.slideDuplicateClass)?s.children("."+i.slideClass+":not(."+i.slideDuplicateClass+')[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass):s.children("."+i.slideClass+"."+i.slideDuplicateClass+'[data-swiper-slide-index="'+l.attr("data-swiper-slide-index")+'"]').addClass(i.slideDuplicatePrevClass)),this.emitSlidesClasses()},updateActiveIndex:function(e){var t,i=this.rtlTranslate?this.translate:-this.translate,s=this.slidesGrid,n=this.snapGrid,a=this.params,r=this.activeIndex,o=this.realIndex,l=this.snapIndex,d=e;if(void 0===d){for(var c=0;c<s.length;c+=1)void 0!==s[c+1]?i>=s[c]&&i<s[c+1]-(s[c+1]-s[c])/2?d=c:i>=s[c]&&i<s[c+1]&&(d=c+1):i>=s[c]&&(d=c);a.normalizeSlideIndex&&(d<0||void 0===d)&&(d=0)}if(n.indexOf(i)>=0)t=n.indexOf(i);else{var p=Math.min(a.slidesPerGroupSkip,d);t=p+Math.floor((d-p)/a.slidesPerGroup)}if(t>=n.length&&(t=n.length-1),d!==r){var u=parseInt(this.slides.eq(d).attr("data-swiper-slide-index")||d,10);Object.assign(this,{snapIndex:t,realIndex:u,previousIndex:r,activeIndex:d}),this.emit("activeIndexChange"),this.emit("snapIndexChange"),o!==u&&this.emit("realIndexChange"),(this.initialized||this.params.runCallbacksOnInit)&&this.emit("slideChange")}else t!==l&&(this.snapIndex=t,this.emit("snapIndexChange"))},updateClickedSlide:function(e){var t,i=this.params,s=z(e).closest("."+i.slideClass)[0],n=!1;if(s)for(var a=0;a<this.slides.length;a+=1)if(this.slides[a]===s){n=!0,t=a;break}if(!s||!n)return this.clickedSlide=void 0,void(this.clickedIndex=void 0);this.clickedSlide=s,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(z(s).attr("data-swiper-slide-index"),10):this.clickedIndex=t,i.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this.params,i=this.rtlTranslate,s=this.translate,n=this.$wrapperEl;if(t.virtualTranslate)return i?-s:s;if(t.cssMode)return s;var a=A(n[0],e);return i&&(a=-a),a||0},setTranslate:function(e,t){var i=this.rtlTranslate,s=this.params,n=this.$wrapperEl,a=this.wrapperEl,r=this.progress,o=0,l=0;this.isHorizontal()?o=i?-e:e:l=e,s.roundLengths&&(o=Math.floor(o),l=Math.floor(l)),s.cssMode?a[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-o:-l:s.virtualTranslate||n.transform("translate3d("+o+"px, "+l+"px, 0px)"),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?o:l;var d=this.maxTranslate()-this.minTranslate();(0===d?0:(e-this.minTranslate())/d)!==r&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,i,s,n){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),void 0===s&&(s=!0);var a=this,r=a.params,o=a.wrapperEl;if(a.animating&&r.preventInteractionOnTransition)return!1;var l,d=a.minTranslate(),c=a.maxTranslate();if(l=s&&e>d?d:s&&e<c?c:e,a.updateProgress(l),r.cssMode){var p=a.isHorizontal();if(0===t)o[p?"scrollLeft":"scrollTop"]=-l;else{var u;if(!a.support.smoothScroll)return H({swiper:a,targetPosition:-l,side:p?"left":"top"}),!0;o.scrollTo(((u={})[p?"left":"top"]=-l,u.behavior="smooth",u))}return!0}return 0===t?(a.setTransition(0),a.setTranslate(l),i&&(a.emit("beforeTransitionStart",t,n),a.emit("transitionEnd"))):(a.setTransition(t),a.setTranslate(l),i&&(a.emit("beforeTransitionStart",t,n),a.emit("transitionStart")),a.animating||(a.animating=!0,a.onTranslateToWrapperTransitionEnd||(a.onTranslateToWrapperTransitionEnd=function(e){a&&!a.destroyed&&e.target===this&&(a.$wrapperEl[0].removeEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.$wrapperEl[0].removeEventListener("webkitTransitionEnd",a.onTranslateToWrapperTransitionEnd),a.onTranslateToWrapperTransitionEnd=null,delete a.onTranslateToWrapperTransitionEnd,i&&a.emit("transitionEnd"))}),a.$wrapperEl[0].addEventListener("transitionend",a.onTranslateToWrapperTransitionEnd),a.$wrapperEl[0].addEventListener("webkitTransitionEnd",a.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||this.$wrapperEl.transition(e),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var i=this.params;i.cssMode||(i.autoHeight&&this.updateAutoHeight(),R({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);var i=this.params;this.animating=!1,i.cssMode||(this.setTransition(0),R({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,i,s,n){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"number"!=typeof e&&"string"!=typeof e)throw new Error("The 'index' argument cannot have type other than 'number' or 'string'. ["+typeof e+"] given.");if("string"==typeof e){var a=parseInt(e,10);if(!isFinite(a))throw new Error("The passed-in 'index' (string) couldn't be converted to 'number'. ["+e+"] given.");e=a}var r=this,o=e;o<0&&(o=0);var l=r.params,d=r.snapGrid,c=r.slidesGrid,p=r.previousIndex,u=r.activeIndex,h=r.rtlTranslate,f=r.wrapperEl,v=r.enabled;if(r.animating&&l.preventInteractionOnTransition||!v&&!s&&!n)return!1;var m=Math.min(r.params.slidesPerGroupSkip,o),g=m+Math.floor((o-m)/r.params.slidesPerGroup);g>=d.length&&(g=d.length-1);var b,w=-d[g];if(l.normalizeSlideIndex)for(var y=0;y<c.length;y+=1){var C=-Math.floor(100*w),T=Math.floor(100*c[y]),x=Math.floor(100*c[y+1]);void 0!==c[y+1]?C>=T&&C<x-(x-T)/2?o=y:C>=T&&C<x&&(o=y+1):C>=T&&(o=y)}if(r.initialized&&o!==u){if(!r.allowSlideNext&&w<r.translate&&w<r.minTranslate())return!1;if(!r.allowSlidePrev&&w>r.translate&&w>r.maxTranslate()&&(u||0)!==o)return!1}if(o!==(p||0)&&i&&r.emit("beforeSlideChangeStart"),r.updateProgress(w),b=o>u?"next":o<u?"prev":"reset",h&&-w===r.translate||!h&&w===r.translate)return r.updateActiveIndex(o),l.autoHeight&&r.updateAutoHeight(),r.updateSlidesClasses(),"slide"!==l.effect&&r.setTranslate(w),"reset"!==b&&(r.transitionStart(i,b),r.transitionEnd(i,b)),!1;if(l.cssMode){var S=r.isHorizontal(),E=h?w:-w;if(0===t){var k=r.virtual&&r.params.virtual.enabled;k&&(r.wrapperEl.style.scrollSnapType="none",r._immediateVirtual=!0),f[S?"scrollLeft":"scrollTop"]=E,k&&requestAnimationFrame((function(){r.wrapperEl.style.scrollSnapType="",r._swiperImmediateVirtual=!1}))}else{var M;if(!r.support.smoothScroll)return H({swiper:r,targetPosition:E,side:S?"left":"top"}),!0;f.scrollTo(((M={})[S?"left":"top"]=E,M.behavior="smooth",M))}return!0}return r.setTransition(t),r.setTranslate(w),r.updateActiveIndex(o),r.updateSlidesClasses(),r.emit("beforeTransitionStart",t,s),r.transitionStart(i,b),0===t?r.transitionEnd(i,b):r.animating||(r.animating=!0,r.onSlideToWrapperTransitionEnd||(r.onSlideToWrapperTransitionEnd=function(e){r&&!r.destroyed&&e.target===this&&(r.$wrapperEl[0].removeEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].removeEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd),r.onSlideToWrapperTransitionEnd=null,delete r.onSlideToWrapperTransitionEnd,r.transitionEnd(i,b))}),r.$wrapperEl[0].addEventListener("transitionend",r.onSlideToWrapperTransitionEnd),r.$wrapperEl[0].addEventListener("webkitTransitionEnd",r.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,i,s){if(void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===i&&(i=!0),"string"==typeof e){var n=parseInt(e,10);if(!isFinite(n))throw new Error("The passed-in 'index' (string) couldn't be converted to 'number'. ["+e+"] given.");e=n}var a=e;return this.params.loop&&(a+=this.loopedSlides),this.slideTo(a,t,i,s)},slideNext:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var s=this.animating,n=this.enabled,a=this.params;if(!n)return this;var r=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(r=Math.max(this.slidesPerViewDynamic("current",!0),1));var o=this.activeIndex<a.slidesPerGroupSkip?1:r;if(a.loop){if(s&&a.loopPreventsSlide)return!1;this.loopFix(),this._clientLeft=this.$wrapperEl[0].clientLeft}return a.rewind&&this.isEnd?this.slideTo(0,e,t,i):this.slideTo(this.activeIndex+o,e,t,i)},slidePrev:function(e,t,i){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0);var s=this.params,n=this.animating,a=this.snapGrid,r=this.slidesGrid,o=this.rtlTranslate;if(!this.enabled)return this;if(s.loop){if(n&&s.loopPreventsSlide)return!1;this.loopFix(),this._clientLeft=this.$wrapperEl[0].clientLeft}function l(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var d,c=l(o?this.translate:-this.translate),p=a.map((function(e){return l(e)})),u=a[p.indexOf(c)-1];void 0===u&&s.cssMode&&(a.forEach((function(e,t){c>=e&&(d=t)})),void 0!==d&&(u=a[d>0?d-1:d]));var h=0;if(void 0!==u&&((h=r.indexOf(u))<0&&(h=this.activeIndex-1),"auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(h=h-this.slidesPerViewDynamic("previous",!0)+1,h=Math.max(h,0))),s.rewind&&this.isBeginning){var f=this.params.virtual&&this.params.virtual.enabled&&this.virtual?this.virtual.slides.length-1:this.slides.length-1;return this.slideTo(f,e,t,i)}return this.slideTo(h,e,t,i)},slideReset:function(e,t,i){return void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),this.slideTo(this.activeIndex,e,t,i)},slideToClosest:function(e,t,i,s){void 0===e&&(e=this.params.speed),void 0===t&&(t=!0),void 0===s&&(s=.5);var n=this.activeIndex,a=Math.min(this.params.slidesPerGroupSkip,n),r=a+Math.floor((n-a)/this.params.slidesPerGroup),o=this.rtlTranslate?this.translate:-this.translate;if(o>=this.snapGrid[r]){var l=this.snapGrid[r];o-l>(this.snapGrid[r+1]-l)*s&&(n+=this.params.slidesPerGroup)}else{var d=this.snapGrid[r-1];o-d<=(this.snapGrid[r]-d)*s&&(n-=this.params.slidesPerGroup)}return n=Math.max(n,0),n=Math.min(n,this.slidesGrid.length-1),this.slideTo(n,e,t,i)},slideToClickedSlide:function(){var e,t=this,i=t.params,s=t.$wrapperEl,n="auto"===i.slidesPerView?t.slidesPerViewDynamic():i.slidesPerView,a=t.clickedIndex;if(i.loop){if(t.animating)return;e=parseInt(z(t.clickedSlide).attr("data-swiper-slide-index"),10),i.centeredSlides?a<t.loopedSlides-n/2||a>t.slides.length-t.loopedSlides+n/2?(t.loopFix(),a=s.children("."+i.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+i.slideDuplicateClass+")").eq(0).index(),I((function(){t.slideTo(a)}))):t.slideTo(a):a>t.slides.length-n?(t.loopFix(),a=s.children("."+i.slideClass+'[data-swiper-slide-index="'+e+'"]:not(.'+i.slideDuplicateClass+")").eq(0).index(),I((function(){t.slideTo(a)}))):t.slideTo(a)}else t.slideTo(a)}},loop:{loopCreate:function(){var e=u(),t=this.params,i=this.$wrapperEl,s=i.children().length>0?z(i.children()[0].parentNode):i;s.children("."+t.slideClass+"."+t.slideDuplicateClass).remove();var n=s.children("."+t.slideClass);if(t.loopFillGroupWithBlank){var a=t.slidesPerGroup-n.length%t.slidesPerGroup;if(a!==t.slidesPerGroup){for(var r=0;r<a;r+=1){var o=z(e.createElement("div")).addClass(t.slideClass+" "+t.slideBlankClass);s.append(o)}n=s.children("."+t.slideClass)}}"auto"!==t.slidesPerView||t.loopedSlides||(t.loopedSlides=n.length),this.loopedSlides=Math.ceil(parseFloat(t.loopedSlides||t.slidesPerView,10)),this.loopedSlides+=t.loopAdditionalSlides,this.loopedSlides>n.length&&this.params.loopedSlidesLimit&&(this.loopedSlides=n.length);var l=[],d=[];n.each((function(e,t){z(e).attr("data-swiper-slide-index",t)}));for(var c=0;c<this.loopedSlides;c+=1){var p=c-Math.floor(c/n.length)*n.length;d.push(n.eq(p)[0]),l.unshift(n.eq(n.length-p-1)[0])}for(var h=0;h<d.length;h+=1)s.append(z(d[h].cloneNode(!0)).addClass(t.slideDuplicateClass));for(var f=l.length-1;f>=0;f-=1)s.prepend(z(l[f].cloneNode(!0)).addClass(t.slideDuplicateClass))},loopFix:function(){this.emit("beforeLoopFix");var e,t=this.activeIndex,i=this.slides,s=this.loopedSlides,n=this.allowSlidePrev,a=this.allowSlideNext,r=this.snapGrid,o=this.rtlTranslate;this.allowSlidePrev=!0,this.allowSlideNext=!0;var l=-r[t]-this.getTranslate();if(t<s)e=i.length-3*s+t,e+=s,this.slideTo(e,0,!1,!0)&&0!==l&&this.setTranslate((o?-this.translate:this.translate)-l);else if(t>=i.length-s){e=-i.length+t+s,e+=s,this.slideTo(e,0,!1,!0)&&0!==l&&this.setTranslate((o?-this.translate:this.translate)-l)}this.allowSlidePrev=n,this.allowSlideNext=a,this.emit("loopFix")},loopDestroy:function(){var e=this.$wrapperEl,t=this.params,i=this.slides;e.children("."+t.slideClass+"."+t.slideDuplicateClass+",."+t.slideClass+"."+t.slideBlankClass).remove(),i.removeAttr("data-swiper-slide-index")}},grabCursor:{setGrabCursor:function(e){if(!(this.support.touch||!this.params.simulateTouch||this.params.watchOverflow&&this.isLocked||this.params.cssMode)){var t="container"===this.params.touchEventsTarget?this.el:this.wrapperEl;t.style.cursor="move",t.style.cursor=e?"grabbing":"grab"}},unsetGrabCursor:function(){this.support.touch||this.params.watchOverflow&&this.isLocked||this.params.cssMode||(this["container"===this.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="")}},events:{attachEvents:function(){var e=u(),t=this.params,i=this.support;this.onTouchStart=q.bind(this),this.onTouchMove=W.bind(this),this.onTouchEnd=Y.bind(this),t.cssMode&&(this.onScroll=Z.bind(this)),this.onClick=U.bind(this),i.touch&&!K&&(e.addEventListener("touchstart",Q),K=!0),J(this,"on")},detachEvents:function(){J(this,"off")}},breakpoints:{setBreakpoint:function(){var e=this,t=e.activeIndex,i=e.initialized,s=e.loopedSlides,n=void 0===s?0:s,a=e.params,r=e.$el,o=a.breakpoints;if(o&&(!o||0!==Object.keys(o).length)){var l=e.getBreakpoint(o,e.params.breakpointsBase,e.el);if(l&&e.currentBreakpoint!==l){var d=(l in o?o[l]:void 0)||e.originalParams,c=ee(e,a),p=ee(e,d),u=a.enabled;c&&!p?(r.removeClass(a.containerModifierClass+"grid "+a.containerModifierClass+"grid-column"),e.emitContainerClasses()):!c&&p&&(r.addClass(a.containerModifierClass+"grid"),(d.grid.fill&&"column"===d.grid.fill||!d.grid.fill&&"column"===a.grid.fill)&&r.addClass(a.containerModifierClass+"grid-column"),e.emitContainerClasses()),["navigation","pagination","scrollbar"].forEach((function(t){var i=a[t]&&a[t].enabled,s=d[t]&&d[t].enabled;i&&!s&&e[t].disable(),!i&&s&&e[t].enable()}));var h=d.direction&&d.direction!==a.direction,f=a.loop&&(d.slidesPerView!==a.slidesPerView||h);h&&i&&e.changeDirection(),G(e.params,d);var v=e.params.enabled;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),u&&!v?e.disable():!u&&v&&e.enable(),e.currentBreakpoint=l,e.emit("_beforeBreakpoint",d),f&&i&&(e.loopDestroy(),e.loopCreate(),e.updateSlides(),e.slideTo(t-n+e.loopedSlides,0,!1)),e.emit("breakpoint",d)}}},getBreakpoint:function(e,t,i){if(void 0===t&&(t="window"),e&&("container"!==t||i)){var s=!1,n=f(),a="window"===t?n.innerHeight:i.clientHeight,r=Object.keys(e).map((function(e){if("string"==typeof e&&0===e.indexOf("@")){var t=parseFloat(e.substr(1));return{value:a*t,point:e}}return{value:e,point:e}}));r.sort((function(e,t){return parseInt(e.value,10)-parseInt(t.value,10)}));for(var o=0;o<r.length;o+=1){var l=r[o],d=l.point,c=l.value;"window"===t?n.matchMedia("(min-width: "+c+"px)").matches&&(s=d):c<=i.clientWidth&&(s=d)}return s||"max"}}},checkOverflow:{checkOverflow:function(){var e=this.isLocked,t=this.params,i=t.slidesOffsetBefore;if(i){var s=this.slides.length-1,n=this.slidesGrid[s]+this.slidesSizesGrid[s]+2*i;this.isLocked=this.size>n}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){var e,t,i,s=this.classNames,n=this.params,a=this.rtl,r=this.$el,o=this.device,l=this.support,d=(e=["initialized",n.direction,{"pointer-events":!l.touch},{"free-mode":this.params.freeMode&&n.freeMode.enabled},{autoheight:n.autoHeight},{rtl:a},{grid:n.grid&&n.grid.rows>1},{"grid-column":n.grid&&n.grid.rows>1&&"column"===n.grid.fill},{android:o.android},{ios:o.ios},{"css-mode":n.cssMode},{centered:n.cssMode&&n.centeredSlides},{"watch-progress":n.watchSlidesProgress}],t=n.containerModifierClass,i=[],e.forEach((function(e){"object"==typeof e?Object.keys(e).forEach((function(s){e[s]&&i.push(t+s)})):"string"==typeof e&&i.push(t+e)})),i);s.push.apply(s,d),r.addClass([].concat(s).join(" ")),this.emitContainerClasses()},removeClasses:function(){var e=this.$el,t=this.classNames;e.removeClass(t.join(" ")),this.emitContainerClasses()}},images:{loadImage:function(e,t,i,s,n,a){var r,o=f();function l(){a&&a()}z(e).parent("picture")[0]||e.complete&&n?l():t?((r=new o.Image).onload=l,r.onerror=l,s&&(r.sizes=s),i&&(r.srcset=i),t&&(r.src=t)):l()},preloadImages:function(){var e=this;function t(){null!=e&&e&&!e.destroyed&&(void 0!==e.imagesLoaded&&(e.imagesLoaded+=1),e.imagesLoaded===e.imagesToLoad.length&&(e.params.updateOnImagesReady&&e.update(),e.emit("imagesReady")))}e.imagesToLoad=e.$el.find("img");for(var i=0;i<e.imagesToLoad.length;i+=1){var s=e.imagesToLoad[i];e.loadImage(s,s.currentSrc||s.getAttribute("src"),s.srcset||s.getAttribute("srcset"),s.sizes||s.getAttribute("sizes"),!0,t)}}}},ne={},ae=function(){function e(){for(var t,i,s=arguments.length,n=new Array(s),a=0;a<s;a++)n[a]=arguments[a];if(1===n.length&&n[0].constructor&&"Object"===Object.prototype.toString.call(n[0]).slice(8,-1)?i=n[0]:(t=n[0],i=n[1]),i||(i={}),i=G({},i),t&&!i.el&&(i.el=t),i.el&&z(i.el).length>1){var r=[];return z(i.el).each((function(t){var s=G({},i,{el:t});r.push(new e(s))})),r}var o,l=this;(l.__swiper__=!0,l.support=j(),l.device=F({userAgent:i.userAgent}),l.browser=V(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[].concat(l.__modules__),i.modules&&Array.isArray(i.modules))&&(o=l.modules).push.apply(o,i.modules);var d={};l.modules.forEach((function(e){e({swiper:l,extendParams:ie(i,d),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})}));var c,p,u=G({},te,d);return l.params=G({},u,ne,i),l.originalParams=G({},l.params),l.passedParams=G({},i),l.params&&l.params.on&&Object.keys(l.params.on).forEach((function(e){l.on(e,l.params.on[e])})),l.params&&l.params.onAny&&l.onAny(l.params.onAny),l.$=z,Object.assign(l,{enabled:l.params.enabled,el:t,classNames:[],slides:z(),slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===l.params.direction},isVertical:function(){return"vertical"===l.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEvents:(c=["touchstart","touchmove","touchend","touchcancel"],p=["pointerdown","pointermove","pointerup"],l.touchEventsTouch={start:c[0],move:c[1],end:c[2],cancel:c[3]},l.touchEventsDesktop={start:p[0],move:p[1],end:p[2]},l.support.touch||!l.params.simulateTouch?l.touchEventsTouch:l.touchEventsDesktop),touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:B(),clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,isTouchEvent:void 0,startMoving:void 0},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}var t,i,s,n=e.prototype;return n.enable=function(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))},n.disable=function(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))},n.setProgress=function(e,t){e=Math.min(Math.max(e,0),1);var i=this.minTranslate(),s=(this.maxTranslate()-i)*e+i;this.translateTo(s,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()},n.emitContainerClasses=function(){var e=this;if(e.params._emitClasses&&e.el){var t=e.el.className.split(" ").filter((function(t){return 0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass)}));e.emit("_containerClasses",t.join(" "))}},n.getSlideClasses=function(e){var t=this;return t.destroyed?"":e.className.split(" ").filter((function(e){return 0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)})).join(" ")},n.emitSlidesClasses=function(){var e=this;if(e.params._emitClasses&&e.el){var t=[];e.slides.each((function(i){var s=e.getSlideClasses(i);t.push({slideEl:i,classNames:s}),e.emit("_slideClass",i,s)})),e.emit("_slideClasses",t)}},n.slidesPerViewDynamic=function(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);var i=this.params,s=this.slides,n=this.slidesGrid,a=this.slidesSizesGrid,r=this.size,o=this.activeIndex,l=1;if(i.centeredSlides){for(var d,c=s[o].swiperSlideSize,p=o+1;p<s.length;p+=1)s[p]&&!d&&(l+=1,(c+=s[p].swiperSlideSize)>r&&(d=!0));for(var u=o-1;u>=0;u-=1)s[u]&&!d&&(l+=1,(c+=s[u].swiperSlideSize)>r&&(d=!0))}else if("current"===e)for(var h=o+1;h<s.length;h+=1){(t?n[h]+a[h]-n[o]<r:n[h]-n[o]<r)&&(l+=1)}else for(var f=o-1;f>=0;f-=1){n[o]-n[f]<r&&(l+=1)}return l},n.update=function(){var e=this;if(e&&!e.destroyed){var t=e.snapGrid,i=e.params;i.breakpoints&&e.setBreakpoint(),e.updateSize(),e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.params.freeMode&&e.params.freeMode.enabled?(s(),e.params.autoHeight&&e.updateAutoHeight()):(("auto"===e.params.slidesPerView||e.params.slidesPerView>1)&&e.isEnd&&!e.params.centeredSlides?e.slideTo(e.slides.length-1,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0))||s(),i.watchOverflow&&t!==e.snapGrid&&e.checkOverflow(),e.emit("update")}function s(){var t=e.rtlTranslate?-1*e.translate:e.translate,i=Math.min(Math.max(t,e.maxTranslate()),e.minTranslate());e.setTranslate(i),e.updateActiveIndex(),e.updateSlidesClasses()}},n.changeDirection=function(e,t){void 0===t&&(t=!0);var i=this.params.direction;return e||(e="horizontal"===i?"vertical":"horizontal"),e===i||"horizontal"!==e&&"vertical"!==e||(this.$el.removeClass(""+this.params.containerModifierClass+i).addClass(""+this.params.containerModifierClass+e),this.emitContainerClasses(),this.params.direction=e,this.slides.each((function(t){"vertical"===e?t.style.width="":t.style.height=""})),this.emit("changeDirection"),t&&this.update()),this},n.changeLanguageDirection=function(e){this.rtl&&"rtl"===e||!this.rtl&&"ltr"===e||(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.$el.addClass(this.params.containerModifierClass+"rtl"),this.el.dir="rtl"):(this.$el.removeClass(this.params.containerModifierClass+"rtl"),this.el.dir="ltr"),this.update())},n.mount=function(e){var t=this;if(t.mounted)return!0;var i=z(e||t.params.el);if(!(e=i[0]))return!1;e.swiper=t;var s=function(){return"."+(t.params.wrapperClass||"").trim().split(" ").join(".")},n=function(){if(e&&e.shadowRoot&&e.shadowRoot.querySelector){var t=z(e.shadowRoot.querySelector(s()));return t.children=function(e){return i.children(e)},t}return i.children?i.children(s()):z(i).children(s())}();if(0===n.length&&t.params.createElements){var a=u().createElement("div");n=z(a),a.className=t.params.wrapperClass,i.append(a),i.children("."+t.params.slideClass).each((function(e){n.append(e)}))}return Object.assign(t,{$el:i,el:e,$wrapperEl:n,wrapperEl:n[0],mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===i.css("direction")),wrongRTL:"-webkit-box"===n.css("display")}),!0},n.init=function(e){return this.initialized||!1===this.mount(e)||(this.emit("beforeInit"),this.params.breakpoints&&this.setBreakpoint(),this.addClasses(),this.params.loop&&this.loopCreate(),this.updateSize(),this.updateSlides(),this.params.watchOverflow&&this.checkOverflow(),this.params.grabCursor&&this.enabled&&this.setGrabCursor(),this.params.preloadImages&&this.preloadImages(),this.params.loop?this.slideTo(this.params.initialSlide+this.loopedSlides,0,this.params.runCallbacksOnInit,!1,!0):this.slideTo(this.params.initialSlide,0,this.params.runCallbacksOnInit,!1,!0),this.attachEvents(),this.initialized=!0,this.emit("init"),this.emit("afterInit")),this},n.destroy=function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var i,s=this,n=s.params,a=s.$el,r=s.$wrapperEl,o=s.slides;return void 0===s.params||s.destroyed||(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),n.loop&&s.loopDestroy(),t&&(s.removeClasses(),a.removeAttr("style"),r.removeAttr("style"),o&&o.length&&o.removeClass([n.slideVisibleClass,n.slideActiveClass,n.slideNextClass,n.slidePrevClass].join(" ")).removeAttr("style").removeAttr("data-swiper-slide-index")),s.emit("destroy"),Object.keys(s.eventsListeners).forEach((function(e){s.off(e)})),!1!==e&&(s.$el[0].swiper=null,i=s,Object.keys(i).forEach((function(e){try{i[e]=null}catch(e){}try{delete i[e]}catch(e){}}))),s.destroyed=!0),null},e.extendDefaults=function(e){G(ne,e)},e.installModule=function(t){e.prototype.__modules__||(e.prototype.__modules__=[]);var i=e.prototype.__modules__;"function"==typeof t&&i.indexOf(t)<0&&i.push(t)},e.use=function(t){return Array.isArray(t)?(t.forEach((function(t){return e.installModule(t)})),e):(e.installModule(t),e)},t=e,s=[{key:"extendedDefaults",get:function(){return ne}},{key:"defaults",get:function(){return te}}],(i=null)&&l(t.prototype,i),s&&l(t,s),e}();Object.keys(se).forEach((function(e){Object.keys(se[e]).forEach((function(t){ae.prototype[t]=se[e][t]}))})),ae.use([function(e){var t=e.swiper,i=e.on,s=e.emit,n=f(),a=null,r=null,o=function(){t&&!t.destroyed&&t.initialized&&(s("beforeResize"),s("resize"))},l=function(){t&&!t.destroyed&&t.initialized&&s("orientationchange")};i("init",(function(){t.params.resizeObserver&&void 0!==n.ResizeObserver?t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver((function(e){r=n.requestAnimationFrame((function(){var i=t.width,s=t.height,n=i,a=s;e.forEach((function(e){var i=e.contentBoxSize,s=e.contentRect,r=e.target;r&&r!==t.el||(n=s?s.width:(i[0]||i).inlineSize,a=s?s.height:(i[0]||i).blockSize)})),n===i&&a===s||o()}))}))).observe(t.el):(n.addEventListener("resize",o),n.addEventListener("orientationchange",l))})),i("destroy",(function(){r&&n.cancelAnimationFrame(r),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null),n.removeEventListener("resize",o),n.removeEventListener("orientationchange",l)}))},function(e){var t=e.swiper,i=e.extendParams,s=e.on,n=e.emit,a=[],r=f(),o=function(e,t){void 0===t&&(t={});var i=new(r.MutationObserver||r.WebkitMutationObserver)((function(e){if(1!==e.length){var t=function(){n("observerUpdate",e[0])};r.requestAnimationFrame?r.requestAnimationFrame(t):r.setTimeout(t,0)}else n("observerUpdate",e[0])}));i.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:void 0===t.childList||t.childList,characterData:void 0===t.characterData||t.characterData}),a.push(i)};i({observer:!1,observeParents:!1,observeSlideChildren:!1}),s("init",(function(){if(t.params.observer){if(t.params.observeParents)for(var e=t.$el.parents(),i=0;i<e.length;i+=1)o(e[i]);o(t.$el[0],{childList:t.params.observeSlideChildren}),o(t.$wrapperEl[0],{attributes:!1})}})),s("destroy",(function(){a.forEach((function(e){e.disconnect()})),a.splice(0,a.length)}))}]);var re=ae;function oe(e,t,i,s){var n=u();return e.params.createElements&&Object.keys(s).forEach((function(a){if(!i[a]&&!0===i.auto){var r=e.$el.children("."+s[a])[0];r||((r=n.createElement("div")).className=s[a],e.$el.append(r)),i[a]=r,t[a]=r}})),i}function le(e){var t=e.swiper,i=e.extendParams,s=e.on,n=e.emit;function a(e){var i;return e&&(i=z(e),t.params.uniqueNavElements&&"string"==typeof e&&i.length>1&&1===t.$el.find(e).length&&(i=t.$el.find(e))),i}function r(e,i){var s=t.params.navigation;e&&e.length>0&&(e[i?"addClass":"removeClass"](s.disabledClass),e[0]&&"BUTTON"===e[0].tagName&&(e[0].disabled=i),t.params.watchOverflow&&t.enabled&&e[t.isLocked?"addClass":"removeClass"](s.lockClass))}function o(){if(!t.params.loop){var e=t.navigation,i=e.$nextEl;r(e.$prevEl,t.isBeginning&&!t.params.rewind),r(i,t.isEnd&&!t.params.rewind)}}function l(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),n("navigationPrev"))}function d(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),n("navigationNext"))}function c(){var e=t.params.navigation;if(t.params.navigation=oe(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),e.nextEl||e.prevEl){var i=a(e.nextEl),s=a(e.prevEl);i&&i.length>0&&i.on("click",d),s&&s.length>0&&s.on("click",l),Object.assign(t.navigation,{$nextEl:i,nextEl:i&&i[0],$prevEl:s,prevEl:s&&s[0]}),t.enabled||(i&&i.addClass(e.lockClass),s&&s.addClass(e.lockClass))}}function p(){var e=t.navigation,i=e.$nextEl,s=e.$prevEl;i&&i.length&&(i.off("click",d),i.removeClass(t.params.navigation.disabledClass)),s&&s.length&&(s.off("click",l),s.removeClass(t.params.navigation.disabledClass))}i({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,$nextEl:null,prevEl:null,$prevEl:null},s("init",(function(){!1===t.params.navigation.enabled?u():(c(),o())})),s("toEdge fromEdge lock unlock",(function(){o()})),s("destroy",(function(){p()})),s("enable disable",(function(){var e=t.navigation,i=e.$nextEl,s=e.$prevEl;i&&i[t.enabled?"removeClass":"addClass"](t.params.navigation.lockClass),s&&s[t.enabled?"removeClass":"addClass"](t.params.navigation.lockClass)})),s("click",(function(e,i){var s=t.navigation,a=s.$nextEl,r=s.$prevEl,o=i.target;if(t.params.navigation.hideOnClick&&!z(o).is(r)&&!z(o).is(a)){if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===o||t.pagination.el.contains(o)))return;var l;a?l=a.hasClass(t.params.navigation.hiddenClass):r&&(l=r.hasClass(t.params.navigation.hiddenClass)),n(!0===l?"navigationShow":"navigationHide"),a&&a.toggleClass(t.params.navigation.hiddenClass),r&&r.toggleClass(t.params.navigation.hiddenClass)}}));var u=function(){t.$el.addClass(t.params.navigation.navigationDisabledClass),p()};Object.assign(t.navigation,{enable:function(){t.$el.removeClass(t.params.navigation.navigationDisabledClass),c(),o()},disable:u,update:o,init:c,destroy:p})}function de(e){return void 0===e&&(e=""),"."+e.trim().replace(/([\.:!\/])/g,"\\$1").replace(/ /g,".")}function ce(e){var t,i=e.swiper,s=e.extendParams,n=e.on,a=e.emit,r="swiper-pagination";s({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:r+"-bullet",bulletActiveClass:r+"-bullet-active",modifierClass:r+"-",currentClass:r+"-current",totalClass:r+"-total",hiddenClass:r+"-hidden",progressbarFillClass:r+"-progressbar-fill",progressbarOppositeClass:r+"-progressbar-opposite",clickableClass:r+"-clickable",lockClass:r+"-lock",horizontalClass:r+"-horizontal",verticalClass:r+"-vertical",paginationDisabledClass:r+"-disabled"}}),i.pagination={el:null,$el:null,bullets:[]};var o=0;function l(){return!i.params.pagination.el||!i.pagination.el||!i.pagination.$el||0===i.pagination.$el.length}function d(e,t){var s=i.params.pagination.bulletActiveClass;e[t]().addClass(s+"-"+t)[t]().addClass(s+"-"+t+"-"+t)}function c(){var e=i.rtl,s=i.params.pagination;if(!l()){var n,r=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,c=i.pagination.$el,p=i.params.loop?Math.ceil((r-2*i.loopedSlides)/i.params.slidesPerGroup):i.snapGrid.length;if(i.params.loop?((n=Math.ceil((i.activeIndex-i.loopedSlides)/i.params.slidesPerGroup))>r-1-2*i.loopedSlides&&(n-=r-2*i.loopedSlides),n>p-1&&(n-=p),n<0&&"bullets"!==i.params.paginationType&&(n=p+n)):n=void 0!==i.snapIndex?i.snapIndex:i.activeIndex||0,"bullets"===s.type&&i.pagination.bullets&&i.pagination.bullets.length>0){var u,h,f,v=i.pagination.bullets;if(s.dynamicBullets&&(t=v.eq(0)[i.isHorizontal()?"outerWidth":"outerHeight"](!0),c.css(i.isHorizontal()?"width":"height",t*(s.dynamicMainBullets+4)+"px"),s.dynamicMainBullets>1&&void 0!==i.previousIndex&&((o+=n-(i.previousIndex-i.loopedSlides||0))>s.dynamicMainBullets-1?o=s.dynamicMainBullets-1:o<0&&(o=0)),u=Math.max(n-o,0),f=((h=u+(Math.min(v.length,s.dynamicMainBullets)-1))+u)/2),v.removeClass(["","-next","-next-next","-prev","-prev-prev","-main"].map((function(e){return""+s.bulletActiveClass+e})).join(" ")),c.length>1)v.each((function(e){var t=z(e),i=t.index();i===n&&t.addClass(s.bulletActiveClass),s.dynamicBullets&&(i>=u&&i<=h&&t.addClass(s.bulletActiveClass+"-main"),i===u&&d(t,"prev"),i===h&&d(t,"next"))}));else{var m=v.eq(n),g=m.index();if(m.addClass(s.bulletActiveClass),s.dynamicBullets){for(var b=v.eq(u),w=v.eq(h),y=u;y<=h;y+=1)v.eq(y).addClass(s.bulletActiveClass+"-main");if(i.params.loop)if(g>=v.length){for(var C=s.dynamicMainBullets;C>=0;C-=1)v.eq(v.length-C).addClass(s.bulletActiveClass+"-main");v.eq(v.length-s.dynamicMainBullets-1).addClass(s.bulletActiveClass+"-prev")}else d(b,"prev"),d(w,"next");else d(b,"prev"),d(w,"next")}}if(s.dynamicBullets){var T=Math.min(v.length,s.dynamicMainBullets+4),x=(t*T-t)/2-f*t,S=e?"right":"left";v.css(i.isHorizontal()?S:"top",x+"px")}}if("fraction"===s.type&&(c.find(de(s.currentClass)).text(s.formatFractionCurrent(n+1)),c.find(de(s.totalClass)).text(s.formatFractionTotal(p))),"progressbar"===s.type){var E;E=s.progressbarOpposite?i.isHorizontal()?"vertical":"horizontal":i.isHorizontal()?"horizontal":"vertical";var k=(n+1)/p,M=1,P=1;"horizontal"===E?M=k:P=k,c.find(de(s.progressbarFillClass)).transform("translate3d(0,0,0) scaleX("+M+") scaleY("+P+")").transition(i.params.speed)}"custom"===s.type&&s.renderCustom?(c.html(s.renderCustom(i,n+1,p)),a("paginationRender",c[0])):a("paginationUpdate",c[0]),i.params.watchOverflow&&i.enabled&&c[i.isLocked?"addClass":"removeClass"](s.lockClass)}}function p(){var e=i.params.pagination;if(!l()){var t=i.virtual&&i.params.virtual.enabled?i.virtual.slides.length:i.slides.length,s=i.pagination.$el,n="";if("bullets"===e.type){var r=i.params.loop?Math.ceil((t-2*i.loopedSlides)/i.params.slidesPerGroup):i.snapGrid.length;i.params.freeMode&&i.params.freeMode.enabled&&!i.params.loop&&r>t&&(r=t);for(var o=0;o<r;o+=1)e.renderBullet?n+=e.renderBullet.call(i,o,e.bulletClass):n+="<"+e.bulletElement+' class="'+e.bulletClass+'"></'+e.bulletElement+">";s.html(n),i.pagination.bullets=s.find(de(e.bulletClass))}"fraction"===e.type&&(n=e.renderFraction?e.renderFraction.call(i,e.currentClass,e.totalClass):'<span class="'+e.currentClass+'"></span> / <span class="'+e.totalClass+'"></span>',s.html(n)),"progressbar"===e.type&&(n=e.renderProgressbar?e.renderProgressbar.call(i,e.progressbarFillClass):'<span class="'+e.progressbarFillClass+'"></span>',s.html(n)),"custom"!==e.type&&a("paginationRender",i.pagination.$el[0])}}function u(){i.params.pagination=oe(i,i.originalParams.pagination,i.params.pagination,{el:"swiper-pagination"});var e=i.params.pagination;if(e.el){var t=z(e.el);0!==t.length&&(i.params.uniqueNavElements&&"string"==typeof e.el&&t.length>1&&(t=i.$el.find(e.el)).length>1&&(t=t.filter((function(e){return z(e).parents(".swiper")[0]===i.el}))),"bullets"===e.type&&e.clickable&&t.addClass(e.clickableClass),t.addClass(e.modifierClass+e.type),t.addClass(i.isHorizontal()?e.horizontalClass:e.verticalClass),"bullets"===e.type&&e.dynamicBullets&&(t.addClass(""+e.modifierClass+e.type+"-dynamic"),o=0,e.dynamicMainBullets<1&&(e.dynamicMainBullets=1)),"progressbar"===e.type&&e.progressbarOpposite&&t.addClass(e.progressbarOppositeClass),e.clickable&&t.on("click",de(e.bulletClass),(function(e){e.preventDefault();var t=z(this).index()*i.params.slidesPerGroup;i.params.loop&&(t+=i.loopedSlides),i.slideTo(t)})),Object.assign(i.pagination,{$el:t,el:t[0]}),i.enabled||t.addClass(e.lockClass))}}function h(){var e=i.params.pagination;if(!l()){var t=i.pagination.$el;t.removeClass(e.hiddenClass),t.removeClass(e.modifierClass+e.type),t.removeClass(i.isHorizontal()?e.horizontalClass:e.verticalClass),i.pagination.bullets&&i.pagination.bullets.removeClass&&i.pagination.bullets.removeClass(e.bulletActiveClass),e.clickable&&t.off("click",de(e.bulletClass))}}n("init",(function(){!1===i.params.pagination.enabled?f():(u(),p(),c())})),n("activeIndexChange",(function(){(i.params.loop||void 0===i.snapIndex)&&c()})),n("snapIndexChange",(function(){i.params.loop||c()})),n("slidesLengthChange",(function(){i.params.loop&&(p(),c())})),n("snapGridLengthChange",(function(){i.params.loop||(p(),c())})),n("destroy",(function(){h()})),n("enable disable",(function(){var e=i.pagination.$el;e&&e[i.enabled?"removeClass":"addClass"](i.params.pagination.lockClass)})),n("lock unlock",(function(){c()})),n("click",(function(e,t){var s=t.target,n=i.pagination.$el;if(i.params.pagination.el&&i.params.pagination.hideOnClick&&n&&n.length>0&&!z(s).hasClass(i.params.pagination.bulletClass)){if(i.navigation&&(i.navigation.nextEl&&s===i.navigation.nextEl||i.navigation.prevEl&&s===i.navigation.prevEl))return;var r=n.hasClass(i.params.pagination.hiddenClass);a(!0===r?"paginationShow":"paginationHide"),n.toggleClass(i.params.pagination.hiddenClass)}}));var f=function(){i.$el.addClass(i.params.pagination.paginationDisabledClass),i.pagination.$el&&i.pagination.$el.addClass(i.params.pagination.paginationDisabledClass),h()};Object.assign(i.pagination,{enable:function(){i.$el.removeClass(i.params.pagination.paginationDisabledClass),i.pagination.$el&&i.pagination.$el.removeClass(i.params.pagination.paginationDisabledClass),u(),p(),c()},disable:f,render:p,update:c,init:u,destroy:h})}function pe(e){var t,i=e.swiper,s=e.extendParams,n=e.on,a=e.emit;function r(){if(!i.size)return i.autoplay.running=!1,void(i.autoplay.paused=!1);var e=i.slides.eq(i.activeIndex),s=i.params.autoplay.delay;e.attr("data-swiper-autoplay")&&(s=e.attr("data-swiper-autoplay")||i.params.autoplay.delay),clearTimeout(t),t=I((function(){var e;i.params.autoplay.reverseDirection?i.params.loop?(i.loopFix(),e=i.slidePrev(i.params.speed,!0,!0),a("autoplay")):i.isBeginning?i.params.autoplay.stopOnLastSlide?l():(e=i.slideTo(i.slides.length-1,i.params.speed,!0,!0),a("autoplay")):(e=i.slidePrev(i.params.speed,!0,!0),a("autoplay")):i.params.loop?(i.loopFix(),e=i.slideNext(i.params.speed,!0,!0),a("autoplay")):i.isEnd?i.params.autoplay.stopOnLastSlide?l():(e=i.slideTo(0,i.params.speed,!0,!0),a("autoplay")):(e=i.slideNext(i.params.speed,!0,!0),a("autoplay")),(i.params.cssMode&&i.autoplay.running||!1===e)&&r()}),s)}function o(){return void 0===t&&(!i.autoplay.running&&(i.autoplay.running=!0,a("autoplayStart"),r(),!0))}function l(){return!!i.autoplay.running&&(void 0!==t&&(t&&(clearTimeout(t),t=void 0),i.autoplay.running=!1,a("autoplayStop"),!0))}function d(e){i.autoplay.running&&(i.autoplay.paused||(t&&clearTimeout(t),i.autoplay.paused=!0,0!==e&&i.params.autoplay.waitForTransition?["transitionend","webkitTransitionEnd"].forEach((function(e){i.$wrapperEl[0].addEventListener(e,p)})):(i.autoplay.paused=!1,r())))}function c(){var e=u();"hidden"===e.visibilityState&&i.autoplay.running&&d(),"visible"===e.visibilityState&&i.autoplay.paused&&(r(),i.autoplay.paused=!1)}function p(e){i&&!i.destroyed&&i.$wrapperEl&&e.target===i.$wrapperEl[0]&&(["transitionend","webkitTransitionEnd"].forEach((function(e){i.$wrapperEl[0].removeEventListener(e,p)})),i.autoplay.paused=!1,i.autoplay.running?r():l())}function h(){i.params.autoplay.disableOnInteraction?l():(a("autoplayPause"),d()),["transitionend","webkitTransitionEnd"].forEach((function(e){i.$wrapperEl[0].removeEventListener(e,p)}))}function f(){i.params.autoplay.disableOnInteraction||(i.autoplay.paused=!1,a("autoplayResume"),r())}i.autoplay={running:!1,paused:!1},s({autoplay:{enabled:!1,delay:3e3,waitForTransition:!0,disableOnInteraction:!0,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}}),n("init",(function(){i.params.autoplay.enabled&&(o(),u().addEventListener("visibilitychange",c),i.params.autoplay.pauseOnMouseEnter&&(i.$el.on("mouseenter",h),i.$el.on("mouseleave",f)))})),n("beforeTransitionStart",(function(e,t,s){i.autoplay.running&&(s||!i.params.autoplay.disableOnInteraction?i.autoplay.pause(t):l())})),n("sliderFirstMove",(function(){i.autoplay.running&&(i.params.autoplay.disableOnInteraction?l():d())})),n("touchEnd",(function(){i.params.cssMode&&i.autoplay.paused&&!i.params.autoplay.disableOnInteraction&&r()})),n("destroy",(function(){i.$el.off("mouseenter",h),i.$el.off("mouseleave",f),i.autoplay.running&&l(),u().removeEventListener("visibilitychange",c)})),Object.assign(i.autoplay,{pause:d,run:r,start:o,stop:l})}function ue(e){var t,i=e.effect,s=e.swiper,n=e.on,a=e.setTranslate,r=e.setTransition,o=e.overwriteParams,l=e.perspective,d=e.recreateShadows,c=e.getEffectParams;n("beforeInit",(function(){if(s.params.effect===i){s.classNames.push(""+s.params.containerModifierClass+i),l&&l()&&s.classNames.push(s.params.containerModifierClass+"3d");var e=o?o():{};Object.assign(s.params,e),Object.assign(s.originalParams,e)}})),n("setTranslate",(function(){s.params.effect===i&&a()})),n("setTransition",(function(e,t){s.params.effect===i&&r(t)})),n("transitionEnd",(function(){if(s.params.effect===i&&d){if(!c||!c().slideShadows)return;s.slides.each((function(e){s.$(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").remove()})),d()}})),n("virtualUpdate",(function(){s.params.effect===i&&(s.slides.length||(t=!0),requestAnimationFrame((function(){t&&s.slides&&s.slides.length&&(a(),t=!1)})))}))}function he(e,t){return e.transformEl?t.find(e.transformEl).css({"backface-visibility":"hidden","-webkit-backface-visibility":"hidden"}):t}function fe(e,t,i){var s="swiper-slide-shadow"+(i?"-"+i:""),n=e.transformEl?t.find(e.transformEl):t,a=n.children("."+s);return a.length||(a=z('<div class="swiper-slide-shadow'+(i?"-"+i:"")+'"></div>'),n.append(a)),a}function ve(e){var t=e.swiper,i=e.extendParams,s=e.on;i({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0,transformEl:null}});ue({effect:"coverflow",swiper:t,on:s,setTranslate:function(){for(var e=t.width,i=t.height,s=t.slides,n=t.slidesSizesGrid,a=t.params.coverflowEffect,r=t.isHorizontal(),o=t.translate,l=r?e/2-o:i/2-o,d=r?a.rotate:-a.rotate,c=a.depth,p=0,u=s.length;p<u;p+=1){var h=s.eq(p),f=n[p],v=(l-h[0].swiperSlideOffset-f/2)/f,m="function"==typeof a.modifier?a.modifier(v):v*a.modifier,g=r?d*m:0,b=r?0:d*m,w=-c*Math.abs(m),y=a.stretch;"string"==typeof y&&-1!==y.indexOf("%")&&(y=parseFloat(a.stretch)/100*f);var C=r?0:y*m,T=r?y*m:0,x=1-(1-a.scale)*Math.abs(m);Math.abs(T)<.001&&(T=0),Math.abs(C)<.001&&(C=0),Math.abs(w)<.001&&(w=0),Math.abs(g)<.001&&(g=0),Math.abs(b)<.001&&(b=0),Math.abs(x)<.001&&(x=0);var S="translate3d("+T+"px,"+C+"px,"+w+"px)  rotateX("+b+"deg) rotateY("+g+"deg) scale("+x+")";if(he(a,h).transform(S),h[0].style.zIndex=1-Math.abs(Math.round(m)),a.slideShadows){var E=r?h.find(".swiper-slide-shadow-left"):h.find(".swiper-slide-shadow-top"),k=r?h.find(".swiper-slide-shadow-right"):h.find(".swiper-slide-shadow-bottom");0===E.length&&(E=fe(a,h,r?"left":"top")),0===k.length&&(k=fe(a,h,r?"right":"bottom")),E.length&&(E[0].style.opacity=m>0?m:0),k.length&&(k[0].style.opacity=-m>0?-m:0)}}},setTransition:function(e){var i=t.params.coverflowEffect.transformEl;(i?t.slides.find(i):t.slides).transition(e).find(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").transition(e)},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0}}})}var me,ge,be=!1,we=null,ye=!1,Ce=null,Te=0,xe=!1,Se=null,Ee=(ge=!1,me=navigator.userAgent||navigator.vendor||window.opera,void((/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(me)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(me.substr(0,4)))&&(ge=!0)),ge);function ke(e){return we=[],[].push.apply(we,e),e}function Me(e){return Se=[],[].push.apply(Se,e),e}function Pe(e){return Ce=[],[].push.apply(Ce,e),e}a.a.initializers.add("wusong8899-client1-header-adv",(function(){Object(s.extend)(o.a.prototype,"view",(function(e){var t=a.a.current.get("routeName");t&&("tags"!==t||function(e){Ee&&($(".item-newDiscussion").find("span.Button-label").html("<div class='buttonRegister'>登录</div>"),$(".item-newDiscussion").find("span.Button-label").css("display","block"),$(".item-newDiscussion").find("span.Button-label").css("font-size","14px"),$(".item-newDiscussion").find("span.Button-label").css("word-spacing","-1px"));$(".item-newDiscussion").find("i").css("display","none"),$(".item-nav").remove(),$(".TagTiles").css("display","none");var t=setInterval((function(){if(e.dom&&(clearInterval(t),void 0!==e.dom)){var i=a.a.forum.attribute("Client1HeaderAdvTransitionTime");i||(i=5e3);var s=2*$(window).width()-50,n=document.getElementById("swiperAdContainer");if(null!==n)return;(n=document.createElement("div")).className="swiperAdContainer",n.id="swiperAdContainer",!0===Ee&&(n.style.width=s+"px",n.style.marginLeft=-.254*s+"px");var r=document.createElement("div");r.className="swiper adSwiper",n.appendChild(r);var o=document.createElement("div");o.className="swiper-wrapper",r.appendChild(o);for(var l=1;l<=30;l++){var d=document.createElement("div"),c=a.a.forum.attribute("Client1HeaderAdvImage"+l),p=a.a.forum.attribute("Client1HeaderAdvLink"+l);c&&(d.className="swiper-slide",d.innerHTML="<img onclick='window.location.href=\""+p+"\"' src='"+c+"' />",o.appendChild(d))}var u=document.createElement("div");u.className="swiper-button-next",r.appendChild(u);var h=document.createElement("div");h.className="swiper-button-prev",r.appendChild(h);var f=document.createElement("div");f.className="swiper-pagination",r.appendChild(f),$("#content .container").prepend(n),new re(".adSwiper",{autoplay:{delay:i},loop:!0,spaceBetween:30,effect:"coverflow",centeredSlides:!0,slidesPerView:2,coverflowEffect:{rotate:0,depth:100,modifier:1,slideShadows:!0,stretch:0},pagination:{el:".swiper-pagination",type:"bullets"},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},modules:[ve,le,ce,pe]}),function(){if(!1===ye)ye=!0,a.a.store.find("linksQueueList").catch((function(){})).then(Pe.bind(this))}(),function(){if(!1===be)be=!0,a.a.store.find("syncTronscanList").catch((function(){})).then(ke.bind(this))}(),function(){if(!1===xe)xe=!0,a.a.store.find("buttonsCustomizationList").catch((function(){})).then(Me.bind(this))}();var v=setInterval((function(){var e;null!==we&&null!==Ce&&null!==Se&&(clearInterval(v),0===$("#swiperTagContainer").length&&(!function(){var e=$(".TagTile");if(0===$("#swiperTagContainer").length){var t=document.createElement("div");t.className="swiperTagContainer",t.id="swiperTagContainer";var i=document.createElement("div");i.className="swiper tagSwiper";var s=document.createElement("div");s.className="TagTextOuterContainer",t.appendChild(s),s.appendChild(i);var n=document.createElement("div");n.className="swiper-wrapper",n.id="swiperTagWrapper",i.appendChild(n);for(var a=0;a<e.length;a++){var r=e[a],o=$(r).find("a").attr("href"),l=$(r).css("background"),d=$(r).find(".TagTile-name").text(),c=$(r).find(".TagTile-name").css("color"),p=($(r).find(".TagTile-description").text(),$(r).find(".TagTile-description").css("color"),document.createElement("div"));p.className="swiper-slide swiper-slide-tag",p.innerHTML="<a href='"+o+"'><div class='"+(Ee?"swiper-slide-tag-inner-mobile":"swiper-slide-tag-inner")+"' style='background:"+l+";background-size: cover;background-position: center;background-repeat: no-repeat;'><div style='font-weight:bold;font-size:14px;color:"+c+"'>"+d+"</div></div></a>",n.appendChild(p)}$("#content .container .TagsPage-content").prepend(t),$(s).prepend("<div class='TagTextContainer'><div class='TagTextIcon'></div>中文玩家社区资讯</div>"),$(s).append('<div style="text-align:center;padding-top: 10px;"><button class="Button Button--primary" type="button" style="font-weight: normal !important; color:#ffa000; background: #1a1d2e !important;border-radius: 2rem !important;"><div style="margin-top: 5px;" class="Button-label"><img onClick="window.open(\'https://kick.com/wangming886\', \'_blank\')" style="width: 32px;" src="https://mutluresim.com/images/2023/04/10/KcgSG.png"><img onClick="window.open(\'https://m.facebook.com\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcF6i.png"><img onClick="window.open(\'https://twitter.com/youngron131_\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcDas.png"><img onClick="window.open(\'https://m.youtube.com/@ag8888\',\'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcQjd.png"><img onClick="window.open(\'https://www.instagram.com/p/CqLvh94Sk8F/?igshid=YmMyMTA2M2Y=\', \'_blank\')" style="width: 32px;margin-left: 20px;" src="https://mutluresim.com/images/2023/04/10/KcBAL.png"></div></button></div>'),$(".TagTiles").remove(),!0===Ee&&($("#app").css("overflow-x","hidden"),$(".App-content").css("min-height","auto"),$(".App-content").css("background","")),new re(".tagSwiper",{loop:!0,spaceBetween:Ee?90:10,slidesPerView:Ee?2:7,autoplay:{delay:3e3,disableOnInteraction:!1},modules:[pe]}),function(){var e=document.getElementById("TronscanTextContainer");if(null===e){(e=document.createElement("div")).id="TronscanTextContainer",e.innerHTML="<div class='TronscanTextIcon'></div>知名博彩公司USDT/TRC公开链钱包额度",e.className="TronscanTextContainer",$("#swiperTagContainer").append(e);var t=document.createElement("div");t.className="swiper tronscanSwiper",$("#swiperTagContainer").append(t);var i=document.createElement("div");i.className="swiper-wrapper",t.appendChild(i);for(var s=0;s<we.length;s++){var n=we[s],a=(n.name(),parseInt(n.valueUsd())+" USD"),r="url("+n.img()+");",o=document.createElement("div");o.className="swiper-slide swiper-slide-tag",o.innerHTML="<div style='width:100px;height:130px;border-radius: 12px;background: "+r+";background-size: cover;background-position: center;background-repeat: no-repeat;word-break: break-all;'><div style='display:inline-block;position: absolute;top: 56px;height:20px;width:100px;background: rgba(255,255,255,0.5);'></div><div class='tronscanMask'><div style='display: flex;width: 90px;justify-content: center;font-weight: bold;color:#02F78E;font-size:10px;'><span>"+a+"</span></div></div></div>",i.appendChild(o)}new re(".tronscanSwiper",{loop:!0,spaceBetween:Ee?80:10,slidesPerView:Ee?4:7})}}()}}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="zhiboContainer",e.style.height=$(".swiperTagContainer").css("height");e.innerHTML="<div id='linksQueueRefresh' style='z-index: 1000;display:inline-block;scale:0.8;position: fixed;bottom: "+(Ee?0:-6)+"px;'><div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='refreshZhiBoButton' class='u-btn-text'>刷新直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div></div><div class='zhiboSubContainer'><div id='linksQueuePrev' style='display:inline-block;scale:0.8'><div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div style='color:#666' id='prevZhiBoButton' class='u-btn-text'>上个直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div></div><div id='linksQueueNext' style='display:inline-block;scale:0.8'><div style='padding-top: 0px;padding-bottom: 0px;' class='selectTitle'><div class='switch-btns'><div class='btns-container'><button type='button' class='u-btn'><div id='nextZhiBoButton' class='u-btn-text'>切换直播间</div></button><div id='buttonSelectedBackground' class='selected-bg' style='left: 0px; top: 0px; opacity: 1;'></div></div></div></div></div></div><iframe id='zhiboIframe' name='contentOnly' class='zhiboIframe' src=''></iframe>",$("#content .TagsPage-content").prepend(e);var t=Ee?"touchend":"click",i=document.getElementById("zhiboIframe");$("#linksQueueRefresh").on(t,(function(){i.src="",setTimeout((function(){var e=Ce[Te].attribute("links");i.src=e}),100)})),$("#linksQueuePrev").on(t,(function(){if(Te--,void 0!==Ce[Te]){var e=Ce[Te].attribute("links");i.src=e}$("#nextZhiBoButton").css("color",""),void 0===Ce[Te-1]?$("#prevZhiBoButton").css("color","#666"):$("#prevZhiBoButton").css("color","")})),$("#linksQueueNext").on(t,(function(){if(Te++,void 0!==Ce[Te]){var e=Ce[Te].attribute("links");i.src=e}$("#prevZhiBoButton").css("color",""),void 0===Ce[Te+1]?$("#nextZhiBoButton").css("color","#666"):$("#nextZhiBoButton").css("color","")}))}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="youxiContainer",e.style.height=$(".swiperTagContainer").css("height"),e.innerText=a.a.translator.trans("wusong8899-client1.forum.under-construction"),$("#content .TagsPage-content").prepend(e)}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="buttonCustomizationContainer",e.style.height=$(".swiperTagContainer").css("height"),e.innerHTML="<iframe id='customButtonIframe' name='contentOnly' class='customButtonIframe' src=''></iframe>",$("#content .TagsPage-content").prepend(e)}(),function(){$(".swiperTagContainer").css("height");var e=document.createElement("div");e.className="shangchengContainer",e.style.height=$(".swiperTagContainer").css("height"),e.innerText=a.a.translator.trans("wusong8899-client1.forum.under-construction"),$("#content .TagsPage-content").prepend(e)}(),function(){var e=document.getElementById("selectTitleContainer");if(null===e){(e=document.createElement("div")).id="selectTitleContainer",e.className="selectTitleContainer";for(var t="",i={},s=3,n=0;n<Se.length;n++){var a=Se[n],r=a.name(),o=a.icon(),l=(a.color(),a.url());s++,i[s]={url:l},t+='<button id="client1HeaderButton'+s+'" number="'+s+'" type="button" class="u-btn"><i class="'+o+'"></i><div class="u-btn-text">'+r+"</div></button>"}var d=document.createElement("div");d.className="selectTitle",d.innerHTML='<div class="switch-btns" style="max-width:'+$(".TagsPage-content").width()+'px"><div class="btns-container"><button id="client1HeaderButton0" type="button" class="u-btn" number="0"><i class="fas fa-paw"></i><div class="u-btn-text">论坛</div></button><button id="client1HeaderButton1" number="1" type="button" class="u-btn"><i class="fab fa-twitch"></i><div class="u-btn-text">直播</div></button><button id="client1HeaderButton2" number="2" type="button" class="u-btn"><i class="fas fa-dice"></i><div class="u-btn-text">游戏</div></button><button id="client1HeaderButton3" number="3" type="button" class="u-btn"><i class="fas fa-gifts"></i><div class="u-btn-text">商城</div></button>'+t+'<div id="buttonSelectedBackground" class="selected-bg" style="left: 0px; top: 0px; opacity: 1;"></div></div></div>',e.appendChild(d),$("#content .TagsPage-content").prepend(e);var c=Ee?"touchend":"click",p=0,u={},h=Ee?3:0;u[0]=0;for(var f=0;f<s;f++){var v=$("#client1HeaderButton"+f).outerWidth();1!==f&&2!==f&&(0===f&&$("#buttonSelectedBackground").width(v),u[f+1]=v+p-h,p+=v)}$(".u-btn").on(c,(function(){var e=parseInt($(this).attr("number")),t=document.getElementById("zhiboIframe");if($(".App").css("min-height","100vh"),0===e)$(".swiperTagContainer").css("display",""),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","none"),$(".App").css("min-height","50vh"),t.src="";else if(1===e){$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","inline-block"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","none");var s=window.innerHeight-$("#app-navigation").outerHeight()-$(".selectTitleContainer").outerHeight()-$("#linksQueuePrev").outerHeight();if($("#zhiboIframe").css("height",s+"px"),Ce[Te]){var n=Ce[Te].attribute("links");t.src!==n&&(t.src=n)}}else if(2===e)$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","flex"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","none"),t.src="";else if(3===e)$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","none"),$(".shangchengContainer").css("display","flex"),t.src="";else{var a=i[e];if(a){var r=window.innerHeight-$("#app-navigation").outerHeight()-$(".selectTitleContainer").outerHeight()-$("#linksQueuePrev").outerHeight(),o=0,l="yes",d=$(".swiperTagContainer").css("height");5==e?(r=550,d=550):6==e?(r=d=380,l="no"):(7==e||8==e)&&(o=20),$(".buttonCustomizationContainer").css("padding-bottom",o+"px"),$("#customButtonIframe").css("padding-bottom",o+"px"),$("#customButtonIframe").attr("scrolling",l),$(".buttonCustomizationContainer").css("height",d+"px"),$("#customButtonIframe").css("height",r+"px");var c=document.getElementById("customButtonIframe");$(".swiperTagContainer").css("display","none"),$(".zhiboContainer").css("display","none"),$(".youxiContainer").css("display","none"),$(".buttonCustomizationContainer").css("display","inline-block"),$(".shangchengContainer").css("display","none"),c.src=a.url}}$("#buttonSelectedBackground").width($(this).outerWidth()),void 0!==u[e]&&$("#buttonSelectedBackground").css("left",u[e])}))}}(),$(".item-MoneyLeaderboard").addClass("App-primaryControl"),$(".item-forum-checkin").parent().append($(".item-MoneyLeaderboard")),$(".item-MoneyLeaderboard").css("right","75px")),a.a.session.user||null===(e=document.getElementById("wusong8899Client1HeaderIcon"))&&((e=document.createElement("div")).id="wusong8899Client1HeaderIcon",e.style.display="inline-block",e.style.marginTop="8px",e.innerHTML='<img src="https://lg666.cc/assets/files/2023-01-18/1674049401-881154-test-16.png" style="height: 24px;" />',$("#app-navigation").find(".App-backControl").prepend(e)))}),100)}}),10)}(e))}))}))}]);
//# sourceMappingURL=forum.js.map