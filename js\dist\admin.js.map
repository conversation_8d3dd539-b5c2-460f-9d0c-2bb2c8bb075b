{"version": 3, "sources": ["webpack://@justoverclock/header-slideshow/webpack/bootstrap", "webpack://@justoverclock/header-slideshow/external \"flarum.core.compat['app']\"", "webpack://@justoverclock/header-slideshow/./src/admin/index.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "flarum", "core", "compat", "app", "initializers", "add", "extensionData", "registerSetting", "setting", "type", "label", "translator", "trans"], "mappings": "2BACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QA0Df,OArDAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,G,gBClFrDhC,EAAOD,QAAUkC,OAAOC,KAAKC,OAAY,K,2DCEzCC,IAAIC,aAAaC,IAAI,6BAA6B,WAC9CF,IAAIG,cAAJ,IACO,6BACJC,gBAAgB,CACfC,QAAS,0CACTC,KAAM,SACNC,MAAOP,IAAIQ,WAAWC,MAAM,yCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,iCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,gCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,kCAE7BL,gBAAgB,CACfC,QAAS,kCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM,iCAE7BL,gBAAgB,CACfC,QAAS,mCACTC,KAAM,MACNC,MAAOP,IAAIQ,WAAWC,MAAM", "file": "admin.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 4);\n", "module.exports = flarum.core.compat['app'];", "import app from 'flarum/app';\n\napp.initializers.add('wusong8899/client1-header-adv', () => {\n    app.extensionData\n      .for('wusong8899-client1-header-adv')\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.TransitionTime',\n        type: 'number',\n        label: app.translator.trans('wusong8899-client1.admin.TransitionTime'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link1',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link1'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image1',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image1'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link2',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link2'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image2',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image2'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link3',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link3'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image3',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image3'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link4',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link4'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image4',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image4'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link5',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link5'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image5',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image5'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link6',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link6'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image6',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image6'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link7',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link7'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image7',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image7'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link8',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link8'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image8',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image8'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link9',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link9'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image9',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image9'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link10',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link10'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image10',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image10'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link11',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link11'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image11',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image11'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link12',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link12'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image12',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image12'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link13',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link13'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image13',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image13'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link14',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link14'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image14',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image14'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link15',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link15'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image15',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image15'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link16',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link16'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image16',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image16'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link17',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link17'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image17',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image17'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link18',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link18'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image18',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image18'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link19',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link19'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image19',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image19'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link20',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link20'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image20',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image20'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link21',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link21'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image21',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image21'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link22',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link22'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image22',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image22'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link23',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link23'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image23',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image23'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link24',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link24'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image24',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image24'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link25',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link25'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image25',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image25'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link26',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link26'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image26',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image26'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link27',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link27'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image27',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image27'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link28',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link28'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image28',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image28'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link29',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link29'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image29',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image29'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Link30',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Link30'),\n      })\n      .registerSetting({\n        setting: 'wusong8899-client1-header-adv.Image30',\n        type: 'URL',\n        label: app.translator.trans('wusong8899-client1.admin.Image30'),\n      });\n});\n"], "sourceRoot": ""}