interface TronscanData {
    name(): string;
    valueUsd(): string;
    img(): string;
}

interface ButtonsCustomizationData {
    name(): string;
    icon(): string;
    color(): string;
    url(): string;
}

interface LinksQueueData {
    attribute(name: string): string;
}

interface ButtonsCustomizationMap {
    [key: number]: { url: string };
}

interface LeftValueMap {
    [key: number]: number;
}

// Add new interfaces for better type safety
interface SwiperConfig {
    loop: boolean;
    spaceBetween: number;
    slidesPerView: number | 'auto';
    autoplay?: {
        delay: number;
        disableOnInteraction: boolean;
    };
    effect?: string;
    centeredSlides?: boolean;
    coverflowEffect?: {
        rotate: number;
        depth: number;
        modifier: number;
        slideShadows: boolean;
        stretch: number;
    };
    pagination?: {
        el: string;
        type: string;
    };
    navigation?: {
        nextEl: string;
        prevEl: string;
    };
    modules: Array<any>;
}

interface ContainerConfig {
    className: string;
    id?: string;
    height?: string | number;
}

export type { 
    TronscanData, 
    ButtonsCustomizationData, 
    LinksQueueData, 
    ButtonsCustomizationMap, 
    LeftValueMap,
    SwiperConfig,
    ContainerConfig
};